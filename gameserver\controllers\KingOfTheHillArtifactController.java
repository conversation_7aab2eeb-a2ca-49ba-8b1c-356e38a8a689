/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.controllers.attack.AttackStatus;
import gameserver.model.alliance.PlayerAlliance;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.model.pvpevents.Battleground;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gnu.trove.TIntIntHashMap;
import gnu.trove.TIntIntIterator;

/**
 * <AUTHOR>
 */
public class KingOfTheHillArtifactController extends MonsterController {
    private boolean done = false;

    private int hitLimit;

    private Battleground bg;

    private TIntIntHashMap teamHits;
    private TIntIntHashMap playerHits;

    public KingOfTheHillArtifactController(Battleground bg, int hitLimit) {
        this.bg = bg;
        this.hitLimit = hitLimit;
    }

    @Override
    public void onRespawn() {
        this.teamHits = new TIntIntHashMap();
        this.playerHits = new TIntIntHashMap();
        this.done = false;
    }

    @Override
    public void onDie(Creature lastAttacker) {
        int teamIndex = -1;

        if (lastAttacker instanceof Player) {
            Player player = (Player) lastAttacker;
            if (player.isInAlliance())
                teamIndex = player.getPlayerAlliance().getBgIndex();
            else if (player.isInGroup())
                teamIndex = player.getPlayerGroup().getBgIndex();
        }

        artifactDie(teamIndex);
    }

    @Override
    public void onAttack(Creature creature, int skillId, TYPE type, int damage, int logId,
        AttackStatus status, boolean notifyAttackedObservers, boolean sendPacket) {
        damage = 0;

        super.onAttack(creature, skillId, type, damage, logId, status, notifyAttackedObservers,
            sendPacket);
        
        if (!notifyAttackedObservers)
            return;

        Creature master = creature;

        if (!(creature instanceof Player)) {
            master = creature.getMaster();
            if (master == null || !(master instanceof Player))
                return;
        }

        Player player = (Player) master;
        if (player.isInGroup()) {
            PlayerGroup group = player.getPlayerGroup();

            if (teamHits.containsKey(group.getBgIndex()))
                teamHits.put(group.getBgIndex(), teamHits.get(group.getBgIndex()) + 1);
            else
                teamHits.put(group.getBgIndex(), 1);
        }
        else if (player.isInAlliance()) {
            PlayerAlliance alliance = player.getPlayerAlliance();

            if (teamHits.containsKey(alliance.getBgIndex()))
                teamHits.put(alliance.getBgIndex(), teamHits.get(alliance.getBgIndex()) + 1);
            else
                teamHits.put(alliance.getBgIndex(), 1);
        }
        else {
            if (playerHits.containsKey(player.getObjectId()))
                playerHits.put(player.getObjectId(), playerHits.get(player.getObjectId()) + 1);
            else
                playerHits.put(player.getObjectId(), 1);
        }

        for (TIntIntIterator it = teamHits.iterator(); it.hasNext();) {
            it.advance();
            if (it.value() >= hitLimit) {
                artifactDie(it.key());
                break;
            }
        }
        for (TIntIntIterator it = playerHits.iterator(); it.hasNext();) {
            it.advance();
            if (it.value() >= hitLimit) {
                artifactDie(it.key());
                break;
            }
        }
    }

    private void artifactDie(int teamIndex) {
        if (!done) {
            done = true;
            bg.onArtifactDie(teamIndex);
        }
    }
}