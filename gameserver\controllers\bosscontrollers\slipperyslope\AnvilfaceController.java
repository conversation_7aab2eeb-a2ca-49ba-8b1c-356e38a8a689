/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.slipperyslope;

import gameserver.ai.state.AIState;
import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.utils.MathUtil;
import gameserver.world.World;

/**
 * <AUTHOR>
 */
public class AnvilfaceController extends BossController {
    private final BossSkill SLOW = new BossSkill(17492, 1);

    private int shoutTicker = 0;

    public AnvilfaceController() {
        super(215794, true);
    }

    @Override
    protected void think() {
        Npc owner = getOwner();
        SpawnTemplate spawn = owner.getSpawn();

        if (MathUtil.getDistance(owner, spawn.getX(), spawn.getY(), spawn.getZ()) > 20) {
            owner.getAi().clearDesires();
            owner.getAggroList().clearHate();

            owner.getMoveController().stop();
            World.getInstance().updatePosition(owner, spawn.getX(), spawn.getY(), spawn.getZ(),
                spawn.getHeading(), true);
            owner.getController().stopMoving();

            owner.setInCombat(false);

            owner.getAi().setAiState(AIState.ACTIVE);
            owner.getLifeStats().increaseHp(TYPE.NATURAL_HP, owner.getLifeStats().getMaxHp() / 6);

            return;
        }

        int hp = owner.getLifeStats().getHpPercentage();

        switch (shoutTicker) {
            case 0:
                if (hp <= 75) {
                    shoutTicker++;
                    owner.shout("Taste my HAMMER!");

                    if (owner.getWorldId() == 300510000) {
                        spawnAdd(700641, null, 981.5f, 1319.2f, 501f, false);
                        spawnAdd(700641, null, 982.1f, 1305.7f, 501f, false);
                        spawnAdd(700641, null, 981.9f, 1331.9f, 501f, false);
                        spawnAdd(700641, null, 966.0f, 1313.6f, 501f, false);
                        spawnAdd(700641, null, 966.0f, 1325.3f, 501f, false);
                        spawnAdd(700641, null, 965.7f, 1319.5f, 501f, false);
                        spawnAdd(700641, null, 995.4f, 1323.5f, 501f, false);
                        spawnAdd(700641, null, 994.9f, 1314.6f, 501f, false);
                    }
                    else {
                        spawnAdd(700641, null, 312.05f, 2661.19f, 424f, false);
                        spawnAdd(700641, null, 281.74f, 2648.83f, 423f, false);
                        spawnAdd(700641, null, 281.02f, 2671.89f, 423f, false);
                        spawnAdd(700641, null, 299.51f, 2689.32f, 423f, false);
                        spawnAdd(700641, null, 323.24f, 2690.49f, 423f, false);
                        spawnAdd(700641, null, 340.50f, 2672.30f, 423f, false);
                        spawnAdd(700641, null, 341.29f, 2648.53f, 423f, false);
                        spawnAdd(700641, null, 323.10f, 2631.09f, 423f, false);
                        spawnAdd(700641, null, 299.15f, 2630.40f, 423f, false);
                    }
                }
                break;
            case 1:
                if (hp <= 50) {
                    shoutTicker++;
                    owner.shout("You fight like a wuss!");

                    if (owner.getWorldId() == 300510000) {
                        spawnAdd(700641, null, 981.5f, 1319.2f, 501f, false);
                        spawnAdd(700641, null, 982.1f, 1305.7f, 501f, false);
                        spawnAdd(700641, null, 981.9f, 1331.9f, 501f, false);
                        spawnAdd(700641, null, 966.0f, 1313.6f, 501f, false);
                        spawnAdd(700641, null, 966.0f, 1325.3f, 501f, false);
                        spawnAdd(700641, null, 965.7f, 1319.5f, 501f, false);
                        spawnAdd(700641, null, 995.4f, 1323.5f, 501f, false);
                        spawnAdd(700641, null, 994.9f, 1314.6f, 501f, false);
                    }
                    else {
                        spawnAdd(700641, null, 312.05f, 2661.19f, 424f, false);
                        spawnAdd(700641, null, 281.74f, 2648.83f, 423f, false);
                        spawnAdd(700641, null, 281.02f, 2671.89f, 423f, false);
                        spawnAdd(700641, null, 299.51f, 2689.32f, 423f, false);
                        spawnAdd(700641, null, 323.24f, 2690.49f, 423f, false);
                        spawnAdd(700641, null, 340.50f, 2672.30f, 423f, false);
                        spawnAdd(700641, null, 341.29f, 2648.53f, 423f, false);
                        spawnAdd(700641, null, 323.10f, 2631.09f, 423f, false);
                        spawnAdd(700641, null, 299.15f, 2630.40f, 423f, false);
                    }
                }
                break;
            case 2:
                if (hp <= 25) {
                    shoutTicker++;
                    owner.shout("Deal with it!");

                    if (owner.getWorldId() == 300510000) {
                        spawnAdd(700641, null, 981.5f, 1319.2f, 501f, false);
                        spawnAdd(700641, null, 982.1f, 1305.7f, 501f, false);
                        spawnAdd(700641, null, 981.9f, 1331.9f, 501f, false);
                        spawnAdd(700641, null, 966.0f, 1313.6f, 501f, false);
                        spawnAdd(700641, null, 966.0f, 1325.3f, 501f, false);
                        spawnAdd(700641, null, 965.7f, 1319.5f, 501f, false);
                        spawnAdd(700641, null, 995.4f, 1323.5f, 501f, false);
                        spawnAdd(700641, null, 994.9f, 1314.6f, 501f, false);
                    }
                    else {
                        spawnAdd(700641, null, 312.05f, 2661.19f, 424f, false);
                        spawnAdd(700641, null, 281.74f, 2648.83f, 423f, false);
                        spawnAdd(700641, null, 281.02f, 2671.89f, 423f, false);
                        spawnAdd(700641, null, 299.51f, 2689.32f, 423f, false);
                        spawnAdd(700641, null, 323.24f, 2690.49f, 423f, false);
                        spawnAdd(700641, null, 340.50f, 2672.30f, 423f, false);
                        spawnAdd(700641, null, 341.29f, 2648.53f, 423f, false);
                        spawnAdd(700641, null, 323.10f, 2631.09f, 423f, false);
                        spawnAdd(700641, null, 299.15f, 2630.40f, 423f, false);
                    }
                }
                break;
        }

        Player priority = getPriorityTarget();
        if (priority == null || !MathUtil.isIn3dRange(owner, priority, 20))
            return;

        getOwner().getAggroList().addHate(priority, 10000);

        if (SLOW.timeSinceUse() > 25)
            queueSkill(SLOW, priority);
    }
}