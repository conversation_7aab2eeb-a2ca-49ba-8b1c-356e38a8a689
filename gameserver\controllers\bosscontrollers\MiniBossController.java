/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.PvpService;

import java.util.Arrays;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class MiniBossController extends BossController {
    private final BossSkill KNOCKBACK = new BossSkill(16858, 1);
    
    private long nextMove = 0;

    public MiniBossController() {
        super(Arrays.asList(234174, 234176, 234177, 234243, 234303, 234310), false);
    }

    protected void think() {
        if (getOwner().getAggroList().getMostHated() != null && getOwner().getTarget() != null) {
            getOwner().setNoHome(false);
            
            if (KNOCKBACK.timeSinceUse() > 13)
                queueSkill(KNOCKBACK, getOwner());
            
            return;
        }

        getOwner().setNoHome(true);

        if (nextMove == 0) {
            nextMove = System.currentTimeMillis() + Rnd.get(10000, 12000);
        }
        else if (System.currentTimeMillis() > nextMove) {
            nextMove = 0;

            randomWalk(3);
        }
    }
    
    @Override
    public void doReward() {
        super.doReward();
        
        Player killer = getOwner().getAggroList().getMostPlayerDamage();
        if (killer != null) {
            PvpService.getInstance().addMight(killer, 10);
        }
    }
}
