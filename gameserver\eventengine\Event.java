/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine;

import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;
import gameserver.world.World;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public abstract class Event implements Runnable {

    public static final int MAX_PRIORITY = 10;
    public static final int MIN_PRIORITY = 0;
    public static final int DEFAULT_PRIORITY = 5;

    private int priority = DEFAULT_PRIORITY;
    private boolean finished = false;

    public final void run() {
        execute();
    }

    abstract protected void execute();

    public final void reset() {
        finished = false;
        onReset();
    }

    abstract protected void onReset();

    protected void finish() {
        finished = true;
    }

    public abstract boolean cancel(boolean mayInterruptIfRunning);

    /**
     * Returns the number of milliseconds to wait after this event before a new one can start.
     * 
     * @return ms to wait
     */
    public int getCooldown() {
        return 30 * 1000;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        if (priority > MAX_PRIORITY)
            priority = MAX_PRIORITY;
        if (priority < MIN_PRIORITY)
            priority = MIN_PRIORITY;
        this.priority = priority;
    }

    public boolean isFinished() {
        return finished;
    }

    protected void announce(Player pl, String msg) {
        announce(pl, msg, 0);
    }

    protected void announce(Collection<Player> players, String msg) {
        for (Player pl : players)
            announce(pl, msg, 0);
    }

    protected void announce(final Player pl, final String msg, int delay) {
        if (delay > 0) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    PacketSendUtility.sendSys2Message(pl, "Event", msg);
                }
            }, delay);
        }
        else {
            PacketSendUtility.sendSys2Message(pl, "Event", msg);
        }
    }

    protected void announceAll(String msg) {
        announceAll(msg, 0);
    }

    protected void announceAll(final String msg, int delay) {
        if (delay > 0) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    World.getInstance().doOnAllPlayers(new Executor<Player>() {
                        @Override
                        public boolean run(Player pl) {
                            if (pl.getBattleground() == null)
                                PacketSendUtility.sendSys2Message(pl, "Event", msg);

                            return true;
                        }
                    });
                }
            }, delay);
        }
        else {
            World.getInstance().doOnAllPlayers(new Executor<Player>() {
                @Override
                public boolean run(Player pl) {
                    if (pl.getBattleground() == null)
                        PacketSendUtility.sendSys2Message(pl, "Event", msg);

                    return true;
                }
            });
        }
    }

}
