/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.eventengine.Event;

import org.apache.log4j.Logger;

/**
 * Simple event for testing purpose. Logs a message on execution.
 * 
 * <AUTHOR>
 * 
 */
public class TestEvent extends Event {
    private static final Logger log = Logger.getLogger(TestEvent.class);

    private final String message;

    public TestEvent(String message) {
        this.message = message;
    }

    /*
     * (non-Javadoc)
     * @see java.lang.Runnable#run()
     */
    @Override
    protected void execute() {
        log.info(message);
        finish();
    }

    @Override
    public int getCooldown() {
        return 2000;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#cancel(boolean)
     */
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return false;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#onReset()
     */
    @Override
    protected void onReset() {

    }
}
