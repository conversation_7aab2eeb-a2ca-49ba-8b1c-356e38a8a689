/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dataholders.loadingutils.adapters;

import gameserver.dataholders.DataManager;
import gameserver.model.templates.item.ItemTemplate;

import java.util.ArrayList;
import java.util.Arrays;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlIDREF;

/**
 * <AUTHOR>
 */
public class NpcEquipmentList {
    @XmlElement(name = "item")
    @XmlIDREF
    public ItemTemplate[] items;

    public static NpcEquipmentList createList(int... itemIds) {
        ArrayList<ItemTemplate> templates = new ArrayList<ItemTemplate>(itemIds.length);

        for (int itemId : itemIds) {
            ItemTemplate template = DataManager.ITEM_DATA.getItemTemplate(itemId);
            if (template != null)
                templates.add(template);
        }

        NpcEquipmentList list = new NpcEquipmentList();

        list.items = templates.toArray(new ItemTemplate[templates.size()]);

        return list;
    }
}