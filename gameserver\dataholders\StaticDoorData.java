/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dataholders;

import gameserver.model.templates.StaticDoorTemplate;
import gnu.trove.TIntObjectHashMap;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.xml.bind.Unmarshaller;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 */
@XmlRootElement(name = "staticdoors")
@XmlAccessorType(XmlAccessType.FIELD)
public class StaticDoorData {
    @XmlElement(name = "staticdoor")
    private List<StaticDoorTemplate> staticdoors;

    private TIntObjectHashMap<List<StaticDoorTemplate>> doorsByMapId = new TIntObjectHashMap<List<StaticDoorTemplate>>();

    void afterUnmarshal(Unmarshaller u, Object parent) {
        for (StaticDoorTemplate template : staticdoors) {
            List<StaticDoorTemplate> doors = doorsByMapId.get(template.getWorldId());

            if (doors == null) {
                doors = new ArrayList<StaticDoorTemplate>();
                doorsByMapId.put(template.getWorldId(), doors);
            }

            doors.add(template);
        }
    }

    public int size() {
        return staticdoors.size();
    }

    public List<StaticDoorTemplate> getStaticDoorList() {
        return staticdoors;
    }

    public List<StaticDoorTemplate> getDoorsByMapId(int worldId) {
        return doorsByMapId.containsKey(worldId) ? doorsByMapId.get(worldId)
            : Collections.<StaticDoorTemplate>emptyList();
    }
}
