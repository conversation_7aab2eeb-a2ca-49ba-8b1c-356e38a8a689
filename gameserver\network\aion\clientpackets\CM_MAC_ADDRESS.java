/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.network.aion.clientpackets;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.dao.DAOManager;

import gameserver.dao.BannedMacDAO;
import gameserver.network.aion.AionClientPacket;
import gameserver.network.aion.serverpackets.SM_QUIT_RESPONSE;
import gameserver.network.loginserver.LoginServer;

/**
 * In this packet client is sending Mac Address - haha.
 * 
 * <AUTHOR>
 */
public class CM_MAC_ADDRESS extends AionClientPacket {
    /**
     * Mac Addres send by client in the same format as: ipconfig /all [ie: xx-xx-xx-xx-xx-xx]
     */
    private String macAddress;

    private String hwId;

    /**
     * Constructs new instance of <tt>CM_MAC_ADDRESS </tt> packet
     * 
     * @param opcode
     */
    public CM_MAC_ADDRESS(int opcode) {
        super(opcode);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected void readImpl() {
        readC();

        int count = readH();
        for (int i = 0; i < count; i++)
            readD();

        macAddress = readS();
        hwId = readS();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected void runImpl() {
        // Logger.getLogger(getClass()).info("IP " + getConnection().getIP() + " authorized with MAC: " + macAddress);

        getConnection().setMacAddress(macAddress);

        if (DAOManager.getDAO(BannedMacDAO.class).isBanned(macAddress)) {
            if (getConnection().getAccount() != null) {
                LoginServer.getInstance().sendBanPacket((byte) 1, getConnection().getAccount().getId(), "", 14 * 24 * 60, 0,
                    "Automated 14-day due to banned MAC");
                
                getConnection().close(new SM_QUIT_RESPONSE(), true);

                Logger.getLogger(CM_ENTER_WORLD.class).info(
                    "[AUDIT] Kicked IP "
                        + getConnection().getIP()
                        + " (account: "
                        + (getConnection().getAccount() != null ? getConnection().getAccount()
                            .getName() : "not-authed-yet") + ") with banned MAC " + macAddress);
            }
        }

        // getConnection().setClientVersion(hwId);
    }
}
