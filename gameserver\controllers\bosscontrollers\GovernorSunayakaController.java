/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.world.World;

/**
 * <AUTHOR>
 * 
 */
public class GovernorSunayakaController extends BossController {
    private final BossSkill CAST_SPEED_INCR = new BossSkill(1923, 1);
    private final BossSkill UNSTABLE_EARTH = new BossSkill(19176, 1);
    private final BossSkill FIRE_COLUMN_II = new BossSkill(1515, 9);
    private final BossSkill EARTHQUAKE = new BossSkill(19189, 9);
    private final BossSkill FLAME_OF_TRIALS = new BossSkill(19065, 9);
    private final BossSkill METEOR_SHOWER = new BossSkill(18760, 1);
    private final BossSkill MISERABLE_STRUGGLE = new BossSkill(17056, 1);
    private final BossSkill RECKLESS_ABANDON = new BossSkill(18968, 1);
    private final BossSkill WAAAAAAAAH = new BossSkill(18974, 1);
    private final BossSkill DOOM_OF_THE_LIVING = new BossSkill(19096, 1);
    private final BossSkill VAIZELS_WISDOM = new BossSkill(1554, 1);
    private final BossSkill DRANA_BURST = new BossSkill(19208, 1);
    private final BossSkill PIERCING_CLAW = new BossSkill(18674, 9);
    private final BossSkill IRRESISTIBLE_CALL = new BossSkill(19207, 1);
    private final BossSkill AETHER_STORM = new BossSkill(19155, 125);
    private final BossSkill AETHER_WAVE = new BossSkill(17839, 1);
    private final BossSkill HEAD_WOUND = new BossSkill(18447, 1);

    private final int NOBLE_LAPILIMA = 216946;
    private final int FLASH_LAPILIMO = 281896;
    private final int COMBAT_DRAKIE = 213642;

    private Mode mode = Mode.WAITING;

    private int addWavesDone = 0;

    public GovernorSunayakaController() {
        super(218553, true);
    }

    protected void think() {
        Creature owner = getOwner();
        
        if (owner.getGameStats().getBaseStat(StatEnum.MAXHP) != 5000000) {
            owner.getGameStats().setStat(StatEnum.MAXHP, 5000000);
            super.onDespawn(true);
            super.onRespawn();
            World.getInstance().spawn(getOwner());
        }

        int hp = owner.getLifeStats().getHpPercentage();
        if (addWavesDone < 1 && hp <= 75) {
            spawnAdds(NOBLE_LAPILIMA, 6);
            spawnAdds(FLASH_LAPILIMO, 10);
            addWavesDone++;
        }
        if (addWavesDone < 2 && hp <= 65) {
            spawnAdds(COMBAT_DRAKIE, 1);
            addWavesDone++;
        }
        if (addWavesDone < 3 && hp <= 63) {
            spawnAdds(COMBAT_DRAKIE, 5);
            addWavesDone++;
        }
        if (addWavesDone < 4 && hp <= 62) {
            spawnAdds(COMBAT_DRAKIE, 10);
            addWavesDone++;
        }
        if (addWavesDone < 5 && hp <= 60) {
            spawnAdds(COMBAT_DRAKIE, 20);
            addWavesDone++;
        }
        if (addWavesDone < 6 && hp <= 50) {
            spawnAdds(NOBLE_LAPILIMA, 8);
            spawnAdds(FLASH_LAPILIMO, 16);
            addWavesDone++;
        }
        if (addWavesDone < 7 && hp <= 30) {
            spawnAdds(NOBLE_LAPILIMA, 4);
            spawnAdds(FLASH_LAPILIMO, 2);
            addWavesDone++;
        }
        if (addWavesDone < 8 && hp <= 25) {
            spawnAdds(NOBLE_LAPILIMA, 10);
            spawnAdds(FLASH_LAPILIMO, 25);
            addWavesDone++;
        }
        if (addWavesDone < 9 && hp <= 10) {
            spawnAdds(NOBLE_LAPILIMA, 10);
            spawnAdds(FLASH_LAPILIMO, 10);
            addWavesDone++;
        }
        if (addWavesDone < 10 && hp <= 3) {
            spawnAdds(COMBAT_DRAKIE, 10);
            spawnAdds(FLASH_LAPILIMO, 10);
            addWavesDone++;
        }

        switch (mode) {
            case WAITING:
                if (owner.getAggroList().getMostHated() != null)
                    mode = Mode.ACTIVE;
                break;

            case ACTIVE:
                if (owner.getLifeStats().getHpPercentage() < 50) {
                    mode = Mode.ENRAGED;
                    break;
                }

                if (EARTHQUAKE.timeSinceUse() > 60)
                    queueSkill(EARTHQUAKE, getRandomTarget());
                else if (PIERCING_CLAW.timeSinceUse() > 16)
                    queueSkill(PIERCING_CLAW, getMostHated());
                else if (AETHER_STORM.timeSinceUse() > 25) {
                    queueSkill(PIERCING_CLAW, getMostHated());
                    queueSkill(AETHER_STORM, getMostHated());
                }
                else if (AETHER_WAVE.timeSinceUse() > 300)
                    queueSkill(AETHER_WAVE, getPriorityTarget());
                else if (FLAME_OF_TRIALS.timeSinceUse() > 15) {
                    queueSkill(FLAME_OF_TRIALS, getPriorityTarget());
                    queueSkill(UNSTABLE_EARTH, getPriorityTarget());
                }
                else if (IRRESISTIBLE_CALL.timeSinceUse() > 40)
                    queueSkill(IRRESISTIBLE_CALL, getRandomTarget());
                else if (EARTHQUAKE.timeSinceUse() > 31 && CAST_SPEED_INCR.timeSinceUse() > 40) {
                    queueSkill(CAST_SPEED_INCR, getOwner());
                    queueSkill(EARTHQUAKE, getRandomTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getPriorityTarget());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getPriorityTarget());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getPriorityTarget());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getPriorityTarget());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getPriorityTarget());
                }
                break;

            case ENRAGED:
                if (owner.getLifeStats().getHpPercentage() <= 25) {
                    mode = Mode.FURIOUS;
                    break;
                }

                if (EARTHQUAKE.timeSinceUse() > 45)
                    queueSkill(EARTHQUAKE, getRandomTarget());
                else if (PIERCING_CLAW.timeSinceUse() > 20)
                    queueSkill(PIERCING_CLAW, getMostHated());
                else if (AETHER_STORM.timeSinceUse() > 30) {
                    queueSkill(PIERCING_CLAW, getMostHated());
                    queueSkill(AETHER_STORM, getMostHated());
                    queueSkill(AETHER_STORM, getMostHated());
                }
                else if (IRRESISTIBLE_CALL.timeSinceUse() > 60)
                    queueSkill(IRRESISTIBLE_CALL, getRandomTarget());
                else if (RECKLESS_ABANDON.timeSinceUse() > 120)
                    queueSkill(RECKLESS_ABANDON, getRandomTarget());
                else if (METEOR_SHOWER.timeSinceUse() > 35) {
                    getOwner().resetSkillCoolDowns();
                    queueSkill(VAIZELS_WISDOM, getOwner());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(VAIZELS_WISDOM, getOwner());
                    queueSkill(METEOR_SHOWER, getMostHated());
                    queueSkill(METEOR_SHOWER, getMostHated());
                    queueSkill(METEOR_SHOWER, getMostHated());
                }
                break;

            case FURIOUS:
                if (owner.getLifeStats().getHpPercentage() <= 5) {
                    mode = Mode.SURVIVAL;
                    break;
                }
                if (EARTHQUAKE.timeSinceUse() > 30)
                    queueSkill(EARTHQUAKE, getRandomTarget());
                else if (PIERCING_CLAW.timeSinceUse() > 25) {
                    queueSkill(PIERCING_CLAW, getRandomTarget());
                    queueSkill(PIERCING_CLAW, getMostHated());
                }
                else if (IRRESISTIBLE_CALL.timeSinceUse() > 60) {
                    queueSkill(IRRESISTIBLE_CALL, getRandomTarget());
                    queueSkill(HEAD_WOUND, getRandomTarget());
                }
                else if (AETHER_WAVE.timeSinceUse() > 300)
                    queueSkill(AETHER_WAVE, getPriorityTarget());
                else if (DRANA_BURST.timeSinceUse() > 60)
                    queueSkill(DRANA_BURST, getRandomTarget());
                else if (VAIZELS_WISDOM.timeSinceUse() > 25) {
                    getOwner().resetSkillCoolDowns();
                    queueSkill(VAIZELS_WISDOM, getOwner());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                }
                else if (UNSTABLE_EARTH.timeSinceUse() > 25) {
                    getOwner().resetSkillCoolDowns();
                    queueSkill(VAIZELS_WISDOM, getOwner());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(UNSTABLE_EARTH, getPriorityTarget());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getPriorityTarget());
                    queueSkill(FLAME_OF_TRIALS, getMostHated());
                }
                break;

            case SURVIVAL:
                if (owner.getLifeStats().getHpPercentage() > 2
                    && VAIZELS_WISDOM.timeSinceUse() > 18) {
                    getOwner().resetSkillCoolDowns();
                    queueSkill(VAIZELS_WISDOM, getOwner());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(MISERABLE_STRUGGLE, getPriorityTarget());
                    queueSkill(EARTHQUAKE, getRandomTarget());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(VAIZELS_WISDOM, getOwner());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(METEOR_SHOWER, getPriorityTarget());
                    queueSkill(FLAME_OF_TRIALS, getRandomTarget());
                }
                else if (IRRESISTIBLE_CALL.timeSinceUse() > 40)
                    queueSkill(IRRESISTIBLE_CALL, getRandomTarget());
                else if (owner.getLifeStats().getHpPercentage() <= 2
                    && CAST_SPEED_INCR.timeSinceUse() > 30) {
                    getOwner().resetSkillCoolDowns();
                    queueSkill(CAST_SPEED_INCR, getOwner());
                    queueSkill(EARTHQUAKE, getRandomTarget());
                    queueSkill(DRANA_BURST, getRandomTarget());
                    queueSkill(RECKLESS_ABANDON, getPriorityTarget());
                    queueSkill(MISERABLE_STRUGGLE, getPriorityTarget());
                    queueSkill(WAAAAAAAAH, getMostHated());
                    queueSkill(EARTHQUAKE, getRandomTarget());
                    queueSkill(METEOR_SHOWER, getMostHated());
                    queueSkill(METEOR_SHOWER, getMostHated());
                    queueSkill(METEOR_SHOWER, getMostHated());
                    queueSkill(DOOM_OF_THE_LIVING, getMostHated());
                    queueSkill(MISERABLE_STRUGGLE, getMostHated());
                    queueSkill(UNSTABLE_EARTH, getMostHated());
                    queueSkill(EARTHQUAKE, getRandomTarget());
                    queueSkill(FLAME_OF_TRIALS, getRandomTarget());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getMostHated());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getMostHated());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getMostHated());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getMostHated());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getMostHated());
                    getOwner().resetSkillCoolDowns();
                    queueSkill(FIRE_COLUMN_II, getMostHated());
                }
                break;
        }

    }

    enum Mode {
        WAITING,
        ACTIVE,
        ENRAGED,
        FURIOUS,
        SURVIVAL
    }
}
