/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.items;

import java.util.ArrayList;
import java.util.List;

/**
 * This enum is defining inventory slots, to which items can be equipped.
 * 
 * <AUTHOR>
 */
public enum ItemSlot {
    MAIN_HAND(1), // 1
    SUB_HAND(1 << 1, false, true), // 2
    HELMET(1 << 2), // 4
    TORSO(1 << 3), // 8
    GLOVES(1 << 4), // 16
    BOOTS(1 << 5), // 32
    EARRINGS_LEFT(1 << 6, false, true), // 64
    EARRINGS_RIGHT(1 << 7), // 128
    RING_LEFT(1 << 8, false, true), // 256
    RING_RIGHT(1 << 9), // 512
    NECKLACE(1 << 10), // 1024
    SHOULDER(1 << 11), // 2048
    PANTS(1 << 12), // 4096
    POWER_SHARD_RIGHT(1 << 13), // 8192
    POWER_SHARD_LEFT(1 << 14), // 16384
    WINGS(1 << 15), // 32768
    // non-NPC equips (slot > Short.MAX)
    WAIST(1 << 16), // 65536
    MAIN_OFF_HAND(1 << 17), // 131072
    SUB_OFF_HAND(1 << 18), // 262144

    // combo
    MAIN_OR_SUB(MAIN_HAND.slotIdMask | SUB_HAND.slotIdMask, true), // 3
    EARRING_RIGHT_OR_LEFT(EARRINGS_LEFT.slotIdMask | EARRINGS_RIGHT.slotIdMask, true), // 192
    RING_RIGHT_OR_LEFT(RING_LEFT.slotIdMask | RING_RIGHT.slotIdMask, true), // 768
    SHARD_RIGHT_OR_LEFT(POWER_SHARD_LEFT.slotIdMask | POWER_SHARD_RIGHT.slotIdMask, true), // 24576
    TORSO_GLOVE_FOOT_SHOULDER_LEG(0, true), // TODO

    FEATHER(1 << 19), // 524288 -- FEATHER!

    // STIGMA slots
    /*
     * STIGMA1(1 << 19), // 524288 STIGMA2(1 << 20), // 1048576 STIGMA3(1 << 21), // 2097152 STIGMA4(1 << 22), //
     * 4194304 STIGMA5(1 << 23), // 8388608 STIGMA6(1 << 24), // 16777216 NONE(1 << 25), // Unknown // 33554432
     * ADV_STIGMA1(1 << 26), // 67108864 ADV_STIGMA2(1 << 27), // 134217728 ADV_STIGMA3(1 << 28), // 268435456
     * ADV_STIGMA4(1 << 29), // 536870912 ADV_STIGMA5(1 << 30), // 1073741824 ADV_STIGMA6(1 << 31); // -2147483648
     */

    STIGMA1(1 << 30), // 1073741824
    STIGMA2(1L << 31), // 2147483648
    STIGMA3(1L << 32), // 4294967296
    STIGMA4(1L << 33), // 8589934592
    STIGMA5(1L << 34), // 17179869184
    STIGMA6(1L << 35), // 34359738368

    NONE(1L << 36), // ??!?!?

    ADV_STIGMA1(1L << 47), // 140737488355328
    ADV_STIGMA2(1L << 48), // 281474976710656
    ADV_STIGMA3(1L << 49), // 562949953421312
    ADV_STIGMA4(1L << 50), // 1125899906842624
    ADV_STIGMA5(1L << 51), // 2251799813685248
    ADV_STIGMA6(1L << 52); // 4503599627370496

    private long slotIdMask;
    private boolean combo;
    private boolean left;

    private ItemSlot(long mask) {
        this(mask, false);
    }

    private ItemSlot(long mask, boolean combo) {
        this(mask, combo, false);
    }
    
    private ItemSlot(long mask, boolean combo, boolean left) {
        this.slotIdMask = mask;
        this.combo = combo;
        this.left = left;
    }

    public long getSlotIdMask() {
        return slotIdMask;
    }

    /**
     * @return the combo
     */
    public boolean isCombo() {
        return combo;
    }
    
    public boolean isLeft() {
        return left;
    }

    public static List<ItemSlot> getSlotsFor(long slotIdMask) {
        ArrayList<ItemSlot> slots = new ArrayList<ItemSlot>();
        for (ItemSlot itemSlot : values()) {
            long sumMask = itemSlot.slotIdMask & slotIdMask;

            /**
             * possible values in this check - one of combo slots (MAIN, RIGHT_RING etc)
             */
            if (sumMask > 0 && sumMask <= slotIdMask && !itemSlot.isCombo())
                slots.add(itemSlot);
        }

        if (slots.size() == 0)
            throw new IllegalArgumentException("Invalid provided slotIdMask " + slotIdMask);

        return slots;
    }
}