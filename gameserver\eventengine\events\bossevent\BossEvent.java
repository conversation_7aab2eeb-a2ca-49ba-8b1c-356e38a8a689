/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events.bossevent;

import gameserver.controllers.attack.AggroInfo;
import gameserver.controllers.attack.AggroList;
import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.ActionObserver.ObserverType;
import gameserver.eventengine.Event;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.alliance.PlayerAlliance;
import gameserver.model.alliance.PlayerAllianceMember;
import gameserver.model.bossevent.EventBoss;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.model.templates.bossevent.BossTemplate;
import gameserver.model.templates.bossevent.Position3D;
import gameserver.services.PvpService;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;
import gameserver.world.World;

import java.awt.Point;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

import org.apache.log4j.Logger;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class BossEvent extends Event {
    private static final Logger log = Logger.getLogger(BossEvent.class);

    private final BossTemplate bossTemplate;
    private final int mapId;
    private final Collection<Position3D> spawnpoints;
    private final Set<PointAlgorithm> algorithms;

    private int mightPerPlayer = 10;
    private int participationReward = 20;

    private boolean running;
    private List<AionObject> spawnedObjects = new ArrayList<AionObject>();

    public BossEvent(BossTemplate bosstemplate, int mapId, Collection<Position3D> spawnpoints,
        Set<PointAlgorithm> algorithms) {

        this.bossTemplate = bosstemplate;
        this.mapId = mapId;
        this.spawnpoints = spawnpoints;
        this.algorithms = algorithms;
    }

    public int getMapId() {
        return mapId;
    }

    public Collection<Position3D> getSpawnpoints() {
        return spawnpoints;
    }

    public Collection<PointAlgorithm> getAlgorithms() {
        return algorithms;
    }

    public int getParticipationReward() {
        return participationReward;
    }

    public void setParticipationReward(int reward) {
        participationReward = reward;
    }

    public int getRewardPerPlayer() {
        return mightPerPlayer;
    }

    public void setRewardPerPlayer(int reward) {
        participationReward = reward;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#execute()
     */
    @Override
    protected void execute() {
        running = true;

        // Point cluster = getAlgorithm().getBiggestCluster(mapId);
        Position3D position = new ArrayList<Position3D>(spawnpoints)
            .get(Rnd.get(spawnpoints.size())); // getClosestSpawn(cluster);

        spawnBoss(position);

        announce(position);
    }

    private void spawnBoss(Position3D position) {
        final EventBoss boss = SpawnEngine.getInstance().spawnEventBoss(mapId,
            bossTemplate.getNpcId(), position.getX(), position.getY(), position.getZ(), (byte) 0);
        if (boss == null)
            finish();

        spawnedObjects.add(boss);

        final String msg = "The mighty foe has been defeated! For now, all is safe again.";
        boss.getObserveController().attach(new ActionObserver(ObserverType.DEATH) {
            @Override
            public void died(Creature creature) {
                World.getInstance().doOnAllPlayers(new Executor<Player>() {
                    @Override
                    public boolean run(Player pl) {
                        PacketSendUtility.sendSys2Message(pl, "Event", msg);
                        return true;
                    }
                });

                EventBoss boss = null;
                for (AionObject obj : spawnedObjects) {
                    if (obj instanceof EventBoss) {
                        boss = (EventBoss) obj;
                        break;
                    }
                }

                if (boss == null) {
                    finish();
                    return;
                }

                AggroList aggrolist = boss.getAggroList();
                float totalDamage = aggrolist.getTotalDamage();

                Player playerWithMostDamage = aggrolist.getMostPlayerDamage();

                Collection<AggroInfo> aggroinfos = aggrolist.getFinalDamageList(true);
                int participants = aggrolist.getFinalDamageList(false).size();

                for (AggroInfo info : aggroinfos) {
                    AionObject attacker = info.getAttacker();
                    float damage = info.getDamage();

                    int mightReward = participationReward
                        + Math.round((damage / totalDamage) * mightPerPlayer * participants);

                    if (attacker instanceof Player) {
                        Player player = (Player) attacker;
                        PvpService.getInstance().addMight(player, mightReward);
                    }

                    if (attacker instanceof PlayerGroup) {
                        PlayerGroup group = (PlayerGroup) attacker;
                        int count = group.size();
                        for (Player player : group.getMembers()) {
                            PvpService.getInstance().addMight(player,
                                Math.round(mightReward / count));
                        }
                    }

                    if (attacker instanceof PlayerAlliance) {
                        PlayerAlliance alliance = (PlayerAlliance) attacker;
                        int count = alliance.size();
                        for (PlayerAllianceMember member : alliance.getMembers()) {
                            PvpService.getInstance().addMight(member.getPlayer(),
                                Math.round(mightReward) / count);
                        }
                    }
                }

                PacketSendUtility.sendMessage(playerWithMostDamage,
                    "For doing the most damage to the boss, you receive an additional reward!");
                PvpService.getInstance().addMight(playerWithMostDamage, 50);

                finish();
            }
        });

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                EventBoss boss = null;
                for (AionObject obj : spawnedObjects) {
                    if (obj instanceof EventBoss) {
                        boss = (EventBoss) obj;
                        break;
                    }
                }

                if (boss == null || boss.getAggroList().getTotalDamage() == 0)
                    finish();
            }
        }, 10 * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (isFinished())
                    return;

                EventBoss boss = null;
                for (AionObject obj : spawnedObjects) {
                    if (obj instanceof EventBoss) {
                        boss = (EventBoss) obj;
                        break;
                    }
                }

                if (boss == null || boss.getLifeStats().getHpPercentage() > 25)
                    finish();
            }
        }, 25 * 60 * 1000);
    }

    private void despawnAll() {
        for (AionObject obj : spawnedObjects) {
            if (obj instanceof Creature) {
                Creature creature = (Creature) obj;
                if (creature.isSpawned())
                    World.getInstance().despawn(creature);
            }
            if (obj instanceof VisibleObject) {
                ((VisibleObject) obj).getController().delete();
            }
        }

        spawnedObjects.clear();
    }

    private void announce(final Position3D position) {
        World.getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player player) {
                PacketSendUtility.sendSys2Message(player, "Event",
                    "A mighty foe has appeared from the depths of Atreia!");
                PacketSendUtility.sendMessage(
                    player,
                    "Hunt it down at [pos:this;" + String.valueOf(mapId) + " "
                        + String.valueOf(position.getX()) + " " + String.valueOf(position.getY())
                        + " 0.0 0] location!");
                return true;
            }
        });
    }

    private Position3D getClosestSpawn(Point point) {
        Position3D closestSpawn = new Position3D();
        double distToSpawn = 9999999;

        for (Position3D spawnPoint : spawnpoints) {
            double pseudoDist = (point.getX() - (int) spawnPoint.getX())
                * (point.getX() - (int) spawnPoint.getX())
                + (point.getY() - (int) spawnPoint.getY())
                * (point.getY() - (int) spawnPoint.getY());
            if (pseudoDist < distToSpawn) {
                closestSpawn = spawnPoint;
                distToSpawn = pseudoDist;
            }
        }

        return calculateZ(closestSpawn);
    }

    private PointAlgorithm getAlgorithm() {
        PointAlgorithm mostEfficient = null;
        for (PointAlgorithm algorithm : algorithms) {
            if (mostEfficient == null) {
                mostEfficient = algorithm;
                continue;
            }
            if (algorithm.getEfficiency(mapId) > mostEfficient.getEfficiency(mapId)) {
                mostEfficient = algorithm;
                continue;
            }
        }
        return mostEfficient;
    }

    private Position3D calculateZ(final Position3D position) {
        Position3D newPosition = new Position3D();
        float z = GeoEngine2.getInstance().getZ(mapId, position.getX(), position.getY(),
            position.getZ());

        newPosition.setX(position.getX());
        newPosition.setY(position.getY());
        newPosition.setZ(z);

        return newPosition;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#onReset()
     */
    @Override
    protected void onReset() {

    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#cancel(boolean)
     */
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        if (!isFinished() && running) {
            finish();
            return true;
        }
        return false;
    }

    @Override
    protected void finish() {
        running = false;
        despawnAll();
        super.finish();
    }
}
