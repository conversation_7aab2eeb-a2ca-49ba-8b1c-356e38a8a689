package gameserver.model.gameobjects.stats.modifiers;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEnum;

public class CheckWeapon {
    public static final CheckWeapon getInstance() {
        return SingletonHolder.instance;
    }

    public int getValue(Player player) {
        if (player == null || player.getEquipment() == null)
            return 0;
        
        if (player.getEquipment().getMainHandWeaponType() != null) {
            return player.getGameStats().getBaseStat(StatEnum.ATTACK_SPEED) / 2 - 10;
        }

        return 600;
    }

    @SuppressWarnings("synthetic-access")
    private static class SingletonHolder {
        protected static final CheckWeapon instance = new CheckWeapon();
    }
}
