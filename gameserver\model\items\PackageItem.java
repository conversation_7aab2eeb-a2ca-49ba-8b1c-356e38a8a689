/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.items;

import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.PersistentState;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.id.IdianStatEffectId;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.templates.PolishGroup;
import gameserver.model.templates.PolishTemplate;
import gameserver.model.templates.item.ItemTemplate;
import gameserver.services.ItemService;

import java.util.TreeSet;

import org.apache.log4j.Logger;

/**
 * <AUTHOR> ggadv2
 */
public class PackageItem {

    private int itemId;
    private int quantity;

    public PackageItem(int itemId, int quantity) {
        this.itemId = itemId;
        this.quantity = quantity;
    }
    
    public int getItemId() {
        return itemId;
    }
    
    public int getQuantity() {
        return quantity;
    }
}