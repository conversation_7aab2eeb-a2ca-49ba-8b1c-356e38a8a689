/*
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.configs.administration;

import com.aionemu.commons.configuration.Property;

/**
 * <AUTHOR>
 */
public class AdminConfig {

    @Property(key = "gameserver.administration.command.enemy", defaultValue = "2")
    public static int COMMAND_ENEMY;

    @Property(key = "gameserver.administration.command.who", defaultValue = "2")
    public static int COMMAND_WHO;

    @Property(key = "administration.command.passkeyreset", defaultValue = "2")
    public static int COMMAND_PASSKEY_RESET;

    @Property(key = "gameserver.administration.gmlevel", defaultValue = "2")
    public static int GM_LEVEL;

    @Property(key = "gameserver.administration.flight.freefly", defaultValue = "2")
    public static int GM_FLIGHT_FREE;

    @Property(key = "gameserver.administration.flight.unlimited", defaultValue = "2")
    public static int GM_FLIGHT_UNLIMITED;

    @Property(key = "gameserver.administration.shield.vulnerable", defaultValue = "0")
    public static int GM_SHIELD_VULNERABLE;

    @Property(key = "gameserver.administration.command.add", defaultValue = "2")
    public static int COMMAND_ADD;

    @Property(key = "gameserver.administration.command.ai", defaultValue = "2")
    public static int COMMAND_AI;

    @Property(key = "gameserver.administration.command.addtitle", defaultValue = "2")
    public static int COMMAND_ADDTITLE;

    @Property(key = "gameserver.administration.command.addset", defaultValue = "2")
    public static int COMMAND_ADDSET;

    @Property(key = "gameserver.administration.command.adddrop", defaultValue = "2")
    public static int COMMAND_ADDDROP;

    @Property(key = "gameserver.administration.command.advsendfakeserverpacket", defaultValue = "2")
    public static int COMMAND_ADVSENDFAKESERVERPACKET;

    @Property(key = "gameserver.administration.command.announce", defaultValue = "2")
    public static int COMMAND_ANNOUNCE;

    @Property(key = "gameserver.administration.command.announce_faction", defaultValue = "2")
    public static int COMMAND_ANNOUNCE_FACTION;

    @Property(key = "gameserver.administration.command.announcements", defaultValue = "2")
    public static int COMMAND_ANNOUNCEMENTS;

    @Property(key = "gameserver.administration.command.ban", defaultValue = "2")
    public static int COMMAND_BAN;

    @Property(key = "gameserver.administration.command.bet", defaultValue = "2")
    public static int COMMAND_BET;

    @Property(key = "gameserver.administration.command.bk", defaultValue = "2")
    public static int COMMAND_BK;

    @Property(key = "gameserver.administration.command.configure", defaultValue = "2")
    public static int COMMAND_CONFIGURE;

    @Property(key = "gameserver.administration.command.deletespawn", defaultValue = "2")
    public static int COMMAND_DELETESPAWN;

    @Property(key = "gameserver.administration.command.dye", defaultValue = "2")
    public static int COMMAND_DYE;

    @Property(key = "gameserver.administration.command.gag", defaultValue = "2")
    public static int COMMAND_GAG;

    @Property(key = "gameserver.administration.command.goto", defaultValue = "2")
    public static int COMMAND_GOTO;

    @Property(key = "gameserver.administration.command.givemissingskills", defaultValue = "2")
    public static int COMMAND_GIVEMISSINGSKILLS;

    @Property(key = "gameserver.administration.command.heal", defaultValue = "2")
    public static int COMMAND_HEAL;

    @Property(key = "gameserver.administration.command.info", defaultValue = "2")
    public static int COMMAND_INFO;

    @Property(key = "gameserver.administration.command.invis", defaultValue = "2")
    public static int COMMAND_INVIS;

    @Property(key = "gameserver.administration.command.invul", defaultValue = "2")
    public static int COMMAND_INVUL;

    @Property(key = "gameserver.administration.command.kick", defaultValue = "2")
    public static int COMMAND_KICK;

    @Property(key = "gameserver.administration.command.kill", defaultValue = "2")
    public static int COMMAND_KILL;

    @Property(key = "gameserver.administration.command.kinah", defaultValue = "2")
    public static int COMMAND_KINAH;

    @Property(key = "gameserver.administration.command.legion", defaultValue = "2")
    public static int COMMAND_LEGION;

    @Property(key = "gameserver.administration.command.morph", defaultValue = "2")
    public static int COMMAND_MORPH;

    @Property(key = "gameserver.administration.command.moveplayertoplayer", defaultValue = "2")
    public static int COMMAND_MOVEPLAYERTOPLAYER;

    @Property(key = "gameserver.administration.command.moveto", defaultValue = "2")
    public static int COMMAND_MOVETO;

    @Property(key = "gameserver.administration.command.movetonpc", defaultValue = "2")
    public static int COMMAND_MOVETONPC;

    @Property(key = "gameserver.administration.command.movetoplayer", defaultValue = "2")
    public static int COMMAND_MOVETOPLAYER;

    @Property(key = "gameserver.administration.command.movetome", defaultValue = "2")
    public static int COMMAND_MOVETOME;

    @Property(key = "gameserver.administration.command.notice", defaultValue = "2")
    public static int COMMAND_NOTICE;

    @Property(key = "gameserver.administration.command.petition", defaultValue = "2")
    public static int COMMAND_PETITION;

    @Property(key = "gameserver.administration.command.playerinfo", defaultValue = "2")
    public static int COMMAND_PLAYERINFO;

    @Property(key = "gameserver.administration.command.prison", defaultValue = "2")
    public static int COMMAND_PRISON;

    @Property(key = "gameserver.administration.command.admin", defaultValue = "1")
    public static int COMMAND_ADMIN;

    @Property(key = "gameserver.administration.command.promote", defaultValue = "2")
    public static int COMMAND_PROMOTE;

    @Property(key = "gameserver.administration.command.questcommand", defaultValue = "2")
    public static int COMMAND_QUESTCOMMAND;

    @Property(key = "gameserver.administration.command.questcommandplayers", defaultValue = "2")
    public static int COMMAND_QUESTCOMMANDPLAYERS;

    @Property(key = "gameserver.administration.command.reload", defaultValue = "2")
    public static int COMMAND_RELOAD;

    @Property(key = "gameserver.administration.command.reloadspawns", defaultValue = "2")
    public static int COMMAND_RELOADSPAWNS;

    @Property(key = "gameserver.administration.command.remove", defaultValue = "2")
    public static int COMMAND_REMOVE;

    @Property(key = "gameserver.administration.command.resurrect", defaultValue = "2")
    public static int COMMAND_RESURRECT;

    @Property(key = "gameserver.administration.command.revoke", defaultValue = "2")
    public static int COMMAND_REVOKE;

    @Property(key = "gameserver.administration.command.savespawndata", defaultValue = "2")
    public static int COMMAND_SAVESPAWNDATA;

    @Property(key = "gameserver.administration.command.seedroplist", defaultValue = "2")
    public static int COMMAND_SEEDROPLIST;

    @Property(key = "gameserver.administration.command.sendfakeserverpacket", defaultValue = "2")
    public static int COMMAND_SENDFAKESERVERPACKET;

    @Property(key = "gameserver.administration.command.sendrawpacket", defaultValue = "2")
    public static int COMMAND_SENDRAWPACKET;

    @Property(key = "gameserver.administration.command.setap", defaultValue = "2")
    public static int COMMAND_SETAP;

    @Property(key = "gameserver.administration.command.setclass", defaultValue = "2")
    public static int COMMAND_SETCLASS;

    @Property(key = "gameserver.administration.command.setexp", defaultValue = "2")
    public static int COMMAND_SETEXP;

    @Property(key = "gameserver.administration.command.setlevel", defaultValue = "2")
    public static int COMMAND_SETLEVEL;

    @Property(key = "gameserver.administration.command.settitle", defaultValue = "2")
    public static int COMMAND_SETTITLE;

    @Property(key = "gameserver.administration.command.setmight", defaultValue = "2")
    public static int COMMAND_SETMIGHT;

    @Property(key = "gameserver.administration.command.siege", defaultValue = "2")
    public static int COMMAND_SIEGE;

    @Property(key = "gameserver.administration.command.spawn", defaultValue = "2")
    public static int COMMAND_SPAWN;

    @Property(key = "gameserver.administration.command.speed", defaultValue = "2")
    public static int COMMAND_SPEED;

    @Property(key = "gameserver.administration.command.speed.maxvalue", defaultValue = "500")
    public static int COMMAND_SPEED_MAXVALUE;

    @Property(key = "gameserver.administration.command.unloadspawn", defaultValue = "2")
    public static int COMMAND_UNLOADSPAWN;

    @Property(key = "gameserver.administration.command.addskill", defaultValue = "2")
    public static int COMMAND_ADDSKILL;

    @Property(key = "gameserver.administration.command.delskill", defaultValue = "2")
    public static int COMMAND_DELSKILL;

    @Property(key = "gameserver.administration.command.system", defaultValue = "2")
    public static int COMMAND_SYSTEM;

    @Property(key = "gameserver.administration.command.unstuck", defaultValue = "2")
    public static int COMMAND_UNSTUCK;

    @Property(key = "gameserver.administration.command.weather", defaultValue = "2")
    public static int COMMAND_WEATHER;

    @Property(key = "gameserver.administration.command.zone", defaultValue = "2")
    public static int COMMAND_ZONE;

    @Property(key = "gameserver.administration.command.enchant", defaultValue = "2")
    public static int COMMAND_ENCHANT;

    @Property(key = "gameserver.administration.command.socket", defaultValue = "2")
    public static int COMMAND_SOCKET;

    @Property(key = "gameserver.administration.command.powerup", defaultValue = "2")
    public static int COMMAND_POWERUP;

    @Property(key = "gameserver.administration.command.godstone", defaultValue = "2")
    public static int COMMAND_GODSTONE;

    @Property(key = "gameserver.administration.command.worldban", defaultValue = "2")
    public static int COMMAND_WORLDBAN;

    @Property(key = "gameserver.administration.command.stat", defaultValue = "2")
    public static int COMMAND_STAT;

    @Property(key = "gameserver.administration.search.listall", defaultValue = "2")
    public static int SEARCH_LIST_ALL;

    @Property(key = "gameserver.administration.command.neutral", defaultValue = "2")
    public static int COMMAND_NEUTRAL;

    @Property(key = "gameserver.administration.command.movie", defaultValue = "2")
    public static int COMMAND_MOVIE;

    @Property(key = "gameserver.administration.command.dispel", defaultValue = "2")
    public static int COMMAND_DISPEL;

    @Property(key = "gameserver.administration.command.recall", defaultValue = "2")
    public static int COMMAND_RECALL;

    @Property(key = "gameserver.administration.command.silence", defaultValue = "2")
    public static int COMMAND_SILENCE;

    @Property(key = "gameserver.administration.command.ring", defaultValue = "2")
    public static int COMMAND_RING;

    @Property(key = "gameserver.administration.command.dredgion", defaultValue = "2")
    public static int COMMAND_DREDGION;

    @Property(key = "gameserver.administration.command.say", defaultValue = "2")
    public static int COMMAND_SAY;

    @Property(key = "gameserver.administration.command.online", defaultValue = "2")
    public static int COMMAND_ONLINE;

    @Property(key = "gameserver.administration.command.fixz", defaultValue = "2")
    public static int COMMAND_FIXZ;

    @Property(key = "gameserver.administration.command.fixh", defaultValue = "2")
    public static int COMMAND_FIXH;

    @Property(key = "gameserver.administration.command.rename", defaultValue = "2")
    public static int COMMAND_RENAME;

    @Property(key = "gameserver.administration.command.html", defaultValue = "2")
    public static int COMMAND_HTML;

    @Property(key = "gameserver.administration.command.gmlist", defaultValue = "0")
    public static int COMMAND_GMLIST;

    @Property(key = "gameserver.administration.command.appearance", defaultValue = "2")
    public static int COMMAND_APPEARANCE;

    @Property(key = "gameserver.administration.command.see", defaultValue = "2")
    public static int COMMAND_SEE;

    @Property(key = "gameserver.administration.instancenogroup", defaultValue = "2")
    public static int INSTANCE_NO_GROUP;

    @Property(key = "gameserver.administration.command.survey", defaultValue = "2")
    public static int COMMAND_SURVEY;

    @Property(key = "gameserver.administration.command.addeffect", defaultValue = "2")
    public static int COMMAND_ADDEFFECT;

    @Property(key = "gameserver.administration.command.ticket", defaultValue = "1")
    public static int COMMAND_TICKET;
    
    @Property(key = "gameserver.administration.command.lounge", defaultValue = "1")
    public static int COMMAND_LOUNGE;
    
    @Property(key = "gameserver.administration.command.base", defaultValue = "1")
    public static int COMMAND_BASE;
    
    @Property(key = "gameserver.administration.command.lounge", defaultValue = "1")
    public static int COMMAND_OUTLAW;
}
