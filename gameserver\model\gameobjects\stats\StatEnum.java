/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is private software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.stats;

import gameserver.model.items.ItemSlot;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
@XmlType(name = "StatEnum")
@XmlEnum
public enum StatEnum {
    // None = 0,
    // FireResistance = 15,
    // HP = 18,
    // MP = 20,
    // FlightTime = 23,
    // Attack = 25,
    // PhysicalDefense = 26,
    // MagicalAttack = 27,
    // MagicalRes = 28,
    // AttackSpeed = 29, //%
    // Accuracy = 30,
    // Evasion = 31,
    // Parry = 32,
    // Block = 33,
    // PhysicalCrit = 34,
    // Speed = 36, // %
    // FlightSpeed = 37, // %
    // MagicalCrit = 40,
    // Concentration = 41,
    // MagicPower = 104,
    // MagicalAccuracy = 105,
    // Knowledge = 106,
    // Agility = 107,
    // Hate = 109
    MAXDP(22, "maxdp"),
    MAXHP(18, "maxhp"),
    MAXMP(20, "maxmp"),

    AGILITY(9, "agility", true),
    BLOCK(33, "block"),
    EVASION(31, "dodge"),
    CONCENTRATION(41, "concentration"),
    WILL(11, "will", true),
    HEALTH(7, "health", true),
    ACCURACY(8, "accuracy", true),
    KNOWLEDGE(10, "knowledge", true),
    PARRY(32, "parry"),
    POWER(6, "strength", true),
    SPEED(36, "speed", true),
    WALK(35, "walk", true),
    HIT_COUNT(35, "hitcount", true),

    ATTACK_RANGE(38, "attackrange", true),
    ATTACK_SPEED(29, "attackdelay", -1, true),
    PHYSICAL_ATTACK(25, "phyattack"),
    PHYSICAL_ACCURACY(30, "hitaccuracy"),
    PHYSICAL_CRITICAL(34, "critical"),
    PHYSICAL_DEFENSE(26, "physicaldefend"),
    MAIN_HAND_HITS(0, "mainhandhits"),
    MAIN_HAND_ACCURACY(0, "mainhandaccuracy"),
    MAIN_HAND_CRITICAL(0, "mainhandcritical"),
    MAIN_HAND_POWER(0, "mainhandpower"),
    MAIN_HAND_ATTACK_SPEED(0, "mainhandattackspeed"),
    OFF_HAND_HITS(0, "offhandhits"),
    OFF_HAND_ACCURACY(0, "offhandaccuracy"),
    OFF_HAND_CRITICAL(0, "offhandcritical"),
    OFF_HAND_POWER(0, "offhandpower"),
    OFF_HAND_ATTACK_SPEED(0, "offhandattackspeed"),

    MAGICAL_ATTACK(27, "magicalattack"),
    MAGICAL_ACCURACY(105, "magicalhitaccuracy"),
    MAGICAL_CRITICAL(40, "magicalcritical"),
    MAGICAL_RESIST(28, "magicalresist"),
    MAX_DAMAGES(0, "maxdamages"),
    MIN_DAMAGES(0, "mindamages"),
    IS_MAGICAL_ATTACK(0, "ismagicalattack", true),
    
    MAIN_HAND_MAGICAL_ATTACK(0, "mainhandmagicalattack"),
    OFF_HAND_MAGICAL_ATTACK(0, "offhandmagicalattack"),

    EARTH_RESISTANCE(14, "elementaldefendearth"),
    FIRE_RESISTANCE(15, "elementaldefendfire"),
    WIND_RESISTANCE(13, "elementaldefendair"),
    WATER_RESISTANCE(12, "elementaldefendwater"),

    BOOST_MAGICAL_SKILL(104, "magicalskillboost"),
    BOOST_CASTING_TIME(108, "boostcastingtime"),
    BOOST_SKILL_CASTING_TIME(0, "boostskillcastingtime"),
    BOOST_HATE(109, "boosthate"),
    BOOST_HEAL(110, "boostheal"),
    
    BOOST_CHARGE_TIME(0, "boostchargetime"),

    FLY_TIME(23, "maxfp"),
    FLY_SPEED(37, "flyspeed"),

    PVP_ATTACK_RATIO(106, "pvpattackratio"),
    PVP_DEFEND_RATIO(107, "pvpdefendratio"),
    
    PVP_ATTACK_RATIO_PHYSICAL(111, "pvpattackratio_physical"),
    PVP_ATTACK_RATIO_MAGICAL(113, "pvpattackratio_magical"),
    PVP_DEFEND_RATIO_PHYSICAL(112, "pvpdefendratio_physical"),
    PVP_DEFEND_RATIO_MAGICAL(114, "pvpdefendratio_magical"),
    
    PVE_ATTACK_RATIO(0, "pveattackratio"),
    PVE_DEFEND_RATIO(0, "pvedefendratio"),

    DAMAGE_REDUCE(0, "damagereduce"),

    BLEED_RESISTANCE(44, "arbleed"),
    BLIND_RESISTANCE(48, "arblind"),
    CHARM_RESISTANCE(49, "archarm"),
    CONFUSE_RESISTANCE(54, "arconfuse"),
    CURSE_RESISTANCE(53, "arcurse"),
    DISEASE_RESISTANCE(50, "ardisease"),
    FEAR_RESISTANCE(52, "arfear"),
    OPENAREIAL_RESISTANCE(59, "aropenareial"),
    PARALYZE_RESISTANCE(45, "arparalyze"),
    PERIFICATION_RESISTANCE(56, "arperification"),
    POISON_RESISTANCE(43, "arpoison"),
    ROOT_RESISTANCE(47, "arroot"),
    SILENCE_RESISTANCE(51, "arsilence"),
    SLEEP_RESISTANCE(46, "arsleep"),
    SLOW_RESISTANCE(60, "arslow"),
    SNARE_RESISTANCE(61, "arsnare"),
    SPIN_RESISTANCE(62, "arspin"),
    STAGGER_RESISTANCE(58, "arstagger"),
    STUMBLE_RESISTANCE(57, "arstumble"),
    STUN_RESISTANCE(55, "arstun"),
    TRANSFORM_RESISTANCE(0, "artransform"),
    PULL_RESISTANCE(0, "arpulled"),
    BIND_RESISTANCE(0, "arbind"),

    REGEN_MP(21, "mpregen"),
    REGEN_HP(19, "hpregen"),
    REGEN_FP(24, "fpregen"),

    /**
     * New/Strange
     */
    STAGGER_BOOST(84, "stagger_arp"),
    STUMBLE_BOOST(83, "stumble_arp"),
    STUN_BOOST(81, "stun_arp"),
    HEAL_BOOST(110, "healskillboost"),
    ALLRESIST(2, "allresist"),
    STUNLIKE_RESISTANCE(0, "arstunlike"),
    ELEMENTAL_RESISTANCE_DARK(17, "elemental_resistance_dark"),
    ELEMENTAL_RESISTANCE_LIGHT(16, "elemental_resistance_light"),
    MAGICAL_CRITICAL_RESIST(116, "magicalcriticalresist"),
    MAGICAL_CRITICAL_DAMAGE_REDUCE(118, "magicalcriticaldamagereduce"),
    PHYSICAL_CRITICAL_RESIST(115, "physicalcriticalresist"),
    PHYSICAL_CRITICAL_DAMAGE_REDUCE(117, "physicalcriticalreducerate"),
    ERFIRE(0, "erfire"),
    ERAIR(0, "erair"),
    EREARTH(0, "erearth"),
    ERWATER(0, "erwater"),
    ABNORMAL_RESISTANCE_ALL(1, "abnormal_resistance_all"),
    // MAGICAL_DEFEND(0, "magical_defend"),
    ALLPARA(0, "allpara"),
    KNOWIL(4, "knowil"),
    AGIDEX(5, "agidex"),
    STRVIT(3, "strvit"),
    
    /**
     * 3.0
     */
    MAGICAL_DEFENSE(125, "magical_defend"),
    BOOST_MAGICAL_SKILL_RESIST(126, "boostmagicalskillresist"),
    PARALYZE_BOOST(71, "paralyze_arp"),
    SILENCE_BOOST(77, "silence_arp"),
    POISON_BOOST(69, "poison_arp"),
    FEAR_BOOST(78, "fear_arp"),
    BLEED_BOOST(70, "bleed_arp"),
    CHARM_BOOST(75, "charm_arp"),
    OPENAREIAL_BOOST(85, "openareial_arp"),
    SLEEP_BOOST(72, "sleep_arp"),
    ROOT_BOOST(73, "root_arp"),
    BLIND_BOOST(74, "blind_arp"),
    DISEASE_BOOST(76, "disease_arp"),
    SLOW_BOOST(86, "slow_arp"),
    CURSE_BOOST(79, "curse_arp"),
    CONFUSE_BOOST(80, "confuse_arp"),
    PETRIFICATION_BOOST(82, "petrification_arp"),
    SNARE_BOOST(87, "snare_arp"),
    SPIN_BOOST(88, "spin_arp"),
    
    PROC_RESIST(0, "procreducerate"),
    
    BLOCK_BOOST(0, "block_penetration"),

    /**
     * Experience Boost
     */
    BOOST_HUNTING_XP_RATE(0, "boost_hunting_xp_rate"),
    BOOST_GROUP_HUNTING_XP_RATE(0, "boost_group_hunting_xp_rate"),
    BOOST_QUEST_XP_RATE(0, "boost_quest_xp_rate"),
    BOOST_CRAFTING_XP_RATE(0, "boost_crafting_xp_rate"),
    BOOST_GATHERING_XP_RATE(0, "boost_gathering_xp_rate"),

    /**
     * custom
     */
    MAIN_MAX_DAMAGES(0, "mainmaxdamages"),
    MAIN_MIN_DAMAGES(0, "mainmindamages"),
    OFF_MAX_DAMAGES(0, "offmaxdamages"),
    OFF_MIN_DAMAGES(0, "offmindamages"),
    
    MAIN_PVP_ATTACK_RATIO(0, "mainpvpatkratio"),
    MAIN_PVP_ATTACK_RATIO_MAGICAL(0, "mainpvpatkratiomagical"),
    MAIN_PVP_ATTACK_RATIO_PHYSICAL(0, "mainpvpatkratiophysical"),
    OFF_PVP_ATTACK_RATIO(0, "offpvpatkratio"),
    OFF_PVP_ATTACK_RATIO_MAGICAL(0, "offpvpatkratiomagical"),
    OFF_PVP_ATTACK_RATIO_PHYSICAL(0, "offpvpatkratiophysical"),
    
    PVP_EVASION(0, "pvpdodge"),
    PVP_BLOCK(0, "pvpblock"),
    PVP_PARRY(0, "pvpparry"),
    PVP_PHYSICAL_ACCURACY(0, "pvphitaccuracy"),
    PVP_MAGICAL_ACCRUACY(0, "pvpmagicalhitaccuracy"),
    PVP_MAGICAL_RESIST(0, "pvpmagicalresist");

    private String name;
    private boolean replace;
    private int sign;

    private int itemStoneMask;

    private StatEnum(int stoneMask, String name) {
        this(stoneMask, name, 1, false);
    }

    private StatEnum(int stoneMask, String name, boolean replace) {
        this(stoneMask, name, 1, replace);
    }

    private StatEnum(int stoneMask, String name, int sign) {
        this(stoneMask, name, sign, false);
    }

    private StatEnum(int stoneMask, String name, int sign, boolean replace) {
        this.itemStoneMask = stoneMask;
        this.name = name;
        this.replace = replace;
        this.sign = sign;
    }

    public String getName() {
        return name;
    }

    public int getSign() {
        return sign;
    }

    /**
     * @return the itemStoneMask
     */
    public int getItemStoneMask() {
        return itemStoneMask;
    }

    public StatEnum getMainOrSubHandStat(ItemSlot slot) {
        if (slot == null)
            return this;
        switch (this) {
            case PHYSICAL_ATTACK:
            case POWER:
                switch (slot) {
                    case SUB_HAND:
                        return OFF_HAND_POWER;
                    case MAIN_HAND:
                        return MAIN_HAND_POWER;
                }
            case PHYSICAL_ACCURACY:
                switch (slot) {
                    case SUB_HAND:
                        return OFF_HAND_ACCURACY;
                    case MAIN_HAND:
                        return MAIN_HAND_ACCURACY;
                }
            case PHYSICAL_CRITICAL:
                switch (slot) {
                    case SUB_HAND:
                        return OFF_HAND_CRITICAL;
                    case MAIN_HAND:
                        return MAIN_HAND_CRITICAL;
                }
            case HIT_COUNT:
                switch (slot) {
                    case SUB_HAND:
                        return OFF_HAND_HITS;
                    case MAIN_HAND:
                        return MAIN_HAND_HITS;
                }
            case ATTACK_SPEED:
                switch (slot) {
                    case SUB_HAND:
                        return OFF_HAND_ATTACK_SPEED;
                    case MAIN_HAND:
                        return MAIN_HAND_ATTACK_SPEED;
                    default:
                        return ATTACK_SPEED;
                }
            case MIN_DAMAGES:
                switch (slot) {
                    case SUB_HAND:
                        return OFF_MIN_DAMAGES;
                    case MAIN_HAND:
                        return MAIN_MIN_DAMAGES;
                }
            case MAX_DAMAGES:
                switch (slot) {
                    case SUB_HAND:
                        return OFF_MAX_DAMAGES;
                    case MAIN_HAND:
                        return MAIN_MAX_DAMAGES;
                }
            case MAGICAL_ATTACK:
                switch (slot) {
                    case SUB_HAND:
                        return OFF_HAND_MAGICAL_ATTACK;
                    case MAIN_HAND:
                        return MAIN_HAND_MAGICAL_ATTACK;
                }
            default:
                return this;
        }
    }

    public boolean isMainOrSubHandStat() {
        switch (this) {
            case PHYSICAL_ATTACK:
            case POWER:
            case PHYSICAL_ACCURACY:
            case PHYSICAL_CRITICAL:
            case MAGICAL_ATTACK:
                return true;

            default:
                return false;
        }
    }

    public boolean isReplace() {
        return replace;
    }
}
