/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import java.util.Arrays;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class RandomWalkMobController extends BossController {
    private long nextMove = 0;

    public RandomWalkMobController() {
        super(Arrays.asList(230496, 231250, 231371, 231369, 230542, 230548, 231288, 230571, 231292,
            230587, 230585, 231277, 213219, 213092, 213104, 213221, 213227, 213223, 213229, 213231,
            211539, 211523, 212500, 215522, 215520, 215521, 234159, 234168, 234169, 234170, 234171,
            234172, 234173, 234182, 234222, 234223, 234226, 234227, 234228, 234233, 234244, 234250,
            234255, 234260, 234262, 234279, 234281, 234291, 234293, 234308, 234707, 235895, 235960,
            235898, 235875, 235888, 235890, 235896, 235912, 235919, 219765, 235906, 235907, 235900,
            235902, 235918, 235916, 235915, 219750, 219995, 219757, 219758, 219752, 219751, 219996,
            219768, 219770, 219765, 219776, 219747, 219744, 219742, 219994), false);
    }

    protected void think() {
        if (getOwner().getAggroList().getMostHated() != null && getOwner().getTarget() != null) {
            getOwner().setNoHome(false);
            return;
        }

        getOwner().setNoHome(true);

        if (nextMove == 0) {
            nextMove = System.currentTimeMillis() + Rnd.get(6000, 8000);
        }
        else if (System.currentTimeMillis() > nextMove) {
            nextMove = 0;

            randomWalk(3);
        }
    }
}
