/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Gatherable;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.skillengine.effect.EffectTemplate;
import gameserver.skillengine.effect.PulledEffect;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.ThreadPoolManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class TwoTeamResourceBg extends Battleground {
    private int extraCounter = 0;
    private Map<Integer, Integer> teamPoints = new ConcurrentHashMap<Integer, Integer>();
    private List<Gatherable> resources = new ArrayList<Gatherable>();
    private List<SpawnPosition> resourcePositions;
    private ReentrantLock pointsLock = new ReentrantLock();

    public TwoTeamResourceBg() {
        super.name = "2-Team War on Resources";
        super.description = "You are on a team and must gather as many resources as possible (Ore). Hinder the enemy as much as possible, but remember also to gather. You will respawn at your base every 30 seconds.";
        super.minSize = 3;
        super.maxSize = 6;
        super.teamCount = 2;
        super.matchLength = 300;

        BattlegroundMap map1 = new BattlegroundMap(220070000);
        map1.addSpawn(new SpawnPosition(1145.0f, 1604.0f, 51.0f));
        map1.addSpawn(new SpawnPosition(998.0f, 1388.0f, 58.0f));
        map1.setKillZ(40f);

        BattlegroundMap map2 = new BattlegroundMap(300160000);
        map2.addSpawn(461f, 1372f, 190f);
        map2.addSpawn(518f, 1512f, 189f);
        map2.setKillZ(180f);

        super.maps.add(map1);
        super.maps.add(map2);
    }

    private void createResourcePositionList() {
        resourcePositions = new ArrayList<SpawnPosition>();

        if (getMapId() == 220070000) {
            resourcePositions.add(new SpawnPosition(1048f, 1434f, 50f));
            resourcePositions.add(new SpawnPosition(1023f, 1465f, 51f));
            resourcePositions.add(new SpawnPosition(1052f, 1487f, 52f));
            resourcePositions.add(new SpawnPosition(1018f, 1488f, 49f));
            resourcePositions.add(new SpawnPosition(1062f, 1514f, 53f));
            resourcePositions.add(new SpawnPosition(1034f, 1538f, 49f));
            resourcePositions.add(new SpawnPosition(1052f, 1566f, 51f));
            resourcePositions.add(new SpawnPosition(1102f, 1544f, 50f));
        }
        else if (getMapId() == 300160000) {
            resourcePositions.add(new SpawnPosition(475f, 1470f, 190.4f));
            resourcePositions.add(new SpawnPosition(484f, 1470f, 190.4f));
            resourcePositions.add(new SpawnPosition(494f, 1470f, 190.4f));
            resourcePositions.add(new SpawnPosition(505f, 1469f, 190.4f));
            resourcePositions.add(new SpawnPosition(513f, 1469f, 190.4f));
            resourcePositions.add(new SpawnPosition(515f, 1450f, 190.4f));
            resourcePositions.add(new SpawnPosition(505f, 1450f, 190.4f));
            resourcePositions.add(new SpawnPosition(494f, 1450f, 190.4f));
            resourcePositions.add(new SpawnPosition(485f, 1451f, 190.4f));
            resourcePositions.add(new SpawnPosition(475f, 1451f, 190.4f));
            resourcePositions.add(new SpawnPosition(475f, 1429f, 190.4f));
            resourcePositions.add(new SpawnPosition(485f, 1429f, 190.4f));
            resourcePositions.add(new SpawnPosition(495f, 1429f, 190.4f));
            resourcePositions.add(new SpawnPosition(505f, 1429f, 190.4f));
            resourcePositions.add(new SpawnPosition(513f, 1428f, 190.4f));
            resourcePositions.add(new SpawnPosition(475f, 1409f, 188.1f));
            resourcePositions.add(new SpawnPosition(485f, 1409f, 188.1f));
            resourcePositions.add(new SpawnPosition(495f, 1409f, 188.1f));
            resourcePositions.add(new SpawnPosition(505f, 1409f, 188.1f));
            resourcePositions.add(new SpawnPosition(513f, 1410f, 188.1f));
        }
    }

    @Override
    public boolean isEffectAllowed(EffectTemplate et) {
        if (et instanceof PulledEffect)
            return false;
        return true;
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 30000);

                    if (!pl.getSkillList().isSkillPresent(30001))
                        pl.getSkillList().addSkill(pl, 30001, 15, true);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 30000);

        startBattleground(30000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups(0) <= 1)
                    endTwoTeamResourceMatch();
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamResourceMatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    @Override
                    public void run() {
                        extraCounter++;

                        if ((extraCounter % 2) == 0)
                            spawnGatherNode();

                        if ((extraCounter % 3) == 0)
                            showTeamPositions();

                        if ((extraCounter % 6) == 0) {
                            extraCounter = 0;
                            autoResurrection();
                        }
                    }
                }, 5 * 1000, 5 * 1000));
            }
        }, 30 * 1000);
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    @Override
    public void onResourceGathered(Gatherable resource, int teamIndex) {
        resources.remove(resource);

        if (super.isDone())
            return;

        if (super.getGroups().get(teamIndex) != null) {
            for (Player pl : super.getGroups().get(teamIndex).getMembers())
                PvpService.getInstance().addMight(pl, 2);
        }

        addPoints(super.getGroups().get(teamIndex), 3);

        super.announceAll(LadderService.getInstance().getNameByIndex(teamIndex)
            + " has gathered an ore!");
    }

    private void addPoints(PlayerGroup group, int points) {
        if (group == null)
            return;

        pointsLock.lock();

        try {
            Integer result = teamPoints.get(group.getBgIndex());
            if (result != null)
                teamPoints.put(group.getBgIndex(), result + points);
            else
                teamPoints.put(group.getBgIndex(), points);
        }
        finally {
            pointsLock.unlock();
        }
    }

    private void spawnGatherNode() {
        if (resourcePositions == null || resourcePositions.size() < 1)
            createResourcePositionList();

        SpawnPosition pos = resourcePositions.get(Rnd.get(resourcePositions.size()));
        if (pos == null)
            return;

        Gatherable resource = SpawnEngine.getInstance().spawnResource(this, getMapId(), 400201,
            pos.getX(), pos.getY(), pos.getZ(), (byte) 0);
        if (resource != null)
            resources.add(resource);
    }

    private void showTeamPositions() {
        String msg = "";
        msg += String.format("Blue: %d - Green: %d", teamPoints.containsKey(0) ? teamPoints.get(0)
            : 0, teamPoints.containsKey(1) ? teamPoints.get(1) : 0);

        for (PlayerGroup group : super.getGroups())
            for (Player pl : group.getMembers())
                super.scheduleAnnouncement(pl, msg, 0);

        super.specAnnounce(msg);
    }

    private void autoResurrection() {
        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead()) {
                    pl.getReviveController().fullRevive();
                    super.spawnProtection(pl);
                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    TeleportService.teleportTo(pl, getMapId(), super.getInstanceId(), pos.getX(),
                        pos.getY(), pos.getZ(), 0);
                    super
                        .scheduleAnnouncement(pl, "You have been automatically resurrected!", 1500);
                }
            }
        }
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super.scheduleAnnouncement(player, "You will automatically resurrect every 30 seconds.", 0);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;

            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in TwoTeamResourceBg!");
            }
            else {
                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 1);

                addPoints(killer.getPlayerGroup(), 1);

                super.announceAll(LadderService.getInstance().getNameByIndex(
                    killer.getPlayerGroup().getBgIndex())
                    + " has slain an enemy!");
            }
        }
    }

    private int getTeamPoints(int teamIndex) {
        if (!teamPoints.containsKey((Integer) teamIndex))
            return 0;

        return teamPoints.get((Integer) teamIndex);
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups(0) <= 1)
            endTwoTeamResourceMatch();
    }

    private void endTwoTeamResourceMatch() {
        super.onEndFirstDefault();

        PlayerGroup winner = null;
        boolean isDraw = false;
        if (getTeamPoints(0) == getTeamPoints(1))
            isDraw = true;
        else if (getTeamPoints(0) > getTeamPoints(1))
            winner = super.getGroups().get(0);
        else if (getTeamPoints(1) > getTeamPoints(0))
            winner = super.getGroups().get(1);

        if (isDraw) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.scheduleAnnouncement(pl, "The battleground has ended in a draw with "
                        + getTeamPoints(group.getBgIndex()) + " points!", 0);
                    super.scheduleAnnouncement(pl,
                        "For your effort you have been rewarded with some might.", 3000);
                    super.rewardPlayer(pl, 10, false);
                }
            }
            super.specAnnounce("The battleground has ended in a draw!");
        }
        else if (winner != null) {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2 : 1;

                    if (group.getObjectId() == winner.getObjectId()) {
                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE / 2);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.scheduleAnnouncement(pl, "Your team has won the match with "
                            + teamPoints.get(group.getBgIndex()) + " points!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                        super.rewardPlayer(pl, 20, true);
                    }
                    else {
                        super.playerLoseMatch(pl, -super.K_VALUE / 3);

                        super.scheduleAnnouncement(
                            pl,
                            "Your team has lost the match with "
                                + getTeamPoints(group.getBgIndex()) + " to "
                                + getTeamPoints(winner.getBgIndex()) + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);
                        super.rewardPlayer(pl, 10, false);
                    }
                }
            }

            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match!");
        }
        else {
            log.error("No winner or draw in a TwoTeamResourceBg!??!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}