package gameserver.itemengine.actions;

import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.ItemChargingService;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ConditionAction")
public class ConditionAction extends AbstractItemAction {
    @XmlAttribute
    protected int level;
    @XmlAttribute
    protected boolean deep;

    @Override
    public boolean canAct(Player player, Item parentItem, Item targetItem) {
        return true;
    }

    @Override
    public void act(Player player, Item parentItem, Item targetItem) {
        Item item = player.getInventory().getItemByObjId(parentItem.getObjectId());

        if (item != null) {
            player.getInventory().decreaseItemCount(item, 1);

            if (deep) {
                ItemChargingService.chargeAllEquippedItems(player, level);
            }
            else {
                ItemChargingService.chargeItem(player, targetItem, level);
            }
        }
    }
}
