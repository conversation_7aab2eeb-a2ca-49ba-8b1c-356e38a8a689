/*
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.ai.desires.impl;

import gameserver.ai.AI;
import gameserver.ai.desires.AbstractDesire;
import gameserver.ai.state.AIState;
import gameserver.controllers.attack.AttackResult;
import gameserver.controllers.attack.AttackStatus;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.siege.ArtifactProtector;
import gameserver.model.siege.FortressGeneral;
import gameserver.network.aion.serverpackets.SM_ATTACK;
import gameserver.network.aion.serverpackets.SM_LEVEL_UPDATE;
import gameserver.skillengine.SkillEngine;
import gameserver.skillengine.model.Skill;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;

import java.util.Collections;

/**
 * <AUTHOR>
 */
public final class AggressionDesire extends AbstractDesire {
    private Npc npc;
    private boolean shouldCancel;
    private int counter;

    public AggressionDesire(Npc npc, int desirePower) {
        super(desirePower);
        this.npc = npc;
    }

    @Override
    public boolean handleDesire(AI<?> ai) {
        if (npc == null)
            return false;

        shouldCancel = false;
        counter = 0;

        npc.getKnownList().doOnAllObjects(new Executor<AionObject>() {
            @Override
            public boolean run(AionObject visibleObject) {
                if (visibleObject == null)
                    return true;

                if (visibleObject instanceof Creature) {
                    final Creature creature = (Creature) visibleObject;

                    if (creature.getLifeStats() == null || creature.getLifeStats().isAlreadyDead())
                        return true;

                    if (!npc.canSee(creature))
                        return true;

                    if (!npc.isAggressiveTo(creature))
                        return true;

                    if (creature.getAdminNeutral() == 1 || creature.getAdminNeutral() == 3)
                        return true;
                    
                    if (!npc.isEnemy(creature))
                        return true;

                    /*
                     * if (creature instanceof Npc && ((Npc) creature).getObjectTemplate().getNpcType().getId() ==
                     * NpcType.NON_ATTACKABLE.getId()) return true;
                     */

                    if (creature instanceof Player
                        && ((Player) creature).isInState(CreatureState.FLIGHT_TELEPORT))
                        return true;

                    // Hack for FortressGenerals aggro
                    if (npc instanceof FortressGeneral || npc instanceof ArtifactProtector) {
                        if (creature instanceof Player) {
                            Player p = (Player) creature;
                            if (p.getCommonData().getRace() == npc.getObjectTemplate().getRace())
                                return true;
                        }
                    }

                    if (MathUtil.isIn3dRange(npc, creature, npc.getAggroRange())) {
                        counter++;
                        
                        if (!GeoEngine2.getInstance().canSee(npc, creature))
                            return true;

                        if (npc.hasWalkRoutes()) {
                            npc.getMoveController().stop();
                            npc.getController().stopMoving();
                        }

                        if (npc instanceof FortressGeneral) {
                            FortressGeneral deity = (FortressGeneral) npc;
                            
                            if (deity.getNextPull() < System.currentTimeMillis()
                                && !deity.isCasting()) {
                                int pullskill = 17195; // wide area pull
                                Skill caster = SkillEngine.getInstance().getSkill(npc, pullskill,
                                    1, creature);
                                caster.useSkill();
                                deity.setNextPull();
                            }
                        }

                        npc.getAi().setAiState(AIState.NONE); // TODO: proper aggro emotion on aggro range enter
                        /*
                         * PacketSendUtility.broadcastPacket( npc, new SM_ATTACK(npc, creature, 0, 633, 0, Collections
                         * .singletonList(new AttackResult(0, AttackStatus.NORMALHIT, 0, 0,
                         * npc.getAttackType().isMagical() ? DamageType.MAGICAL : DamageType.PHYSICAL))));
                         */

                        PacketSendUtility.broadcastPacket(
                            npc,
                            /*new SM_ATTACK(npc, creature, 0, 633, 0, Collections
                                .singletonList(new AttackResult(0, AttackStatus.NORMALHIT)))*/
                            new SM_LEVEL_UPDATE(npc.getObjectId(),
                                5, npc.getLevel()));

                        ThreadPoolManager.getInstance().schedule(new Runnable() {
                            @Override
                            public void run() {
                                npc.getAggroList().addHate(creature, 1);
                            }
                        }, 200);

                        shouldCancel = true;

                        return false;
                    }
                }
                return true;
            }
        }, true);

        if (shouldCancel || counter == 0)
            return false;

        return true;
    }

    @Override
    public int getExecutionInterval() {
        return 1;
    }

    @Override
    public void onClear() {
        // TODO Auto-generated method stub

    }
}
