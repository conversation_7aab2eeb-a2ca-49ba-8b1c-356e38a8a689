/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dataholders;

import gameserver.configs.main.CustomConfig;
import gameserver.configs.main.GSConfig;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.skillengine.change.Change;
import gameserver.skillengine.effect.AlwaysResistEffect;
import gameserver.skillengine.effect.DispelDebuffEffect;
import gameserver.skillengine.effect.EffectTemplate;
import gameserver.skillengine.effect.StaggerEffect;
import gameserver.skillengine.effect.StatupEffect;
import gameserver.skillengine.effect.StunEffect;
import gameserver.skillengine.effect.SummonSkillAreaEffect;
import gameserver.skillengine.effect.SummonTotemEffect;
import gameserver.skillengine.effect.WeaponStatupEffect;
import gameserver.skillengine.model.ActivationAttribute;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.skillengine.properties.Property;
import gameserver.skillengine.properties.TargetStatusProperty;
import gnu.trove.TIntObjectHashMap;
import gnu.trove.TIntObjectIterator;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.xml.bind.Unmarshaller;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 */
@XmlRootElement(name = "skill_data")
@XmlAccessorType(XmlAccessType.FIELD)
public class SkillData {
    @XmlElement(name = "skill_template")
    private List<SkillTemplate> skillTemplates;
    /**
     * Map that contains skillId - SkillTemplate key-value pair
     */
    private TIntObjectHashMap<SkillTemplate> skillData = new TIntObjectHashMap<SkillTemplate>();

    void afterUnmarshal(Unmarshaller u, Object parent) {
        skillData.clear();
        for (SkillTemplate skillTemplate : skillTemplates) {
            // Stack Modification
            if (skillTemplate.getStack().startsWith("SKILL_"))
                skillTemplate.setStack(skillTemplate.getStack().substring(6));
            else if (skillTemplate.getStack().startsWith("SKILLN_"))
                skillTemplate.setStack(skillTemplate.getStack().substring(7));

            // Limited Ranger Buff
            String stack = skillTemplate.getStack();

            if (stack.equals("RA_STIGMA_TIGEREYE") || stack.equals("SC_TRUESHOTMIND")
                || stack.equals("RA_MINDSEYE") || stack.equals("RA_EAGLEEYE")
                || stack.equals("RA_HAWKEYE") || stack.equals("RA_SNAKEEYE"))
                skillTemplate.setLimitedRangerBuff(true);

            // Dispel Buff Debuff
            if (stack.equals("EL_MANAREVERSE") // Magic Implosion
                || stack.equals("EL_ENFEEBLEMENT") // Shackle of Vulnerability
                || stack.equals("EL_HELLPAIN") // Infernal Pain
                || stack.equals("MA_GRAVITYCAGE") // Erosion
                || stack.equals("EL_AREACAGE") // Sandblaster
                || stack.equals("EL_EARTHGRAB") // Chain of Earth
                || stack.equals("EL_GAIAGRAB") // Blade of Earth
                || stack.equals("EL_NIGHTMAREROOT") // Nightmare
                || stack.equals("EL_BIND") // Body Root
                || stack.equals("EL_MAGICALBREAKDOWN") // Magic's Freedom
                || stack.equals("EL_SLOW") // Root of Enervation
                || stack.equals("EL_ENERVATIONCURSE") // Withering Gloom
                || stack.equals("MA_ROOT") // Root
                || stack.equals("EL_DISPELEXPLOSION") // Aegis Breaker
                || stack.equals("EL_STIGMA_STORMBLADE") // Cyclone of Wrath
                || stack.equals("EL_SILENCE") // Sigil of Silence
                || stack.equals("NPC_SKILLN_EL_ORDER_DECAYING_FIRE") // Spirit Erosion Fire
                || stack.equals("NPC_SKILLN_EL_ORDER_DECAYING_EARTH") // Spirit Erosion Earth
                || stack.equals("NPC_SKILLN_EL_ORDER_DECAYING_AIR") // Spirit Erosion Wind
                || stack.equals("NPC_SKILLN_EL_ORDER_DECAYING_WATER") // Spirit Erosion Water
                || stack.equals("NPC_SKILLN_EL_ORDER_DECAYING_TEMPEST") // Spirit Erosion Tempest
                || stack.equals("NPC_SKILLN_EL_ORDER_DECAYING_MAGMA") // Spirit Erosion Magma
            )
                skillTemplate.setDispelBuffDebuff(true);

            // Curse of Fire/Water
            if (stack.equals("EL_FIREFEAR") || stack.equals("EL_WATERFEAR"))
                skillTemplate.setSpecialFear(true);

            skillTemplate.setEffectPower(initializePower(skillTemplate));

            // General skill fixes
            if (stack.equals("GU_MENTALICSHOT")) {
                for (Iterator<EffectTemplate> it = skillTemplate.getEffects().getEffects()
                    .iterator(); it.hasNext();) {
                    EffectTemplate ms = it.next();

                    if (ms.getEffectId() == 131313)
                        it.remove();
                }
            }

            // Fix skill definition bugs
            if (stack.equals("WI_STONEBARRIER")) {
                EffectTemplate sb2 = skillTemplate.getEffectTemplate(2);
                sb2.setDuration(6000);
            }
            else if (stack.equals("EL_ENFEEBLEMENT")) { // Shackle of Vulnerability
                EffectTemplate e2 = skillTemplate.getEffectTemplate(2);
                e2.setDuration(30000);
            }
            else if (stack.equals("EL_FIREFEAR") || stack.equals("EL_WATERFEAR")
                || stack.equals("EL_DESPAIRCURSE")) { // Curse of Fire/Water, Flames of Anguish
                skillTemplate.setPvpDuration(60);
            }
            else if (stack.equals("PR_PURIFY")) { // Cleanse
                EffectTemplate p1 = skillTemplate.getEffectTemplate(1);

                if (p1 instanceof DispelDebuffEffect) {
                    ((DispelDebuffEffect) p1).setCount(3);
                }
            }
            else if (stack.equals("WI_DELAYEDSTRIKE")) { // Volcanic Heat
                EffectTemplate ds1 = skillTemplate.getEffectTemplate(1);

                if (ds1 instanceof SummonSkillAreaEffect) {
                    ((SummonSkillAreaEffect) ds1).setTime(10);
                }
            }
            else if (stack.equals("WI_MAGICALFLAME")) { // Balaur Seeker
                EffectTemplate mf1 = skillTemplate.getEffectTemplate(1);

                mf1.setSubEffect(null);
            }
            else if (stack.equals("RI_PROTECTIONCURTAIN")) { // Protective Shell
                for (Iterator<Property> it = skillTemplate.getSetproperties().getProperties()
                    .iterator(); it.hasNext();) {
                    Property prop = it.next();

                    if (prop instanceof TargetStatusProperty)
                        it.remove();
                }
            }
            else if (stack.equals("BA_SONGOFAPPRECIATION")) { // Hymn of Thanksgiving
                EffectTemplate sof3 = skillTemplate.getEffectTemplate(3);

                sof3.setDuration(6000);
            }
            else if (stack.equals("FI_WARFLAG")) {
                EffectTemplate wf1 = skillTemplate.getEffectTemplate(1);

                if (wf1 instanceof SummonTotemEffect) {
                    ((SummonTotemEffect) wf1).setTime(10);
                }
            }
            else if (stack.equals("GU_FOREHEADSNIPE")) { // Trunk Shot
                List<EffectTemplate> effects = skillTemplate.getEffects().getEffects();

                if (effects.size() >= 2) {
                    EffectTemplate fhs1 = effects.get(1);

                    if (fhs1 instanceof StaggerEffect) {
                        StunEffect stunEffect = new StunEffect(fhs1.getEffectId(),
                            fhs1.getDuration(), fhs1.getPosition(), fhs1.getElement(),
                            fhs1.getAccMod(), fhs1.getPreeffectsMask());

                        effects.remove(fhs1);
                        effects.add(stunEffect);
                    }
                }
            }
            else if (stack.equals("STUN_2")) { // Shock and Awe charge level 3
                EffectTemplate s2 = skillTemplate.getEffectTemplate(1);

                s2.setDuration(4000);
            }
            else if (stack.equals("AS_BACKFOCUS_PROC")) { // Scoundrel's Bond active
                EffectTemplate sb = skillTemplate.getEffectTemplate(1);
                
                sb.setCriticalProb(0f);
            }

            if (CustomConfig.OLD_SCHOOL) {
                switch (skillTemplate.getSkillId()) {
                    case 8822: // Ranger I
                    case 8823:
                    case 8824:
                    case 8825: // Ranger IV
                    case 8826: // Assassin I
                    case 8827:
                    case 8828:
                    case 8829: // Assassin IV
                        // Remove Calming Whisper I~IV resistances
                        continue;

                    case 833: // Pain Rune I
                    case 834:
                    case 835:
                    case 868: // IV
                    case 911: // Blood Rune I
                    case 933: // II
                    case 2523: // III
                    case 914: // Needle Rune I
                    case 2109: // II
                    case 940: // Radiant Rune I
                    case 941: // Darkness Rune I
                    case 946: // Divine Rune I
                    case 947: // Rune Swipe I
                    case 1989: // Signet Silence I

                        // Remove 20% PvP damage boost on Assassin rune bursts
                        skillTemplate.setPvpDamage(100);
                        break;
                }
            }

            skillData.put(skillTemplate.getSkillId(), skillTemplate);
        }

        if (CustomConfig.OLD_SCHOOL) {
            // Unwavering Devotion 800 -> 1000 resistances
            SkillTemplate UD = getSkillTemplate(287);
            EffectTemplate UD1 = UD.getEffectTemplate(1);

            if (UD1 instanceof StatupEffect) {
                List<Change> change = ((StatupEffect) UD1).getChange();

                for (Change ch : change)
                    ch.setValue(1000);
            }

            // Killer's Eye no penetrations
            SkillTemplate KE1 = getSkillTemplate(839);
            EffectTemplate KE12 = KE1.getEffectTemplate(2);

            if (KE12 instanceof StatupEffect) {
                List<Change> change = ((StatupEffect) KE12).getChange();

                for (Change ch : change)
                    if (ch.getStat() == StatEnum.STUN_BOOST)
                        ch.setValue(0);
            }

            EffectTemplate KE13 = KE1.getEffectTemplate(3);

            if (KE13 instanceof StatupEffect) {
                List<Change> change = ((StatupEffect) KE13).getChange();

                for (Change ch : change)
                    ch.setValue(0);
            }

            // Soul Freeze 20 -> 30 second CD
            SkillTemplate SF1 = getSkillTemplate(1498);
            SF1.setCooldown(300);

            SkillTemplate SF2 = getSkillTemplate(1524);
            SF2.setCooldown(300);

            SkillTemplate SF3 = getSkillTemplate(2853);
            SF3.setCooldown(300);

            // Strengthen Wings 3 -> 5 min CD
            SkillTemplate SW = getSkillTemplate(383);
            SW.setCooldown(3000);

            // Aether Armor 3 -> 5 min CD
            SkillTemplate AA = getSkillTemplate(401);
            AA.setCooldown(3000);

            // Supplication of Focus 30 -> 20 sec duration
            SkillTemplate SoF = getSkillTemplate(1442);
            EffectTemplate SoF1 = SoF.getEffectTemplate(1);
            SoF1.setDuration(20000);

            // Oath of Accuracy 3 -> 5 min CD
            SkillTemplate OoA = getSkillTemplate(836);
            OoA.setCooldown(3000);

            // Nature's Resolve 2 -> 1 resists
            SkillTemplate NR = getSkillTemplate(724);
            EffectTemplate NR2 = NR.getEffectTemplate(2);

            if (NR2 instanceof AlwaysResistEffect) {
                ((AlwaysResistEffect) NR2).setValue(1);
            }

            // Bestial Fury 15 -> 10 meter range
            SkillTemplate BF = getSkillTemplate(646);
            EffectTemplate BF1 = BF.getEffectTemplate(1);

            if (BF1 instanceof WeaponStatupEffect) {
                List<Change> change = ((WeaponStatupEffect) BF1).getChange();

                for (Change ch : change)
                    ch.setValue(-15000);
            }
        }

        skillTemplates.clear();
        skillTemplates = null;
    }

    /**
     * @param skillId
     * @return SkillTemplate
     */
    public SkillTemplate getSkillTemplate(int skillId) {
        return skillData.get(skillId);
    }

    /**
     * @param delayId
     * @return List<SkillTemplate>
     */
    public List<SkillTemplate> getSkillTemplatesByDelayId(int delayId) {
        List<SkillTemplate> templates = new ArrayList<SkillTemplate>();

        for (TIntObjectIterator<SkillTemplate> it = skillData.iterator(); it.hasNext();) {
            it.advance();

            SkillTemplate template = it.value();
            if (template.getDelayId() == delayId)
                templates.add(template);
        }

        return templates;
    }

    /**
     * @return skillData.size()
     */
    public int size() {
        return skillData.size();
    }

    /**
     * @return the skillTemplates
     */
    public List<SkillTemplate> getSkillTemplates() {
        return skillTemplates;
    }

    /**
     * @param skillTemplates
     *            the skillTemplates to set
     */
    public void setSkillTemplates(List<SkillTemplate> skillTemplates) {
        this.skillTemplates = skillTemplates;
        afterUnmarshal(null, null);
    }

    private int initializePower(SkillTemplate skill) {
        if (skill.getActivationAttribute() == ActivationAttribute.MAINTAIN)
            return 30;

        String stack = skill.getStack();

        if (stack.equals("PR_GODSVOICE"))
            return 20; // Word of Destruction

        if (stack.equals("WA_STEADINESS") || stack.equals("KN_IRONBODY")
            || stack.equals("FI_ENERGYWING") || stack.equals("KN_INVINSIBLEPROTECT")
            || stack.equals("FI_CRUSADE") || stack.equals("FI_BERSERK")
            || stack.equals("KN_PURIFYWING"))
            return 30; // Warrior Buffs

        if (stack.equals("FI_ANKLEGRAB"))
            return 30; // Glad Ankle Snare

        if (stack.equals("EL_ORDER_SACRIFIC") || stack.equals("EL_ETERNALSHIELD"))
            return 30; // SM pet shields

        if (stack.equals("CH_IMPROVEDBODY"))
            return 30; // Chanter Elemental Screen

        if (stack.equals("WI_GAINMANA"))
            return 30; // Sorc Gain Mana

        if (stack.equals("RA_SHOCKARROW") || stack.equals("RA_ROOTARROW"))
            return 30; // Ranger snares

        if (stack.equals("WI_COUNTERMAGIC"))
            return 30; // Curse of Weakness

        if (stack.equals("EL_HELLCURSE"))
            return 30; // Infernal Blight

        if (stack.equals("PR_PAINLINKS"))
            return 30; // Chain of Suffering

        if (stack.equals("CH_ETERNALSEAL"))
            return 30; // Stilling Word

        if (stack.equals("EL_DECAYINGWING"))
            return 30; // Wing Root

        if (stack.equals("AS_VENOMSTAB"))
            return 30; // Venomous Strike

        if (stack.equals("CH_MOUNTAINCRASH"))
            return 30; // Mountain Crash

        if (stack.equals("CH_SOAREDROCK"))
            return 30; // Soul Lock

        if (stack.equals("EL_PLAGUESWAM"))
            return 40; // Cursecloud

        if (stack.equals("BA_SONGOFBRAVE"))
            return 80; // Mvt. 2: Summer

        if (stack.equals("LDF5_FORTRESS_ANOHA_AREAFIRE3")
            || stack.equals("LDF5_FORTRESS_ANOHA_POINTFIRE2"))
            return 30; // Anoha debuffs

        if (stack.equals("LDF5_FORTRESS_ANOHA_SUMMON_NORMALFIRE"))
            return 30; // Anoha add DoT

        return 10;
    }
}
