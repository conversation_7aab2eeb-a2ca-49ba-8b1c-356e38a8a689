/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.bossevent;

import gameserver.controllers.NpcController;
import gameserver.model.alliance.PlayerAllianceGroup;
import gameserver.model.gameobjects.Npc;
import gameserver.model.group.PlayerGroup;
import gameserver.model.templates.VisibleObjectTemplate;
import gameserver.model.templates.spawn.SpawnTemplate;

import java.util.ArrayList;
import java.util.List;

public class EventBoss extends Npc {

    private ArrayList<PlayerGroup> rewardGroups;
    private ArrayList<PlayerAllianceGroup> rewardAlliances;

    public EventBoss(int objId, NpcController controller, SpawnTemplate spawn,
        VisibleObjectTemplate objectTemplate) {
        super(objId, controller, spawn, objectTemplate);
        this.rewardGroups = new ArrayList<PlayerGroup>();
        this.rewardAlliances = new ArrayList<PlayerAllianceGroup>();
    }

    public void registerGroup(PlayerGroup group) {
        if (!rewardGroups.contains(group))
            rewardGroups.add(group);
    }

    public void registerAllianceGroup(PlayerAllianceGroup group) {
        if (!rewardAlliances.contains(group))
            rewardAlliances.add(group);
    }

    public List<PlayerGroup> getRewardGroups() {
        return rewardGroups;
    }

    public List<PlayerAllianceGroup> getRewardAlliances() {
        return rewardAlliances;
    }

    @Override
    public NpcController getController() {
        return (NpcController) super.getController();
    }
}
