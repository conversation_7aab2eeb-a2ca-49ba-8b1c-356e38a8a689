/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.configs.network.NetworkConfig;
import gameserver.model.ChatType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.services.PvpService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.stats.StatFunctions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class MercenaryController extends BossController {
    private int legionId = 0;

    private static final List<String> followShouts = new ArrayList<String>() {
        {
            add("Lead us to victory, NAME!");
            add("It is an honor to serve your bidding.");
            add("I will stand by your side!");
        }
    };

    private static final List<String> leaveShouts = new ArrayList<String>() {
        {
            add("NAME, I bid you farewell.");
            add("It has been an honor to serve you, NAME.");
            add("As you wish; I will leave you alone.");
        }
    };

    private static final List<String> busyShouts = new ArrayList<String>() {
        {
            add("Can't you see I'm busy?");
        }
    };

    private static final List<String> ownerShouts = new ArrayList<String>() {
        {
            add("You don't own me!");
            add("I serve only my true masters.");
            add("Who do you think you are?");
        }
    };

    public MercenaryController() {
        super(Arrays.asList(272362, 272363, 232139), true);
    }

    protected void think() {
        return;
    }

    public void setLegionId(int legionId) {
        this.legionId = legionId;
    }

    @Override
    public void doReward() {
        Player killer = getOwner().getAggroList().getMostPlayerDamage();
        if (killer != null && !killer.isOutlaw() && !killer.isLawless() && !killer.isBandit()) {
            // DP reward
            int currentDp = killer.getCommonData().getDp();
            int dpReward = StatFunctions.calculateSoloDPReward(killer, getOwner());
            killer.getCommonData().setDp(dpReward + currentDp);
            
            PvpService.getInstance().addMight(killer, 5);
            killer.getCommonData().addAp(300);
        }
    }

    @Override
    public void onDialogRequest(Player player) {
        if (getOwner().isEnemy(player))
            return;

        Creature followLeader = getOwner().getMoveController().getFollowLeader();

        if (legionId != 0 && player.isLegionMember()
            && player.getLegion().getLegionId() != legionId) {
            // not owner
            PacketSendUtility.sendPacket(
                player,
                new SM_MESSAGE(getOwner().getObjectId(), "Mercenary", ownerShouts.get(
                    Rnd.get(ownerShouts.size())).replaceAll("NAME", player.getName()),
                    ChatType.ALLIANCE));
        }
        else if (followLeader != null) {
            if (followLeader != player) {
                // busy
                PacketSendUtility.sendPacket(
                    player,
                    new SM_MESSAGE(getOwner().getObjectId(), "Mercenary", busyShouts.get(
                        Rnd.get(busyShouts.size())).replaceAll("NAME", player.getName()),
                        ChatType.ALLIANCE));
            }
            else {
                // leave
                getOwner().setTarget(null);
                player.removeFollower(getOwner());
                PacketSendUtility.sendPacket(
                    player,
                    new SM_MESSAGE(getOwner().getObjectId(), "Mercenary", leaveShouts.get(
                        Rnd.get(leaveShouts.size())).replaceAll("NAME", player.getName()),
                        ChatType.ALLIANCE));
            }
        }
        else {
            if (NetworkConfig.GAMESERVER_ID == 17 || player.getFollowers().size() >= 3) {
                PacketSendUtility.sendPacket(player, new SM_MESSAGE(getOwner().getObjectId(),
                    "Mercenary", "I am not at your disposal! You are alone on this one.",
                    ChatType.ALLIANCE));
                return;
            }

            // follow
            player.addFollower(getOwner());
            PacketSendUtility.sendPacket(
                player,
                new SM_MESSAGE(getOwner().getObjectId(), "Mercenary", followShouts.get(
                    Rnd.get(followShouts.size())).replaceAll("NAME", player.getName()),
                    ChatType.ALLIANCE));
        }
    }
}
