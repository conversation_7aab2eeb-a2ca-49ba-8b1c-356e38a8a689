/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.bet;

/**
 * Representing a bet condition consist of a is-Value and a set-Value. In this case "value" and "expectedValue". <br>
 * 
 * <AUTHOR>
 * 
 */
public class Condition {
    private int eventId;
    private int expectedValue;

    public Condition(int eventID, int expectedValue) {
        this.eventId = eventID;
        this.expectedValue = expectedValue;
    }

    public int getEventID() {
        return eventId;
    }

    public int getExpectedValue() {
        return expectedValue;
    }

    public void setEventID(int eventId) {
        this.eventId = eventId;
    }

    public void setExpectedValue(int expectedValue) {
        this.expectedValue = expectedValue;
    }

}
