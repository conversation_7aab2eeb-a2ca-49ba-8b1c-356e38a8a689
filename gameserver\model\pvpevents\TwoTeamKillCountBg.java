/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.configs.main.GSConfig;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.model.pvpevents.Battleground.BattlegroundMap;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.utils.ThreadPoolManager;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TwoTeamKillCountBg extends Battleground {
    private int extraCounter = 0;

    public TwoTeamKillCountBg() {
        super.name = "2-Team Kill Count";
        super.description = "You are on a team and must kill as many enemies as possible within the time limit. You will respawn at your base every 30 seconds.";
        super.minSize = 3;
        super.maxSize = 6;
        super.teamCount = 2;
        super.matchLength = 300;

        BattlegroundMap map1 = new BattlegroundMap(220010000);
        map1.addSpawn(new SpawnPosition(519.3f, 1884.0f, 294.00f));
        map1.addSpawn(new SpawnPosition(637.2f, 1890.9f, 296.07f));
        map1.setKillZ(270f);

        BattlegroundMap map2 = new BattlegroundMap(300350000);
        map2.addSpawn(new SpawnPosition(1328.0f, 1026.6f, 340.6f));
        map2.addSpawn(new SpawnPosition(1332.1f, 1115.0f, 339.9f));
        map2.addStaticDoor(221);
        map2.addStaticDoor(224);
        map2.addStaticDoor(216);
        map2.addStaticDoor(222);
        map2.addStaticDoor(218);
        map2.addStaticDoor(217);
        map2.addStaticDoor(219);
        map2.addStaticDoor(214);
        map2.addStaticDoor(223);
        map2.addStaticDoor(215);
        map2.addStaticDoor(213);
        map2.addStaticDoor(220);
        map2.setKillZ(330f);

        BattlegroundMap map3 = new BattlegroundMap(301180000); // 4.0
        map3.addSpawn(new SpawnPosition(196.0f, 176.0f, 59.0f));
        map3.addSpawn(new SpawnPosition(318.0f, 341.0f, 60.0f));
        map3.setKillZ(53f);

        BattlegroundMap map4 = new BattlegroundMap(300280000); // Rentus
        map4.addSpawn(new SpawnPosition(770f, 636f, 156f));
        map4.addSpawn(new SpawnPosition(892f, 638f, 156f));
        map4.setKillZ(152f);

        BattlegroundMap map6 = new BattlegroundMap(400030000);
        map6.addSpawn(new SpawnPosition(570.2f, 476.2f, 676f));
        map6.addSpawn(new SpawnPosition(449.2f, 549.8f, 676f));
        map6.setKillZ(672f);
        map6.setHighestZ(683f);
        
        BattlegroundMap map7 = new BattlegroundMap(210060000);
        map7.addSpawn(2756.4f, 1684f, 52f);
        map7.addSpawn(2673.5f, 1623f, 52f);
        map7.setKillZ(42f);
        map7.setHighestZ(75f);

        super.maps.add(map1);
        super.maps.add(map2);
        super.maps.add(map4);
        super.maps.add(map6);
        super.maps.add(map7);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 30000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 30000);

        startBattleground(30000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups(0) <= 1)
                    endTwoTeamMatch();
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamMatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    @Override
                    public void run() {
                        extraCounter++;

                        showTeamPositions();

                        if ((extraCounter % 2) == 0) {
                            extraCounter = 0;
                            autoResurrection();
                        }
                    }
                }, 15 * 1000, 15 * 1000));
            }
        }, 30 * 1000);
    }

    @Override
    public boolean createTournament(List<List<Player>> teams) {
        if (!super.createGroups(teams))
            return false;

        this.matchLength = 510;
        startMatch();

        return true;
    }

    public void showTeamPositions() {
        PlayerGroup leading = null;
        for (PlayerGroup group : super.getGroups()) {
            if (leading == null && group.getKillCount() > 0)
                leading = group;
            else if (leading != null && group.getKillCount() > leading.getKillCount())
                leading = group;
        }

        if (leading != null) {
            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == leading.getGroupId()) {
                    for (Player pl : group.getMembers())
                        super.scheduleAnnouncement(pl, "Your team is in the lead on kills!", 0);
                }
                else {
                    int killDiff = leading.getKillCount() - group.getKillCount();
                    for (Player pl : group.getMembers())
                        super.scheduleAnnouncement(pl, "Your team is " + killDiff
                            + " kills behind the leading team!", 0);
                }
            }

            super.specAnnounce(LadderService.getInstance().getNameByIndex(leading.getBgIndex())
                + " is in the lead on kills!");
        }
    }

    public void autoResurrection() {
        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead()) {
                    pl.getReviveController().fullRevive();
                    super.spawnProtection(pl);
                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    TeleportService.teleportTo(pl, getMapId(), super.getInstanceId(), pos.getX(),
                        pos.getY(), pos.getZ(), 0);
                    super
                        .scheduleAnnouncement(pl, "You have been automatically resurrected!", 1500);
                }
            }
        }
    }

    private PlayerGroup getWinner() {
        PlayerGroup winner = null;
        int drawPoints = 0;

        for (PlayerGroup group : super.getGroups()) {
            if (winner == null && group.getKillCount() > drawPoints) {
                winner = group;
            }
            else if (winner == null) {
                continue;
            }
            else if (winner.getKillCount() < group.getKillCount()) {
                winner = group;
            }
            else if (winner.getKillCount() == group.getKillCount()) {
                drawPoints = winner.getKillCount();
                winner = null;
            }
        }

        return winner;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super.scheduleAnnouncement(player, "You will automatically resurrect every 30 seconds.", 0);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in TwoTeamKillCountBg!");
            }
            else {
                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 2);
            }
        }
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups(0) <= 1)
            endTwoTeamMatch();
    }

    private void endTwoTeamMatch() {
        super.onEndFirstDefault();

        PlayerGroup winner = getWinner();
        if (winner == null) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    scheduleAnnouncement(pl, "The match was a draw! Better luck next time.", 0);
                    super.rewardPlayer(pl, 10, false);
                }
            }
            super.specAnnounce("The match was a draw!");
        }
        else {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == winner.getGroupId()) {
                    for (Player pl : group.getMembers()) {
                        int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2 : 1;

                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE / 2);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.scheduleAnnouncement(pl,
                            "Your team has won the match with " + group.getKillCount() + " kills!",
                            0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                        super.rewardPlayer(pl, 20, true);
                    }
                }
                else {
                    for (Player pl : group.getMembers()) {
                        super.playerLoseMatch(pl, -super.K_VALUE / 3);

                        super.scheduleAnnouncement(pl,
                            "Your team has lost the match with " + group.getKillCount()
                                + " kills against " + winner.getKillCount() + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);
                        super.rewardPlayer(pl, 10, false);
                    }
                }
            }

            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match with " + winner.getKillCount() + " kills!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}