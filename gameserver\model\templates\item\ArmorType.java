/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.templates.item;

import gameserver.configs.main.GSConfig;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlType(name = "armor_type")
@XmlEnum
public enum ArmorType {
    CHAIN(new int[] { 6, 13 }),
    CLOTHES(new int[] { 4 }),
    LEATHER(new int[] { 5, 12 }),
    PLATE(new int[] { 18 }),
    ROBE(new int[] { 67, 70 }),
    SHARD(new int[] {}),
    SHIELD(new int[] { 7, 14 }),
    ARROW(new int[] {});

    private int[] requiredSkills;

    private ArmorType(int[] requiredSkills) {
        if (requiredSkills.length == 2) {
            if (requiredSkills[0] == 6 && requiredSkills[1] == 13) // Chain
                requiredSkills = new int[] { 42, 49 };
            else if (requiredSkills[0] == 5 && requiredSkills[1] == 12) // Leather
                requiredSkills = new int[] { 41, 48 };
            else if (requiredSkills[0] == 67 && requiredSkills[1] == 70) // Robe (Cloth)
                requiredSkills = new int[] { 103, 106 };
            else if (requiredSkills[0] == 7 && requiredSkills[1] == 14) // Shield
                requiredSkills = new int[] { 43, 50 };
        }
        else if (requiredSkills.length == 1) {
            if (requiredSkills[0] == 4) // Clothes (costume)
                requiredSkills = new int[] { 40, 47 };
            else if (requiredSkills[0] == 18) // Plate
                requiredSkills = new int[] { 54, 64 };
        }

        this.requiredSkills = requiredSkills;
    }

    public int[] getRequiredSkills() {
        return requiredSkills;
    }

    /**
     * @return int
     */
    public int getMask() {
        return 1 << this.ordinal();
    }
}
