/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.itemengine.actions;

import gameserver.dao.PlayerAppearanceDAO;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.PlayerAppearance;
import gameserver.network.aion.serverpackets.SM_MOTION;
import gameserver.network.aion.serverpackets.SM_PLAYER_INFO;
import gameserver.utils.PacketSendUtility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CosmeticAction")
public class CosmeticAction extends AbstractItemAction {
    @XmlAttribute
    private String lips;

    @XmlAttribute
    private String eyes;

    @XmlAttribute
    private String face;

    @XmlAttribute
    private String hair;

    @XmlAttribute
    private int hairType;

    @XmlAttribute
    private int faceType;

    @XmlAttribute
    private int tattooType;

    @XmlAttribute
    private int makeupType;

    @XmlAttribute
    private int voiceType;

    @XmlAttribute
    private String preset;

    public void act(Player player, Item parentItem, Item targetItem) {
        player.getInventory().removeFromBagByObjectId(parentItem.getObjectId(), 1);
        
        PlayerAppearance appearance = player.getPlayerAppearance();
        
        if (lips != null)
            appearance.setLipRGB(Integer.parseInt(lips, 16));
        if (eyes != null)
            appearance.setEyeRGB(Integer.parseInt(eyes, 16));
        if (hair != null)
            appearance.setHairRGB(Integer.parseInt(hair, 16));
        
        if (hairType != 0)
            appearance.setHair(hairType);
        if (faceType != 0)
            appearance.setFaceShape(faceType);
        if (tattooType != 0)
            appearance.setTattoo(tattooType);
        if (makeupType != 0)
            appearance.setDeco(makeupType);
        if (voiceType != 0)
            appearance.setVoice(voiceType);
        
        DAOManager.getDAO(PlayerAppearanceDAO.class).store(player); // save new appearance
        
        player.clearKnownlist();
        PacketSendUtility.sendPacket(player, new SM_PLAYER_INFO(player, false));
        PacketSendUtility.broadcastPacketAndReceive(player, new SM_MOTION(player));
        player.updateKnownlist();
        player.getEffectController().updatePlayerEffectIcons();
    }

    @Override
    public boolean canAct(Player player, Item parentItem, Item targetItem) {
        return true;
    }

    /**
     * @return the lips
     */
    public String getLipsColor() {
        return lips;
    }

    /**
     * @return the eyes
     */
    public String getEyesColor() {
        return eyes;
    }

    /**
     * @return the face
     */
    public String getFaceColor() {
        return face;
    }

    /**
     * @return the hair
     */
    public String getHairColor() {
        return hair;
    }

    /**
     * @return the hairType
     */
    public int getHairType() {
        return hairType;
    }

    /**
     * @return the faceType
     */
    public int getFaceType() {
        return faceType;
    }

    /**
     * @return the tattooType
     */
    public int getTattooType() {
        return tattooType;
    }

    /**
     * @return the makeupType
     */
    public int getMakeupType() {
        return makeupType;
    }

    /**
     * @return the voiceType
     */
    public int getVoiceType() {
        return voiceType;
    }

    /**
     * @return the preset
     */
    public String getPresetName() {
        return preset;
    }

}
