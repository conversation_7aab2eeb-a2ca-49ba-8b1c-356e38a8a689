/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.eventengine.Event;
import gameserver.utils.ThreadPoolManager;

import java.util.ArrayList;
import java.util.List;

/**
 * This event wraps around other event and fire them in a repetitively order
 * 
 * <AUTHOR>
 * 
 */
public class IterativeEvent<E extends Event> extends Event {
    private static final int CHECK_DELAY = 1000; // in msec

    private List<Event> events = new ArrayList<Event>();
    private int last_index = 0;
    private Event last_event;

    /*
     * (non-Javadoc)
     * @see java.lang.Runnable#run()
     */
    @Override
    public void execute() {
        if (events.size() == 0) {
            finish();
            return;
        }

        last_index = getIndex();
        last_event = events.get(last_index);
        last_event.run();
        ThreadPoolManager.getInstance().schedule(new waitForExecutionRunnanble(last_event),
            CHECK_DELAY);
    }

    @Override
    public int getCooldown() {
        if (last_event == null)
            return 1;
        return last_event.getCooldown();
    }

    public void register(E event) {
        events.add(event);
    }

    public void unregister(E event) {
        events.remove(event);
    }

    public int getIndex() {
        int index = last_index + 1;
        if (index >= events.size())
            index = 0;

        return index;
    }

    private void waitForExecution(Event event) {
        if (event.isFinished()) {
            finish();
        }
        else {
            ThreadPoolManager.getInstance().schedule(new waitForExecutionRunnanble(event),
                CHECK_DELAY);
        }
    }

    private class waitForExecutionRunnanble implements Runnable {
        private final Event event;

        public waitForExecutionRunnanble(Event event) {
            this.event = event;
        }

        @Override
        public void run() {
            waitForExecution(event);
        }

    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#cancel(boolean)
     */
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return false;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#onReset()
     */
    @Override
    protected void onReset() {
        if (last_event != null)
            last_event.reset();
    }

}
