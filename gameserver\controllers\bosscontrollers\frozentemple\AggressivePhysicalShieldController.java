/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.frozentemple;

import gameserver.controllers.bosscontrollers.BossController;
import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.utils.MathUtil;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class AggressivePhysicalShieldController extends BossController {
    private final SkillTemplate sTemplate = DataManager.SKILL_DATA.getSkillTemplate(19238);

    public AggressivePhysicalShieldController() {
        super(Arrays.asList(230508, 231060), true);
    }

    @Override
    protected void think() {
        Npc owner = getOwner();

        if (!owner.getEffectController().hasAbnormalEffect(19238)) {
            Effect effect = new Effect(owner, owner, sTemplate, 1, 60 * 60 * 1000);
            owner.getEffectController().addEffect(effect);
            effect.addAllEffectToSucess();
            effect.startEffect(true);
        }

        Player priority = getPriorityTarget();
        if (priority == null || !MathUtil.isIn3dRange(owner, priority, 20))
            return;

        getOwner().getAggroList().addHate(priority, 10000);
    }
}