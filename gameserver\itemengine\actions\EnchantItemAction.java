/*
 * This file is part of aion-unique <aion-unique.com>.
 *
 *     <PERSON>on-unique is free software: you can redistribute it and/or modify
 *     it under the terms of the GNU General Public License as published by
 *     the Free Software Foundation, either version 3 of the License, or
 *     (at your option) any later version.
 *
 *     <PERSON>on-unique is distributed in the hope that it will be useful,
 *     but WITHOUT ANY WARRANTY; without even the implied warranty of
 *     MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *     GNU General Public License for more details.
 *
 *     You should have received a copy of the GNU General Public License
 *     along with aion-unique.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.itemengine.actions;

import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.StartMovingListener;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_ITEM_USAGE_ANIMATION;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.EnchantService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

import org.apache.log4j.Logger;

/**
 * <AUTHOR> Date: 16.12.2009
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EnchantItemAction")
public class EnchantItemAction extends AbstractItemAction {
    @XmlAttribute(name = "count")
    protected int sub_enchant_material_many;

    public int getEnchantCount() {
        return sub_enchant_material_many;
    }

    @Override
    public boolean canAct(Player player, Item parentItem, Item targetItem) {
        return canAct(player, parentItem, targetItem, 1);
    }

    public boolean canAct(Player player, Item parentItem, Item targetItem, int targetWeapon) {
        if (targetItem == null) { // no item selected.
            PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.STR_ITEM_ERROR);
            return false;
        }

        if (!targetItem.getItemTemplate().isWeapon() && !targetItem.getItemTemplate().isArmor()) {
            Logger.getLogger(this.getClass()).info(
                "[AUDIT] Player: " + player.getName()
                    + " is trying to enchant/socket non-weapon or non-armor. Hacking!");
            return false;
        }

        return true;
    }

    @Override
    public void act(final Player player, final Item parentItem, final Item targetItem) {
        act(player, parentItem, targetItem, null, 1);
    }

    // necessary overloading to not change AbstractItemAction
    public void act(final Player player, final Item parentItem, final Item targetItem,
        final Item supplementItem, final int targetWeapon) {
        PacketSendUtility.broadcastPacketAndReceive(player, new SM_ITEM_USAGE_ANIMATION(player
            .getObjectId(), parentItem.getObjectId(), parentItem.getItemTemplate().getTemplateId(),
            1000, 0, 0));
        player.getController().cancelTask(TaskId.ITEM_USE);

        final ActionObserver observer = new StartMovingListener() {
            @Override
            public void moved() {
                player.getController().cancelTask(TaskId.ITEM_USE);
                PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.STR_ITEM_CANCELED());
                PacketSendUtility.broadcastPacketAndReceive(player, new SM_ITEM_USAGE_ANIMATION(
                    player.getObjectId(), parentItem.getObjectId(), parentItem.getItemTemplate()
                        .getTemplateId(), 0, 3, 0));
            }
        };
        player.getObserveController().attach(observer);

        player.getController().addNewTask(TaskId.ITEM_USE,
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    player.getObserveController().removeObserver(observer);

                    if (parentItem.getItemTemplate().isEnchantmentStone()) {
                        boolean result = EnchantService.enchantItem(player, parentItem, targetItem,
                            supplementItem);
                        PacketSendUtility.broadcastPacketAndReceive(player,
                            new SM_ITEM_USAGE_ANIMATION(player.getObjectId(), parentItem
                                .getObjectId(), parentItem.getItemTemplate().getTemplateId(), 0,
                                result ? 1 : 2, 0));
                    }
                    else if (parentItem.getItemTemplate().isTemperingSolution()) {
                        boolean result = EnchantService.temperItem(player, parentItem, targetItem);
                        PacketSendUtility.broadcastPacketAndReceive(player,
                            new SM_ITEM_USAGE_ANIMATION(player.getObjectId(), parentItem
                                .getObjectId(), parentItem.getItemTemplate().getTemplateId(), 0,
                                result ? 1 : 2, 0));
                    }
                    else {
                        boolean result = EnchantService.socketManastone(player, parentItem,
                            targetItem, supplementItem, targetWeapon);
                        PacketSendUtility.broadcastPacketAndReceive(player,
                            new SM_ITEM_USAGE_ANIMATION(player.getObjectId(), parentItem
                                .getObjectId(), parentItem.getItemTemplate().getTemplateId(), 0,
                                result ? 1 : 2, 0));
                    }

                }

            }, 1000));
    }
}
