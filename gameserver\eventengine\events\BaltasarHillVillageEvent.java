/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class BaltasarHillVillageEvent extends MobEvent {
    public BaltasarHillVillageEvent() {
        super.mapId = 220050000;
        super.center = new SpawnPosition(1368, 1963, 43);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("An event at Baltasar Hill Village in Brusthonin will commence in 3 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at Baltasar Hill Village starts in 1 minute", 2 * 60 * 1000);
        announceAll("The event at Baltasar Hill Village starts in 30 seconds", 30 * 1000 + 2
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 3 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters at Baltasar Hill Village in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217079, 1391, 2008, 52);
        spawnMob(217079, 1376, 2001, 49);
        spawnMob(217079, 1388, 1990, 49);
        spawnMob(217079, 1368, 1990, 46);
        spawnMob(217079, 1381, 1978, 46);
        spawnMob(217079, 1354, 2020, 46);
        spawnMob(217079, 1347, 2017, 45);
        spawnMob(217079, 1329, 2016, 46);
        spawnMob(217079, 1317, 2011, 46);
        spawnMob(217079, 1309, 1997, 47);
        spawnMob(217079, 1305, 1958, 52);
        spawnMob(217079, 1312, 1950, 52);
        spawnMob(217079, 1312, 1939, 52);
        spawnMob(217079, 1312, 1926, 52);
        spawnMob(217079, 1351, 1909, 47);
        spawnMob(217079, 1364, 1907, 47);
        spawnMob(217079, 1359, 1900, 47);
        spawnMob(217079, 1383, 1918, 48);
        spawnMob(217079, 1394, 1927, 48);
        spawnMob(217079, 1401, 1931, 48);
    }
}
