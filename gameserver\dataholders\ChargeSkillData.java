/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dataholders;

import gameserver.model.templates.ChargeSkillTemplate;
import gnu.trove.TIntObjectHashMap;

import java.util.List;

import javax.xml.bind.Unmarshaller;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 */
@XmlRootElement(name = "charge_skills")
@XmlAccessorType(XmlAccessType.FIELD)
public class ChargeSkillData {
    @XmlElement(name = "charge_skill")
    private List<ChargeSkillTemplate> chargeSkills;

    private TIntObjectHashMap<ChargeSkillTemplate> templates;

    void afterUnmarshal(Unmarshaller u, Object parent) {
        templates = new TIntObjectHashMap<ChargeSkillTemplate>();

        for (ChargeSkillTemplate chargeSkill : chargeSkills)
            templates.put(chargeSkill.getId(), chargeSkill);

        chargeSkills = null;
    }

    public int size() {
        return templates.size();
    }

    public ChargeSkillTemplate getChargeSkillTemplate(int skillId) {
        return templates.get(skillId);
    }
}
