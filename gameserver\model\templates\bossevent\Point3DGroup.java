package gameserver.model.templates.bossevent;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "point3Dgroup")
/*
 * , propOrder = { "point" })
 */
public class Point3DGroup {

    @XmlElement(name = "point")
    protected List<Point3DTemplate> point;

    protected List<Position3D> returnList;

    public List<Position3D> getSpawnpoints() {
        if (point == null) {
            point = new ArrayList<Point3DTemplate>();
        }
        if (returnList == null) {
            returnList = new ArrayList<Position3D>();
            for (Point3DTemplate pointE : point) {
                returnList.add(pointE.Point3DValue());
            }
        }
        return returnList;
    }

}
