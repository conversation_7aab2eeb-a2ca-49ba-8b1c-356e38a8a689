package gameserver.controllers.bosscontrollers.slipperyslope;

import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.gameobjects.Npc;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class AssaultWarriorController extends BossController {
    private long nextMove = 0;

    public AssaultWarriorController() {
        super(282637, true);
    }

    protected void think() {
        Npc owner = getOwner();

        if (owner.getAggroList().getMostHated() == null) {
            if (nextMove == 0) {
                nextMove = System.currentTimeMillis() + Rnd.get(8000, 15000);
            }
            else if (System.currentTimeMillis() > nextMove) {
                nextMove = 0;

                randomWalk(8);
            }
        }
    }
}
