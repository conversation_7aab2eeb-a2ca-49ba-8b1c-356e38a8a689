/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.ActionObserver.ObserverType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StoneStatEffectId;
import gameserver.model.gameobjects.stats.modifiers.RateModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.group.PlayerGroup;
import gameserver.network.aion.serverpackets.SM_MOTION;
import gameserver.network.aion.serverpackets.SM_PLAYER_INFO;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.skillengine.effect.EffectTemplate;
import gameserver.skillengine.effect.FearEffect;
import gameserver.skillengine.effect.ItemHealEffect;
import gameserver.skillengine.effect.OpenAerialEffect;
import gameserver.skillengine.effect.ParalyzeEffect;
import gameserver.skillengine.effect.PulledEffect;
import gameserver.skillengine.effect.RootEffect;
import gameserver.skillengine.effect.SimpleRootEffect;
import gameserver.skillengine.effect.SleepEffect;
import gameserver.skillengine.effect.SlowEffect;
import gameserver.skillengine.effect.SnareEffect;
import gameserver.skillengine.effect.StaggerEffect;
import gameserver.skillengine.effect.StumbleEffect;
import gameserver.skillengine.effect.StunEffect;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.World;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class TwoTeamChickenBg extends Battleground {
    private static final int EGG_MODEL = 280482;
    private static final int CHICKEN_MODEL = 210340;
    private static final float CHICKEN_HEIGHT = 4.0f;

    private Map<Integer, Integer> roundResults = new HashMap<Integer, Integer>();
    private int maxRounds = 3;
    private int roundsDone = 0;

    private Player chicken = null;
    private ActionObserver chickenObserver = null;
    private float oldChickenHeight = 1.0f;

    public TwoTeamChickenBg() {
        super.name = "2-Team Chicken";
        super.description = "You are a on a team and must get the Chicken to the gate at your base, but first you need to crack the Egg at the center open. Killing an enemy Chicken will turn the killer into a Chicken. There are "
            + maxRounds + " rounds.";
        super.minSize = 3;
        super.maxSize = 6;
        super.teamCount = 2;
        super.matchLength = 105;

        BattlegroundMap map1 = new BattlegroundMap(300170000);
        map1.addSpawn(new SpawnPosition(1215.5f, 659.6f, 250.24f));
        map1.addSpawn(new SpawnPosition(1215.6f, 495.1f, 247.12f));
        map1.setKillZ(245f);

        super.maps.add(map1);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 25000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 25000);

        startBattleground(25000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups() <= 1) {
                    roundsDone = maxRounds;
                    endTwoTeamMatch(null, true);
                }
            }
        });

        spawnEgg();

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamMatch(null, true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        boolean chickenKill = false;

        if (chicken != null && chicken.getObjectId() == player.getObjectId()) {
            endChicken();
            chickenKill = true;
        }

        super.onDieDefault(player, lastAttacker);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in TwoTeamChickenBg!");
            }
            else {
                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 2);
            }

            if (chickenKill) {
                for (PlayerGroup group : super.getGroups())
                    for (Player pl : group.getMembers())
                        scheduleAnnouncement(pl,
                            "The chicken was caught before it got home! Catch the new chicken!", 0);
                specAnnounce("The chicken was caught before it got home! Catch the new chicken!");

                makeChicken(killer);
            }
        }

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (player.getBattleground() != null
                    && player.getBattleground() instanceof TwoTeamChickenBg)
                    spawnPlayer(player);
            }
        }, 6000);
    }

    @Override
    public void onArtifactDie(int playerObjId) {
        Player player = World.getInstance().findPlayer(playerObjId);
        if (player == null)
            player = super.getGroups().get(Rnd.get(super.getGroups().size())).getGroupLeader();

        if (chicken != null) {
            endChicken();
        }

        makeChicken(player);

        for (PlayerGroup group : super.getGroups())
            for (Player pl : group.getMembers())
                scheduleAnnouncement(pl, "The egg was cracked! Catch the chicken!", 0);
        specAnnounce("The egg was cracked! Catch the chicken!");
    }

    @Override
    public boolean isEffectAllowed(EffectTemplate et) {
        if (et instanceof ItemHealEffect)
            return false;
        else if (et instanceof SleepEffect)
            return false;
        else if (et instanceof StunEffect)
            return false;
        else if (et instanceof StumbleEffect)
            return false;
        else if (et instanceof FearEffect)
            return false;
        else if (et instanceof RootEffect)
            return false;
        else if (et instanceof ParalyzeEffect)
            return false;
        else if (et instanceof PulledEffect)
            return false;
        else if (et instanceof SimpleRootEffect)
            return false;
        else if (et instanceof SlowEffect)
            return false;
        else if (et instanceof SnareEffect)
            return false;
        else if (et instanceof StaggerEffect)
            return false;
        else if (et instanceof OpenAerialEffect)
            return false;
        else if (et.getEffectId() == 101821)
            return false;
        return true;
    }

    private void addChickenChecker() {
        if (chicken != null && chicken.isInGroup()) {
            final SpawnPosition endPos;
            if (chicken.getPlayerGroup().getBgIndex() == 0)
                endPos = new SpawnPosition(1215.3f, 678.4f, 252.44f);
            else
                endPos = new SpawnPosition(1215.2f, 482.8f, 249.16f);

            chickenObserver = new ActionObserver(ObserverType.MOVE) {
                @Override
                public void moved() {
                    if (MathUtil.getDistance(chicken, endPos.getX(), endPos.getY(), endPos.getZ()) < 5)
                        endTwoTeamMatch(chicken.getPlayerGroup(), false);
                }
            };

            chicken.getObserveController().addObserver(chickenObserver);
        }
    }

    private void applyChickenSpeedReduction() {
        chicken.getGameStats().endEffect(StoneStatEffectId.getInstance(1, 1));

        TreeSet<StatModifier> stMods = new TreeSet<StatModifier>();
        stMods.add(RateModifier.newInstance(StatEnum.SPEED, -80, true));

        chicken.getGameStats().addModifiers(StoneStatEffectId.getInstance(1, 1), stMods);
    }

    private void spawnPlayer(Player player) {
        if (player.getLifeStats().isAlreadyDead())
            player.getReviveController().fullRevive();

        if (player.getPlayerGroup().getBgIndex() > 0
            && player.getPlayerGroup().getBgIndex() < getSpawnPositions().size()) {
            SpawnPosition spawnPos = getSpawnPositions().get(player.getPlayerGroup().getBgIndex());

            if (spawnPos != null)
                TeleportService.teleportTo(player, getMapId(), getInstanceId(), spawnPos.getX(),
                    spawnPos.getY(), spawnPos.getZ(), 0);
            else
                log.error("spawnPos == null!");
        }
        else {
            log.error("TwoTeamChickenBg: player group bg index out of range!");
        }
    }

    private void endChicken() {
        chicken.getObserveController().removeObserver(chickenObserver);
        chicken.setTransformedModelId(0);
        chicken.getPlayerAppearance().setHeight(oldChickenHeight);
        chicken.getGameStats().endEffect(StoneStatEffectId.getInstance(1, 1));
        chicken.clearKnownlist();
        PacketSendUtility.sendPacket(chicken, new SM_PLAYER_INFO(chicken, false));
        PacketSendUtility.sendPacket(chicken, new SM_MOTION(chicken));
        chicken.updateKnownlist();
        chicken = null;
    }

    private void makeChicken(Player player) {
        chicken = player;

        applyChickenSpeedReduction();

        oldChickenHeight = chicken.getPlayerAppearance().getHeight();
        chicken.setTransformedModelId(CHICKEN_MODEL);
        chicken.getPlayerAppearance().setHeight(CHICKEN_HEIGHT);
        chicken.clearKnownlist();
        PacketSendUtility.sendPacket(chicken, new SM_PLAYER_INFO(chicken, false));
        PacketSendUtility.sendPacket(chicken, new SM_MOTION(chicken));
        chicken.updateKnownlist();
        addChickenChecker();

        scheduleAnnouncement(player, "Hurry back to the gate at your spawn!", 500);
    }

    private void spawnEgg() {
        SpawnEngine.getInstance().spawnEgg(this, 5, getMapId(), EGG_MODEL, 1215.58f, 565.97f,
            246.23135f, (byte) 0);
    }

    public boolean isChicken(Player player) {
        return (chicken != null && chicken == player);
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups() <= 1) {
            roundsDone = maxRounds;
            endTwoTeamMatch(null, false);
        }
    }

    private void startNewRound() {
        super.setStartStamp(System.currentTimeMillis());
        super.killSummonedCreatures();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                int delay = 0;

                synchronized (getGroups()) {
                    for (PlayerGroup group : getGroups()) {
                        for (Player pl : group.getMembers()) {
                            preparePlayer(pl, 7000, false);

                            SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                            if (pos != null)
                                TeleportService.teleportTo(pl, getMapId(), pos.getX(), pos.getY(),
                                    pos.getZ(), delay += 250);
                        }
                    }
                }
            }
        }, 3000);

        spawnEgg();

        for (Player pl : super.getSpectators())
            super.createTimer(pl, getMatchLength());

        super.specAnnounce("The round has begun!!!", 10000);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamMatch(null, true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                for (PlayerGroup group : getGroups()) {
                    if (group.size() < 1) {
                        roundsDone = maxRounds;
                        endTwoTeamMatch(null, false);
                        break;
                    }
                }
            }
        }, 15000);
    }

    private void addRoundWin(PlayerGroup group) {
        if (group == null)
            return;

        Integer result = roundResults.get(group.getBgIndex());
        if (result != null)
            roundResults.put(group.getBgIndex(), result + 1);
        else
            roundResults.put(group.getBgIndex(), 1);
    }

    private PlayerGroup getWinner() {
        PlayerGroup winner = null;
        int drawPoints = 0;

        for (Map.Entry<Integer, Integer> entry : roundResults.entrySet()) {
            if (winner == null && entry.getValue() > drawPoints) {
                winner = super.getGroups().get(entry.getKey());
            }
            else if (winner == null) {
                continue;
            }
            else if (roundResults.get(winner.getBgIndex()) < entry.getValue()) {
                winner = super.getGroups().get(entry.getKey());
            }
            else if (roundResults.get(winner.getBgIndex()) == entry.getValue()) {
                drawPoints = roundResults.get(winner.getBgIndex());
                winner = null;
            }
        }

        return winner;
    }

    private void endTwoTeamMatch(PlayerGroup roundWinner, boolean isDraw) {
        if (chicken != null) {
            endChicken();
        }

        for (PlayerGroup group : super.getGroups())
            for (Player pl : group.getMembers())
                super.freezeNoEnd(pl);

        super.onEndFirstDefault();

        if (roundsDone < maxRounds) {
            roundsDone++;

            if (isDraw && roundWinner == null && chicken != null)
                roundWinner = chicken.getPlayerGroup();

            if (roundWinner != null)
                addRoundWin(roundWinner);
        }

        if (roundsDone < maxRounds) {
            if (roundWinner != null) {
                for (PlayerGroup group : super.getGroups())
                    for (Player pl : group.getMembers())
                        super.scheduleAnnouncement(pl,
                            LadderService.getInstance().getNameByIndex(roundWinner.getBgIndex())
                                + " has won the round!", 0);
                super.specAnnounce(LadderService.getInstance().getNameByIndex(
                    roundWinner.getBgIndex())
                    + " has won the round!");
            }
            else {
                for (PlayerGroup group : super.getGroups())
                    for (Player pl : group.getMembers())
                        super.scheduleAnnouncement(pl, "The round was a draw!", 0);
                super.specAnnounce("The round was a draw!");
            }

            startNewRound();
            return;
        }

        PlayerGroup winner = getWinner();
        if (winner == null) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.scheduleAnnouncement(pl, "The match was a draw! Better luck next time.",
                        0);
                    super.rewardPlayer(pl, 20, false);
                }
            }
            super.specAnnounce("The match was a draw!");
        }
        else {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == winner.getGroupId()) {
                    for (Player pl : group.getMembers()) {
                        int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2 : 1;

                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE / 2);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.scheduleAnnouncement(pl, "Your team has won the match!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                        super.rewardPlayer(pl, 30, true);
                    }
                }
                else {
                    for (Player pl : group.getMembers()) {
                        super.playerLoseMatch(pl, -super.K_VALUE / 3);

                        super.scheduleAnnouncement(pl, "Your team has lost the match!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);
                        super.rewardPlayer(pl, 20, false);
                    }
                }
            }
            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}