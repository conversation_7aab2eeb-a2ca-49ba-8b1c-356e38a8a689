/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.eventengine.Event;
import gameserver.eventengine.events.MobEvent;
import gameserver.model.gameobjects.Creature;

/**
 * <AUTHOR>
 * 
 */
public class EventMobController extends MonsterController {
    private Event event;

    public EventMobController(Event event) {
        this.event = event;
    }

    @Override
    public void onDie(Creature lastAttacker) {
        super.onDie(lastAttacker);

        if (event != null) {
            if (event instanceof MobEvent)
                ((MobEvent) event).onCreatureDie(lastAttacker);
        }
    }

    @Override
    public void doReward() {
        // Do nothing - hot-overridable
    }
}