/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.stats;

import gameserver.model.gameobjects.Npc;

/**
 * <AUTHOR>
 */
public class NpcPlayerGameStats extends NpcGameStats {
    int currentRunSpeed = 0;

    public NpcPlayerGameStats(Npc owner) {
        super(owner);

        initStat(StatEnum.MAXHP, 100000);
        initStat(StatEnum.MAXMP, 100000);
        initStat(StatEnum.MAXDP, 4000);
        initStat(StatEnum.KNOWLEDGE, 100);
        initStat(StatEnum.POWER, 100);
        
        initStat(StatEnum.ATTACK_SPEED, 2000);
        initStat(StatEnum.ATTACK_RANGE, 2000);
        
        initStat(StatEnum.PHYSICAL_DEFENSE, 1000);
        
        initStat(StatEnum.MAIN_HAND_ACCURACY, 3000);
        initStat(StatEnum.MAIN_HAND_POWER, 1000);

        initStat(StatEnum.WALK, 1500);
        initStat(StatEnum.SPEED, 6000);
    }
}