/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.controllers.attack.AttackStatus;
import gameserver.dataholders.DataManager;
import gameserver.model.EmotionType;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.Summon.SummonMode;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_SUMMON_OWNER_REMOVE;
import gameserver.network.aion.serverpackets.SM_SUMMON_PANEL_REMOVE;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.LifeStatsRestoreService;
import gameserver.skillengine.SkillEngine;
import gameserver.skillengine.model.Skill;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 * <AUTHOR> (Attack-speed hack protection)
 */
public class SummonController extends CreatureController<Summon> {
    private Logger log = Logger.getLogger(SummonController.class);

    private long nextAttackMilis = 0;
    private Set<Integer[]> orderSkills = new HashSet<Integer[]>();

    @Override
    public void notSee(VisibleObject object, boolean isOutOfRange) {
        super.notSee(object, isOutOfRange);
        if (getOwner().getMaster() == null)
            return;

        if (object.getObjectId() == getOwner().getMaster().getObjectId()) {
            release(UnsummonType.DISTANCE);
        }
    }

    @Override
    public Summon getOwner() {
        return (Summon) super.getOwner();
    }

    /**
     * Release summon
     */
    public void release(final UnsummonType unsummonType) {
        final Summon owner = getOwner();

        if (owner.getMode() == SummonMode.RELEASE)
            return;
        owner.setMode(SummonMode.RELEASE);

        final Player master = owner.getMaster();
        final int summonObjId = owner.getObjectId();

        switch (unsummonType) {
            case COMMAND:
                PacketSendUtility.sendPacket(master,
                    SM_SYSTEM_MESSAGE.SUMMON_UNSUMMON(getOwner().getNameId()));
                // PacketSendUtility.sendPacket(master, new SM_SUMMON_UPDATE(getOwner()));
                getOwner().getGameStats().updateVisualStatOnly();
                break;
            case DISTANCE:
                PacketSendUtility.sendPacket(getOwner().getMaster(),
                    SM_SYSTEM_MESSAGE.SUMMON_UNSUMMON_BY_TOO_DISTANCE());
                // PacketSendUtility.sendPacket(master, new SM_SUMMON_UPDATE(getOwner()));
                getOwner().getGameStats().updateVisualStatOnly();
                break;
            case LOGOUT:
            case UNSPECIFIED:
                break;
        }

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                removeSpiritSubstitution((Creature) master);

                if (master != null)
                    master.setSummon(null);

                owner.setMaster(null);

                owner.getController().delete();

                switch (unsummonType) {
                    case COMMAND:
                    case DISTANCE:
                    case UNSPECIFIED:
                        PacketSendUtility.sendPacket(master,
                            SM_SYSTEM_MESSAGE.SUMMON_DISMISSED(getOwner().getNameId()));
                        PacketSendUtility.sendPacket(master,
                            new SM_SUMMON_OWNER_REMOVE(summonObjId));

                        PacketSendUtility.sendPacket(master, new SM_SUMMON_PANEL_REMOVE(getOwner()
                            .getSkillId(), getOwner().getSkillLevel()));
                        break;
                    case LOGOUT:
                        break;
                }
            }
        }, 3000);
    }

    /**
     * Change to rest mode
     */
    public void restMode() {
        getOwner().getController().cancelTask(TaskId.RESTORE);
        getOwner().setMode(SummonMode.REST);
        Player master = getOwner().getMaster();
        PacketSendUtility.sendPacket(master,
            SM_SYSTEM_MESSAGE.SUMMON_RESTMODE(getOwner().getNameId()));
        // PacketSendUtility.sendPacket(master, new SM_SUMMON_UPDATE(getOwner()));
        getOwner().getGameStats().updateVisualStatOnly();
        checkCurrentHp();
    }

    private void checkCurrentHp() {
        if (!getOwner().getLifeStats().isFullyRestoredHp() && getOwner().isSpawned())
            getOwner().getController().addTask(
                TaskId.RESTORE,
                LifeStatsRestoreService.getInstance()
                    .scheduleRestoreTask(getOwner().getLifeStats()));
    }

    /**
     * Change to guard mode
     */
    public void guardMode() {
        getOwner().setMode(SummonMode.GUARD);
        Player master = getOwner().getMaster();
        PacketSendUtility.sendPacket(master,
            SM_SYSTEM_MESSAGE.SUMMON_GUARDMODE(getOwner().getNameId()));
        // PacketSendUtility.sendPacket(master, new SM_SUMMON_UPDATE(getOwner()));
        getOwner().getGameStats().updateVisualStatOnly();
        getOwner().getController().cancelTask(TaskId.RESTORE);
    }

    /**
     * Change to attackMode
     */
    public void attackMode() {
        getOwner().setMode(SummonMode.ATTACK);
        Player master = getOwner().getMaster();
        PacketSendUtility.sendPacket(master,
            SM_SYSTEM_MESSAGE.SUMMON_ATTACKMODE(getOwner().getNameId()));
        // PacketSendUtility.sendPacket(master, new SM_SUMMON_UPDATE(getOwner()));
        getOwner().getGameStats().updateVisualStatOnly();
        getOwner().getController().cancelTask(TaskId.RESTORE);
    }

    @Override
    public void attackTarget(Creature target) {
        super.attackTarget(target);
    }

    @Override
    public void onAttack(Creature creature, int skillId, TYPE type, int damage, int logId,
        AttackStatus status, boolean notifyAttackedObservers, boolean sendPacket) {
        if (getOwner().getLifeStats().isAlreadyDead())
            return;

        // temp
        if (getOwner().getMode() == SummonMode.RELEASE)
            return;

        super.onAttack(creature, skillId, type, damage, logId, status, notifyAttackedObservers,
            sendPacket);
        // PacketSendUtility.sendPacket(getOwner().getMaster(), new SM_SUMMON_UPDATE(getOwner()));
        getOwner().getGameStats().updateVisualStatOnly();
    }

    @Override
    public void onDie(Creature lastAttacker) {
        super.onDie(lastAttacker);
        release(UnsummonType.UNSPECIFIED);
        Summon owner = getOwner();
        removeSpiritSubstitution((Creature) owner.getMaster());
        PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner, EmotionType.DIE, 0,
            lastAttacker == null ? 0 : lastAttacker.getObjectId()));
    }

    public void useSkill(int skillId, Creature target) {
        Creature creature = getOwner();

        boolean petHasSkill = DataManager.PET_SKILL_DATA.petHasSkill(getOwner().getObjectTemplate()
            .getTemplateId(), skillId);
        if (!petHasSkill)
            return;

        Skill skill = SkillEngine.getInstance().getSkill(creature, skillId, 1, target);
        if (skill != null) {
            skill.useSkill();
        }
    }

    public void removeSpiritSubstitution(Creature master) {
        List<Integer> skillIds = new ArrayList<Integer>();
        for (int skillid = 18262; skillid < 18295; ++skillid) {
            skillIds.add(skillid);
        }

        if (master != null) {
            master.getEffectController().removeEffects(skillIds);
            master.getEffectController().removeSummonEffects(getOwner());
        }
    }

    public static enum UnsummonType {
        LOGOUT,
        DISTANCE,
        COMMAND,
        UNSPECIFIED
    }

    /**
     * @param orderSkill
     *            [skillId, cooldown]
     */
    public void queueOrderSkill(Integer[] orderSkill) {
        orderSkills.add(orderSkill);
    }

    /**
     * @param lastAttackMilis
     *            the lastAttackMilis to set
     */
    public void setNextAttackMilis(long nextAttackMilis) {
        this.nextAttackMilis = nextAttackMilis;
    }

    /**
     * @return the lastAttackMilis
     */
    public long getNextAttackMilis() {
        return nextAttackMilis;
    }

    public boolean checkSkillPacket(int skillId, Creature target) {
        Skill skill = SkillEngine.getInstance().getSkill(getOwner(), skillId, 1, target);
        long skillCooldownTime = getOwner().getSkillCoolDown(skill.getSkillTemplate().getDelayId());

        if (System.currentTimeMillis() < skillCooldownTime) {
            log.info("[AUDIT] " + getOwner().getMaster().getName()
                + " CM_SUMMON_CASTSPELL packet hack. Packet force send. COOLDOWN AVOID.");
            return false;
        }

        Integer[] toRemove = null;
        for (Integer[] orderSkill : orderSkills) {
            int rightSkillId = DataManager.PET_SKILL_DATA.getPetOrderSkill(orderSkill[0],
                getOwner().getNpcId());

            if (skillId == rightSkillId) {
                getOwner().setSkillCoolDown(skill.getSkillTemplate().getDelayId(),
                    orderSkill[1] - 2000 + System.currentTimeMillis());
                toRemove = orderSkill;
                break;
            }
        }

        if (toRemove != null) {
            orderSkills.remove(toRemove);
            return true;
        }

        log.info("[AUDIT] " + getOwner().getMaster().getName() + " CM_SUMMON_CASTSPELL skillId "
            + skillId + " (npcId " + getOwner().getNpcId() + ")");
        return false;
    }
}
