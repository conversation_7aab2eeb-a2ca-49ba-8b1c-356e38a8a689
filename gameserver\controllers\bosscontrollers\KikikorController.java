/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.ChatType;
import gameserver.model.gameobjects.Creature;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

/**
 * <AUTHOR>
 * 
 */
public class <PERSON><PERSON><PERSON><PERSON><PERSON>roller extends BossController {
    private long nextShout = 0;

    public KikikorController() {
        super(205848, true);
    }

    protected void think() {
        Creature owner = getOwner();

        /*if (System.currentTimeMillis() < nextShout)
            return;

        if (owner.getKnownList().getPlayersCount() > 0) {
            shout();
        }*/
    }

    private void shout() {
        nextShout = System.currentTimeMillis() + 120000;

        PacketSendUtility
            .broadcastPacket(getOwner(), new SM_MESSAGE(getOwner().getObjectId(), "Kikikor",
                "Welcome to Beluslan! You can bind here at the Obelisk.", ChatType.ALLIANCE));

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                PacketSendUtility.broadcastPacket(getOwner(), new SM_MESSAGE(getOwner()
                    .getObjectId(), "Kikikor",
                    "Want to go back to Gelkmaros? Just enter the Dimensional Vortex!",
                    ChatType.ALLIANCE));
            }
        }, 10000);
    }
}
