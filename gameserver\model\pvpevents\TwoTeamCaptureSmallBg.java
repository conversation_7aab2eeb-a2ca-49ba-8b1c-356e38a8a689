/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.controllers.NpcController;
import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.model.templates.spawn.SpawnGroup;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_DELETE;
import gameserver.network.aion.serverpackets.SM_NPC_INFO;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.idfactory.IDFactory;
import gameserver.world.Executor;
import gameserver.world.World;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
public class TwoTeamCaptureSmallBg extends Battleground {
    private int extraCounter = 0;
    private Map<Integer, Integer> teamPoints = new ConcurrentHashMap<Integer, Integer>();

    private static final int NPC_CAPTURE_POINT = 283130;

    private static final int INITIAL_DELAY = 30;

    private static final int CAPTURE_POINT_RADIUS = 5;
    private static final int CAPTURE_POINT_DELAY = 10 * 1000;

    private static final int CAPTURE_POINT_RESET_DELAY = 120 * 1000;

    private static final int POINTS_TO_WIN = 90;

    private static final int NEUTRAL = -1;
    private static final int BLUE_TEAM = 0;
    private static final int GREEN_TEAM = 1;

    private Npc[] points = new Npc[2];
    private Npc[] flags = new Npc[2];
    private int[] owners = new int[2];
    private ScheduledFuture<?>[] tasks = new ScheduledFuture<?>[2];
    private int[] taskOwners = new int[2];
    private long[] lastActivities = new long[2];
    private ReentrantLock[] locks = new ReentrantLock[2];

    private boolean endCalled = false;

    public TwoTeamCaptureSmallBg() {
        super.name = "2-Team Capture Small";
        super.description = "You are on a team and must capture points to gain points. You capture points by standing on them. You will respawn at your base every 30 seconds. If a team reaches "
            + POINTS_TO_WIN + " they win.";
        super.minSize = 3;
        super.maxSize = 4;
        super.teamCount = 2;
        super.matchLength = 300 + INITIAL_DELAY;

        BattlegroundMap map1 = new BattlegroundMap(300240000);
        map1.addSpawn(new SpawnPosition(572.3f, 446.1f, 648f));
        map1.addSpawn(new SpawnPosition(475f, 446f, 648f));
        map1.setKillZ(645f);

        super.maps.add(map1);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    private void spawnStuff() {
        if (super.getMapId() == 300240000) {
            points[0] = (Npc) spawn(NPC_CAPTURE_POINT, 521.9f, 428.0f, 647.91565f);
            points[1] = (Npc) spawn(NPC_CAPTURE_POINT, 523.7f, 485.0f, 649.916f);
        }

        for (int i = 0; i < points.length; i++) {
            locks[i] = new ReentrantLock();
        }

        Arrays.fill(owners, NEUTRAL);
        Arrays.fill(taskOwners, NEUTRAL);
        Arrays.fill(lastActivities, 0);
    }

    public void startMatch() {
        super.createInstance();

        spawnStuff();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, INITIAL_DELAY * 1000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", INITIAL_DELAY * 1000);

        startBattleground(INITIAL_DELAY * 1000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups(0) <= 1)
                    endTwoTeamCaptureMatch();
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamCaptureMatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                showPointsOnMap();

                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    @Override
                    public void run() {
                        extraCounter++;

                        handleCapturePoints();

                        if ((extraCounter % 5) == 0) {
                            givePointsToOwners();
                        }

                        if ((extraCounter % 15) == 0) {
                            showPointsOnMap();
                            showTeamPositions();
                        }

                        if ((extraCounter % 30) == 0) {
                            extraCounter = 0;
                            autoResurrection();
                        }
                    }
                }, 1 * 1000, 1 * 1000));
            }
        }, INITIAL_DELAY * 1000);
    }

    private void handleCapturePoints() {
        for (int _i = 0; _i < points.length; _i++) {
            final int i = _i;

            locks[i].lock();

            try {
                if (points[i] != null) {
                    int blueTeam = 0;
                    int greenTeam = 0;

                    for (PlayerGroup group : getGroups()) {
                        for (Player pl : group.getMembers()) {
                            if (!pl.getLifeStats().isAlreadyDead()
                                && MathUtil.isIn3dRange(points[i], pl, CAPTURE_POINT_RADIUS)) {
                                if (group.getBgIndex() == BLUE_TEAM)
                                    blueTeam++;
                                else if (group.getBgIndex() == GREEN_TEAM)
                                    greenTeam++;
                            }
                        }
                    }

                    if (blueTeam > 0 || greenTeam > 0)
                        lastActivities[i] = System.currentTimeMillis();

                    int check = blueTeam - greenTeam;

                    if (check == 0) {
                        if (tasks[i] != null) {
                            if (taskOwners[i] != NEUTRAL) {
                                announceAll(LadderService.getInstance().getNameByIndex(
                                    taskOwners[i])
                                    + "'s attempt to capture a point failed.");
                            }

                            taskOwners[i] = NEUTRAL;
                            tasks[i].cancel(true);
                            tasks[i] = null;
                        }
                    }
                    else if (check > 0) {
                        if (taskOwners[i] != BLUE_TEAM && tasks[i] != null) {
                            announceAll(LadderService.getInstance().getNameByIndex(taskOwners[i])
                                + "'s attempt to capture a point failed.");

                            taskOwners[i] = NEUTRAL;
                            tasks[i].cancel(true);
                            tasks[i] = null;
                        }

                        if (taskOwners[i] != BLUE_TEAM && owners[i] != BLUE_TEAM) {
                            if (owners[i] != NEUTRAL) {
                                announceAll(LadderService.getInstance().getNameByIndex(BLUE_TEAM)
                                    + " is neutralizing a point.");

                                taskOwners[i] = BLUE_TEAM;
                                tasks[i] = ThreadPoolManager.getInstance().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        announceAll(LadderService.getInstance().getNameByIndex(
                                            BLUE_TEAM)
                                            + " has neutralized a point!");

                                        locks[i].lock();

                                        try {
                                            owners[i] = NEUTRAL;
                                            taskOwners[i] = NEUTRAL;
                                            tasks[i] = null;
                                        }
                                        finally {
                                            locks[i].unlock();
                                        }

                                        updatePoint(i);
                                    }
                                }, CAPTURE_POINT_DELAY / 2);
                            }
                            else {
                                announceAll(LadderService.getInstance().getNameByIndex(BLUE_TEAM)
                                    + " is capturing a point.");

                                taskOwners[i] = BLUE_TEAM;
                                tasks[i] = ThreadPoolManager.getInstance().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        announceAll(LadderService.getInstance().getNameByIndex(
                                            BLUE_TEAM)
                                            + " has captured a point!");

                                        locks[i].lock();

                                        try {
                                            owners[i] = BLUE_TEAM;
                                            taskOwners[i] = NEUTRAL;
                                            tasks[i] = null;
                                        }
                                        finally {
                                            locks[i].unlock();
                                        }

                                        updatePoint(i);
                                    }
                                }, CAPTURE_POINT_DELAY);
                            }
                        }
                    }
                    else if (check < 0) {
                        if (taskOwners[i] != GREEN_TEAM && tasks[i] != null) {
                            announceAll(LadderService.getInstance().getNameByIndex(taskOwners[i])
                                + "'s attempt to capture a point failed.");

                            taskOwners[i] = NEUTRAL;
                            tasks[i].cancel(true);
                            tasks[i] = null;
                        }

                        if (taskOwners[i] != GREEN_TEAM && owners[i] != GREEN_TEAM) {
                            if (owners[i] != NEUTRAL) {
                                announceAll(LadderService.getInstance().getNameByIndex(GREEN_TEAM)
                                    + " is neutralizing a point.");

                                taskOwners[i] = GREEN_TEAM;
                                tasks[i] = ThreadPoolManager.getInstance().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        announceAll(LadderService.getInstance().getNameByIndex(
                                            GREEN_TEAM)
                                            + " has neutralized a point!");

                                        locks[i].lock();

                                        try {
                                            owners[i] = NEUTRAL;
                                            taskOwners[i] = NEUTRAL;
                                            tasks[i] = null;
                                        }
                                        finally {
                                            locks[i].unlock();
                                        }

                                        updatePoint(i);
                                    }
                                }, CAPTURE_POINT_DELAY / 2);
                            }
                            else {
                                announceAll(LadderService.getInstance().getNameByIndex(GREEN_TEAM)
                                    + " is capturing a point.");

                                taskOwners[i] = GREEN_TEAM;
                                tasks[i] = ThreadPoolManager.getInstance().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        announceAll(LadderService.getInstance().getNameByIndex(
                                            GREEN_TEAM)
                                            + " has captured a point!");

                                        locks[i].lock();

                                        try {
                                            owners[i] = GREEN_TEAM;
                                            taskOwners[i] = NEUTRAL;
                                            tasks[i] = null;
                                        }
                                        finally {
                                            locks[i].unlock();
                                        }

                                        updatePoint(i);
                                    }
                                }, CAPTURE_POINT_DELAY);
                            }
                        }
                    }

                    if (owners[i] != NEUTRAL
                        && (System.currentTimeMillis() - lastActivities[i]) >= CAPTURE_POINT_RESET_DELAY) {
                        announceAll("A " + LadderService.getInstance().getNameByIndex(owners[i])
                            + " capture point reset to neutral status.");

                        owners[i] = NEUTRAL;
                        updatePoint(i);
                    }
                }
            }
            catch (Exception e) {
                log.error("Error in CapturePoints handling: ", e);
            }
            finally {
                locks[i].unlock();
            }
        }
    }

    private int getNpcIdForFlag(int bgIndex) {
        switch (bgIndex) {
            case 0:
                return 801601;
            case 1:
                return 801600;
        }

        return 801602;
    }

    private Npc createFlag(int npcId, VisibleObject loc) {
        SpawnTemplate spawn = new SpawnTemplate(loc.getX(), loc.getY(), loc.getZ(), (byte) 0, 0, 0,
            0);
        SpawnGroup spawnGroup = new SpawnGroup(this.getMapId(), npcId, 295, 1);
        spawn.setSpawnGroup(spawnGroup);
        spawnGroup.getObjects().add(spawn);

        final Npc flag = new Npc(IDFactory.getInstance().nextPlayerId(), new NpcController(),
            spawn, DataManager.NPC_DATA.getNpcTemplate(npcId));

        World.getInstance().setPosition(flag, mapId, this.getInstanceId(), loc.getX(), loc.getY(),
            loc.getZ(), (byte) 0);

        flag.setOutpostFlag(true);

        this.getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                PacketSendUtility.sendPacket(pl, new SM_NPC_INFO(flag, pl));
                return true;
            }
        }, true);

        return flag;
    }

    private void destroyFlag(Npc flag) {
        if (flag == null)
            return;

        final SM_DELETE pck = new SM_DELETE(flag, 19);

        this.getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                PacketSendUtility.sendPacket(pl, pck);
                return true;
            }
        }, true);
    }

    private void showPointsOnMap() {
        for (int i = 0; i < flags.length; i++) {
            updatePoint(i);
        }
    }

    private void updatePoint(int index) {
        if (index < 0 || index >= flags.length)
            return;

        destroyFlag(flags[index]);
        flags[index] = createFlag(getNpcIdForFlag(owners[index]), points[index]);
    }

    private void givePointsToOwners() {
        for (int i = 0; i < owners.length; i++)
            if (owners[i] != -1)
                addPoints(owners[i], 1);
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    private void addPoints(int bgIndex, int points) {
        if (bgIndex < 0)
            return;

        if (!teamPoints.containsKey(bgIndex))
            teamPoints.put(bgIndex, 0);

        int result = teamPoints.get(bgIndex);
        teamPoints.put(bgIndex, result + points);

        if (getTeamPoints(bgIndex) >= POINTS_TO_WIN)
            endTwoTeamCaptureMatch();
    }

    private void showTeamPositions() {
        String msg = "";
        msg += String.format("Blue: %d - Green: %d", getTeamPoints(0), getTeamPoints(1));

        for (PlayerGroup group : super.getGroups())
            for (Player pl : group.getMembers())
                super.scheduleAnnouncement(pl, msg, 0);

        super.specAnnounce(msg);
    }

    private void autoResurrection() {
        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead()) {
                    pl.getReviveController().fullRevive();
                    super.spawnProtection(pl);
                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    TeleportService.teleportTo(pl, getMapId(), super.getInstanceId(), pos.getX(),
                        pos.getY(), pos.getZ(), 0);
                    super
                        .scheduleAnnouncement(pl, "You have been automatically resurrected!", 1500);
                }
            }
        }
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super.scheduleAnnouncement(player, "You will automatically resurrect every 30 seconds.", 0);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;

            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in TwoTeamCapturePointsBg!");
            }
            else {
                addPoints(killer.getPlayerGroup().getBgIndex(), 3);

                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 2);
            }
        }
    }

    @Override
    public boolean createTournament(List<List<Player>> teams) {
        if (!super.createGroups(teams))
            return false;

        startMatch();

        return true;
    }

    private int getTeamPoints(int teamIndex) {
        if (!teamPoints.containsKey((Integer) teamIndex))
            return 0;

        return teamPoints.get((Integer) teamIndex);
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups(0) <= 1)
            endTwoTeamCaptureMatch();
    }

    private void endTwoTeamCaptureMatch() {
        if (endCalled)
            return;

        endCalled = true;

        super.onEndFirstDefault();

        PlayerGroup winner = null;
        boolean isDraw = false;
        if (getTeamPoints(0) == getTeamPoints(1))
            isDraw = true;
        else if (getTeamPoints(0) > getTeamPoints(1))
            winner = super.getGroups().get(0);
        else if (getTeamPoints(1) > getTeamPoints(0))
            winner = super.getGroups().get(1);

        String score = getTeamPoints(0) + " : " + getTeamPoints(1);

        if (isDraw) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.scheduleAnnouncement(pl,
                        "The battleground has ended in a draw with the score " + score + "!", 0);
                    super.scheduleAnnouncement(pl,
                        "For your effort you have been rewarded with some might.", 3000);
                    super.rewardPlayer(pl, 20, false);
                }
            }
            super.specAnnounce("The battleground has ended in a draw!");
        }
        else if (winner != null) {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    if (group.getObjectId() == winner.getObjectId()) {
                        int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2 : 1;

                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE / 2);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.scheduleAnnouncement(pl,
                            "Your team has won the match with the score " + score + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);

                        super.rewardPlayer(pl, 30, true);
                    }
                    else {
                        super.playerLoseMatch(pl, -super.K_VALUE / 3);

                        super.scheduleAnnouncement(pl,
                            "Your team has lost the match with the score " + score + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);

                        super.rewardPlayer(pl, 20, false);
                    }
                }
            }

            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match with the score " + score + "!");
        }
        else {
            log.error("No winner or draw in a TwoTeamCaptureSmallBg!??!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}