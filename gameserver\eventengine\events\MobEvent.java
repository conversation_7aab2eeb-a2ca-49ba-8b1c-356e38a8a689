/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.eventengine.Event;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Monster;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.services.PvpService;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.World;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;

/**
 * <AUTHOR>
 * 
 */
public abstract class MobEvent extends Event {
    protected ScheduledFuture<?> endTask = null;

    protected Map<Integer, Integer> playerPoints = new HashMap<Integer, Integer>();
    protected List<Integer> eventSpawns = new ArrayList<Integer>();

    protected int creatureKillCount = 0;
    protected boolean ended = false;

    protected int mapId = 0;
    protected SpawnPosition center = new SpawnPosition(0, 0, 0);
    protected int apBasePlayer = 0;
    protected int apPoolPerPlayer = 0;
    protected int mightBasePlayer = 0;
    protected int mightPoolPerPlayer = 0;

    protected abstract void spawnEventMobs();

    public void onCreatureDie(Creature lastAttacker) {
        Creature killer = lastAttacker;

        if (killer != null) {
            if (killer.getMaster() != null)
                killer = killer.getMaster();

            if (killer instanceof Player) {
                Player pl = (Player) killer;
                synchronized (playerPoints) {
                    if (playerPoints.containsKey(pl.getObjectId())) {
                        playerPoints.put(pl.getObjectId(), playerPoints.get(pl.getObjectId()) + 1);
                        PacketSendUtility.sendMessage(pl, "Your score is now "
                            + playerPoints.get(pl.getObjectId()) + " points.");
                    }
                    else {
                        announce(pl, "You cannot participate in the event if you enter too late!");
                    }
                }
            }
        }

        creatureKillCount++;
        if (creatureKillCount >= eventSpawns.size())
            endEvent();
    }

    public void onPlayerDie(Creature lastAttacker) {
        if (lastAttacker == null)
            return;

        Creature killer = lastAttacker;

        if (killer.getMaster() != null)
            killer = killer.getMaster();

        if (!(killer instanceof Player) || killer.getWorldId() != mapId)
            return;

        Player pl = (Player) killer;
        synchronized (playerPoints) {
            if (playerPoints.containsKey(pl.getObjectId())) {
                playerPoints.put(pl.getObjectId(), playerPoints.get(pl.getObjectId()) + 3);
                PacketSendUtility.sendMessage(pl, "Your score is now "
                    + playerPoints.get(pl.getObjectId()) + " points.");
            }
        }
    }

    protected void endEvent() {
        if (ended)
            return;

        ended = true;

        if (endTask != null)
            endTask.cancel(false);

        for (Integer objectId : eventSpawns) {
            AionObject obj = World.getInstance().findAionObject(objectId);

            if (obj instanceof VisibleObject) {
                ((VisibleObject) obj).getController().delete();
            }
        }

        eventSpawns.clear();

        int totalPoints = 0;

        List<Integer> playersToRemove = new ArrayList<Integer>();
        for (Map.Entry<Integer, Integer> entry : playerPoints.entrySet()) {
            if (entry.getValue() > 0)
                totalPoints += entry.getValue();
            else
                playersToRemove.add(entry.getKey());
        }

        for (Integer objId : playersToRemove)
            playerPoints.remove(objId);

        int mightPool = mightPoolPerPlayer * playerPoints.size();
        int apPool = apPoolPerPlayer * playerPoints.size();

        for (Map.Entry<Integer, Integer> entry : playerPoints.entrySet()) {
            Player pl = World.getInstance().findPlayer(entry.getKey());
            if (pl == null)
                continue;

            pl.setEvent(null);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 0));

            announce(pl, "The event has ended! You have been rewarded based on your effort.");

            // Reward AP
            int ap = apBasePlayer + (entry.getValue() * apPool) / totalPoints;
            pl.getCommonData().addAp(ap);

            // Reward might
            int might = mightBasePlayer + (entry.getValue() * mightPool) / totalPoints;
            PvpService.getInstance().addMight(pl, might);
        }

        super.finish();
    }

    protected Monster spawnMob(int npcId, float x, float y, float z) {
        Monster mob = SpawnEngine.getInstance().spawnEventMob(this, mapId, npcId, x, y,
            GeoEngine2.getInstance().getZ(mapId, x, y, z));

        if (mob != null)
            eventSpawns.add(mob.getObjectId());
        return mob;
    }

    protected void scheduleEnd(int seconds) {
        endTask = ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endEvent();
            }
        }, seconds * 1000);
    }

    protected Collection<Player> getAvailablePlayers() {
        Collection<Player> players = World.getInstance().getWorldMap(mapId).getWorldMapInstance()
            .getPlayers();
        Collection<Player> available = new ArrayList<Player>(players.size());

        for (Player player : players)
            if (MathUtil.getDistance(player, center.getX(), center.getY(), center.getZ()) < 300)
                available.add(player);

        return available;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#onReset()
     */
    @Override
    protected void onReset() {
        if (endTask != null)
            endTask.cancel(true);

        playerPoints.clear();
        eventSpawns.clear();
        creatureKillCount = 0;
        ended = false;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#cancel(boolean)
     */
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return false;
    }
}
