/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.configs.administration.AdminConfig;
import gameserver.configs.main.CustomConfig;
import gameserver.configs.main.GSConfig;
import gameserver.configs.network.NetworkConfig;
import gameserver.controllers.SummonController.UnsummonType;
import gameserver.controllers.attack.AttackStatus;
import gameserver.dataholders.DataManager;
import gameserver.eventengine.events.MobEvent;
import gameserver.model.EmotionType;
import gameserver.model.Race;
import gameserver.model.TaskId;
import gameserver.model.alliance.PlayerAllianceEvent;
import gameserver.model.alliance.PlayerAllianceMember;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.DecorationObject;
import gameserver.model.gameobjects.Gatherable;
import gameserver.model.gameobjects.GroupGate;
import gameserver.model.gameobjects.Kisk;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.NpcPlayer;
import gameserver.model.gameobjects.StaticObject;
import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.SkillListEntry;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.gameobjects.state.CreatureVisualState;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.group.GroupEvent;
import gameserver.model.templates.quest.QuestItems;
import gameserver.model.templates.stats.PlayerStatsTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_CUBE_UPDATE;
import gameserver.network.aion.serverpackets.SM_DELETE;
import gameserver.network.aion.serverpackets.SM_DIE;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_GATHERABLE_INFO;
import gameserver.network.aion.serverpackets.SM_HOUSE_DECORATION_DELETE;
import gameserver.network.aion.serverpackets.SM_HOUSE_DECORATION_SPAWN;
import gameserver.network.aion.serverpackets.SM_ITEM_USAGE_ANIMATION;
import gameserver.network.aion.serverpackets.SM_KISK_UPDATE;
import gameserver.network.aion.serverpackets.SM_LEVEL_UPDATE;
import gameserver.network.aion.serverpackets.SM_MOTION;
import gameserver.network.aion.serverpackets.SM_NAME_CHANGE;
import gameserver.network.aion.serverpackets.SM_NEARBY_QUESTS;
import gameserver.network.aion.serverpackets.SM_NPC_INFO;
import gameserver.network.aion.serverpackets.SM_PET;
import gameserver.network.aion.serverpackets.SM_PLAYER_INFO;
import gameserver.network.aion.serverpackets.SM_PLAYER_STATE;
import gameserver.network.aion.serverpackets.SM_PRIVATE_STORE;
import gameserver.network.aion.serverpackets.SM_QUEST_LIST;
import gameserver.network.aion.serverpackets.SM_SKILL_CANCEL;
import gameserver.network.aion.serverpackets.SM_STANCE_STATE;
import gameserver.network.aion.serverpackets.SM_STATS_INFO;
import gameserver.network.aion.serverpackets.SM_SUMMON_PANEL;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_TARGET_SELECTED;
import gameserver.network.aion.serverpackets.SM_TARGET_UPDATE;
import gameserver.questEngine.QuestEngine;
import gameserver.questEngine.model.QuestCookie;
import gameserver.restrictions.RestrictionsManager;
import gameserver.services.AllianceService;
import gameserver.services.ArenaService;
import gameserver.services.ClassChangeService;
import gameserver.services.CustomSkillsService;
import gameserver.services.DuelService;
import gameserver.services.HTMLService;
import gameserver.services.InstanceService;
import gameserver.services.ItemService;
import gameserver.services.LegionService;
import gameserver.services.MountService;
import gameserver.services.OutlawService;
import gameserver.services.PolymorphService;
import gameserver.services.PvpService;
import gameserver.services.QuestService;
import gameserver.services.SerialService;
import gameserver.services.SkillLearnService;
import gameserver.services.TeleportService;
import gameserver.services.TownArenaService;
import gameserver.services.ToyPetService;
import gameserver.services.ZoneService;
import gameserver.services.ZoneService.ZoneUpdateMode;
import gameserver.skillengine.SkillEngine;
import gameserver.skillengine.model.DispelCategoryType;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.HealType;
import gameserver.skillengine.model.Skill;
import gameserver.skillengine.model.Skill.SkillActivationType;
import gameserver.skillengine.model.SkillTargetSlot;
import gameserver.skillengine.properties.FirstTargetAttribute;
import gameserver.skillengine.properties.TargetRelationAttribute;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;
import gameserver.world.World;
import gameserver.world.WorldMapInstance;
import gameserver.world.WorldMapType;
import gameserver.world.WorldType;
import gameserver.world.zone.ZoneInstance;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.log4j.Logger;

import com.aionemu.commons.utils.Rnd;

/**
 * This class is for controlling players.
 * 
 * <AUTHOR> ATracer (2009-09-29), xavier, Sarynth
 * <AUTHOR> (Attack-speed hack protection)
 */
public class PlayerController extends CreatureController<Player> {
    private Logger log = Logger.getLogger(PlayerController.class);

    private boolean isInShutdownProgress = false;

    private boolean canAutoRevive = true;

    private volatile byte zoneUpdateMask;

    private AtomicBoolean didReward = new AtomicBoolean(false);

    private long dieTime = 0;

    /**
     * {@inheritDoc}
     */
    @Override
    public void see(VisibleObject object) {
        super.see(object);
        if (object instanceof Player) {
            final Player player = (Player) object;

            if (!player.isOnline())
                player.getController().delete();
            else {
                PacketSendUtility.sendPacket(getOwner(),
                    new SM_PLAYER_INFO(player,
                        (player.isInSafeZone() || getOwner().isInSafeZone()) ? player
                            .getCommonData().getRace() != getOwner().getCommonData().getRace()
                            : getOwner().isEnemyPlayer(player)));
                PacketSendUtility.sendPacket(getOwner(), new SM_MOTION(player));

                if (player.getToyPet() != null)
                    PacketSendUtility.sendPacket(getOwner(), new SM_PET(3, player.getToyPet()));

                // getOwner().getEffectController().sendEffectIconsTo(player);
                player.getEffectController().sendEffectIconsTo(getOwner());

                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        if (getOwner().getKnownList().knows(player) && player.isUsingMount()) {
                            PacketSendUtility.sendPacket(getOwner(), new SM_EMOTION(player,
                                EmotionType.START_EMOTE2, 0, 0));
                            PacketSendUtility.sendPacket(getOwner(), new SM_EMOTION(player,
                                EmotionType.MOUNT_SPAWN, player.getMountId(), player.getGameStats()
                                    .getCurrentStat(StatEnum.SPEED) / 1000f, 0));
                        }
                    }
                }, 1000);
            }
        }
        else if (object instanceof Kisk) {
            Kisk kisk = ((Kisk) object);
            PacketSendUtility.sendPacket(getOwner(), new SM_NPC_INFO(getOwner(), kisk));
            if (getOwner().getCommonData().getRace() == kisk.getOwnerRace())
                PacketSendUtility.sendPacket(getOwner(), new SM_KISK_UPDATE(kisk));
        }
        else if (object instanceof GroupGate) {
            GroupGate groupgate = ((GroupGate) object);
            PacketSendUtility.sendPacket(getOwner(), new SM_NPC_INFO(getOwner(), groupgate));
        }
        else if (object instanceof NpcPlayer) {
            NpcPlayer npcPlayer = ((NpcPlayer) object);

            PacketSendUtility.sendPacket(getOwner(), new SM_PLAYER_INFO(npcPlayer, getOwner()
                .isEnemy(npcPlayer)));
        }
        else if (object instanceof Npc) {
            boolean update = false;
            Npc npc = ((Npc) object);

            PacketSendUtility.sendPacket(getOwner(), new SM_NPC_INFO(npc, getOwner()));

            if (npc.getCustomName() != null && !npc.getCustomName().isEmpty())
                PacketSendUtility.sendPacket(getOwner(),
                    new SM_NAME_CHANGE(npc.getObjectId(), npc.getName(), npc.getCustomName()));

            for (int questId : QuestEngine.getInstance().getNpcQuestData(npc.getNpcId())
                .getOnQuestStart()) {
                if (QuestService
                    .checkStartCondition(new QuestCookie(object, getOwner(), questId, 0))) {
                    if (!getOwner().getNearbyQuests().contains(questId)) {
                        update = true;
                        getOwner().getNearbyQuests().add(questId);
                    }
                }
            }

            if (update)
                updateNearbyQuestList();

            WorldMapInstance instance = InstanceService.getRegisteredInstance(getOwner()
                .getWorldId(), getOwner().getObjectId());
            if (instance != null) {
                int doorId = instance.getDoor(object.getObjectId());
                if (doorId != -1)
                    PacketSendUtility.sendPacket(getOwner(), new SM_EMOTION(doorId));
            }
        }
        else if (object instanceof Summon) {
            Summon npc = ((Summon) object);
            PacketSendUtility.sendPacket(getOwner(), new SM_NPC_INFO(npc, getOwner()));
        }
        else if (object instanceof Gatherable || object instanceof StaticObject) {
            PacketSendUtility.sendPacket(getOwner(), new SM_GATHERABLE_INFO(object));
        }
        else if (object instanceof DecorationObject) {
            PacketSendUtility.sendPacket(getOwner(), new SM_HOUSE_DECORATION_SPAWN(
                (DecorationObject) object));
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void notSee(VisibleObject object, boolean isOutOfRange) {
        super.notSee(object, isOutOfRange);

        if (object instanceof NpcPlayer) {
            PacketSendUtility.sendPacket(getOwner(), new SM_DELETE(object, isOutOfRange ? 0 : 15));
            return;
        }
        else if (object instanceof Npc) {
            boolean update = false;

            if (object.getObjectTemplate() != null) {
                for (int questId : QuestEngine.getInstance()
                    .getNpcQuestData(((Npc) object).getNpcId()).getOnQuestStart()) {
                    if (QuestService.checkStartCondition(new QuestCookie(object, getOwner(),
                        questId, 0))) {
                        if (getOwner().getNearbyQuests().contains(questId)) {
                            update = true;
                            getOwner().getNearbyQuests().remove(
                                getOwner().getNearbyQuests().indexOf(questId));
                        }
                    }
                }
            }

            if (update)
                updateNearbyQuestList();

            if (((Npc) object).isOutpostFlag())
                PacketSendUtility.sendPacket(getOwner(), new SM_DELETE(object, 19));
            else
                PacketSendUtility.sendPacket(getOwner(), new SM_DELETE(object, isOutOfRange ? 0
                    : 15));

            return;
        }
        else if (object instanceof DecorationObject) {
            PacketSendUtility.sendPacket(getOwner(),
                new SM_HOUSE_DECORATION_DELETE(object.getObjectId()));
            return;
        }
        else if (object instanceof Player) {
            Player player = (Player) object;

            if (player.getToyPet() != null)
                PacketSendUtility.sendPacket(getOwner(), new SM_PET(4, player.getToyPet()
                    .getDatabaseIndex()));

            if (player.isUsingMount()) {
                PacketSendUtility.sendPacket(getOwner(), new SM_EMOTION(player,
                    EmotionType.START_EMOTE2, 0, 0));
                PacketSendUtility.sendPacket(getOwner(),
                    new SM_EMOTION(player, EmotionType.MOUNT_DESPAWN, 0, player.getGameStats()
                        .getCurrentStat(StatEnum.SPEED) / 1000f, 0));
            }
        }

        PacketSendUtility.sendPacket(getOwner(), new SM_DELETE(object, 0));

        if (getOwner().isTargeting(object.getObjectId())) {
            getOwner().setTarget(null);
            PacketSendUtility.sendPacket(getOwner(), new SM_TARGET_SELECTED(getOwner()));
            PacketSendUtility.broadcastPacket(getOwner(), new SM_TARGET_UPDATE(getOwner()));
        }
    }

    public void updateNearbyQuests() {
        getOwner().getNearbyQuests().clear();

        getOwner().getKnownList().doOnAllNpcs(new Executor<Npc>() {
            @Override
            public boolean run(Npc obj) {
                for (int questId : QuestEngine.getInstance()
                    .getNpcQuestData(((Npc) obj).getNpcId()).getOnQuestStart()) {
                    if (QuestService.checkStartCondition(new QuestCookie(obj, getOwner(), questId,
                        0))) {
                        if (!getOwner().getNearbyQuests().contains(questId)) {
                            getOwner().getNearbyQuests().add(questId);
                        }
                    }
                }
                return true;
            }
        }, true);

        updateNearbyQuestList();
    }

    /**
     * Set zone instance as null (Where no zones defined)
     */
    public void resetZone() {
        getOwner().setZoneInstance(null);
        getOwner().setInZergingZone(false);
        CustomSkillsService.getInstance().onEnterZone(getOwner());
    }

    public void onEnterWorld() {
        // remove abyss transformation if worldtype != abyss && worldtype != balaurea
        for (Effect ef : getOwner().getEffectController().getAbnormalEffects()) {
            if (ef.isAvatar()) {
                if (getOwner().getBattleground() != null
                    || (getOwner().getWorldType() != WorldType.ABYSS && getOwner().getWorldType() != WorldType.BALAUREA)) {
                    getOwner().getEffectController().removeEffect(ef.getSkillId());
                    getOwner().getEffectController().removeEffect(ef.getLaunchSkillId());
                    break;
                }
            }
        }

        // Map<Integer, SiegeLocation> fortresses = DataManager.SIEGE_LOCATION_DATA.getSiegeLocations();
        // for (SiegeLocation fort : fortresses.values())
        // if (fort.getWorldId() == getOwner().getWorldId())
        // PacketSendUtility.sendPacket(getOwner(), new SM_SHIELD_EFFECT(fort.getLocationId(), true));
    }

    /**
     * {@inheritDoc}
     * <p/>
     * Should only be triggered from one place (life stats)
     */
    @Override
    public void onDie(Creature lastAttacker) {
        final Player player = this.getOwner();

        this.cancelCurrentAction();

        Player master = player.getAggroList().getWinner(lastAttacker);

        PolymorphService.endPolymorphs(player);

        if (player.getBattleground() != null) {
            // Wintersday Event FLUFFY SNOW CRYSTAL
            // if (Rnd.get(100) < 20 && master != player && master != null) {
            // ItemService.addItem(master, 186000170, 1);
            // PacketSendUtility.sendMessage(master,
            // "You have received a wintery [item: 186000170]!");
            // }

            // Wintersday Event SNOWMAN GIFT BOX
            // if (Rnd.get(100) < 1 && master != player && master != null) {
            // ItemService.addItem(master, 188051842, 1);
            // PacketSendUtility.sendMessage(master,
            // "You have received a wintery [item: 188051842]!");
            // }

            // Halloween Event PUMPKIN COOKIE
            // if (Rnd.get(100) < 10 && master != player && master != null) {
            // ItemService.addItem(master, 186000169, 1);
            // PacketSendUtility.sendMessage(master, "You have received a [item: 186000169]!");
            // }

            player.getAggroList().clear();
            player.getBattleground().onDie(player, master != null ? master : player);
            return;
        }

        if (ArenaService.getInstance().isInArena(player)) {
            // Wintersday Event FLUFFY SNOW CRYSTAL
            // if (Rnd.get(100) < 10 && master != player && master != null) {
            // ItemService.addItem(master, 186000170, 1);
            // PacketSendUtility.sendMessage(master,
            // "You have received a wintery [item: 186000170]!");
            // }

            // Wintersday Event SNOWMAN GIFT BOX
            // if (Rnd.get(100) < 1 && master != player && master != null) {
            // ItemService.addItem(master, 188051842, 1);
            // PacketSendUtility.sendMessage(master,
            // "You have received a wintery [item: 188051842]!");
            // }

            // Halloween Event PUMPKIN COOKIE
            // if (Rnd.get(100) < 3 && master != player && master != null) {
            // ItemService.addItem(master, 186000169, 1);
            // PacketSendUtility.sendMessage(master, "You have received a [item: 186000169]!");
            // }

            player.getAggroList().clear();
            ArenaService.getInstance().onDie(player, master);
            return;
        }

        if (TownArenaService.getInstance().isInArena(player)) {
            player.getAggroList().clear();
            TownArenaService.getInstance().onDie(player, master);
            return;
        }

        if (master != null && !(player.isLawless() || player.isOutlaw() || player.isBandit())) {
            if (player.getEvent() != null && player.getEvent() instanceof MobEvent
                && !isDueling((Player) master))
                ((MobEvent) player.getEvent()).onPlayerDie(lastAttacker);

            if (isDueling((Player) master)) {
                DuelService.getInstance().onDie(player);
                return;
            }

            if (player.isFriend((Player) master)
                && ((Player) master).getAccessLevel() < AdminConfig.COMMAND_KILL
                && player.getBattleground() == null) {
                player.getLifeStats().setCurrentHp(1);
                return;
            }
        }

        if (DuelService.getInstance().isDueling(player.getObjectId()))
            DuelService.getInstance().loseDuel(player);

        if (player.getStance() != 0) {
            player.getEffectController().removeNoshowEffect(player.getStance());
            PacketSendUtility.broadcastPacketAndReceive(player,
                new SM_STANCE_STATE(player.getObjectId(), 0));
            player.setStance(0);
        }

        // Frienderino fix
        if (master == null || player.isFriend(master))
            master = player.getAggroList().getMostPlayerDamage();

        if (player.isRidingRobot()) {
            Effect ef = player.getEffectController().getNoshowEffect("RI_SUMMONARMOR");
            if (ef != null)
                ef.endEffect();
        }

        // Wintersday Event FLUFFY SNOW CRYSTAL
        // if (Rnd.get(100) < 10 && master != player && master != null) {
        // ItemService.addItem(master, 186000170, 1);
        // PacketSendUtility.sendMessage(master, "You have received a wintery [item: 186000170]!");
        // }

        // Wintersday Event SNOWMAN GIFT BOX
        // if (Rnd.get(100) < 1 && master != player && master != null) {
        // ItemService.addItem(master, 188051842, 1);
        // PacketSendUtility.sendMessage(master, "You have received a wintery [item: 188051842]!");
        // }

        // Halloween Event PUMPKIN COOKIE
        // if (Rnd.get(100) < 10 && master != player && master != null) {
        // ItemService.addItem(master, 186000169, 1);
        // PacketSendUtility.sendMessage(master, "You have received a [item: 186000169]!");
        // }

        if (didReward.compareAndSet(false, true)) {
            this.doReward();
        }
        else {
            return;
        }

        dieTime = System.currentTimeMillis();

        String killer = master != null ? master.getName() + " ("
            + master.getCommonData().getRace().name() + (master.isOutlaw() ? "-outlaw" : "")
            + (master.isLawless() ? "-lawless" : "") + (master.isBandit() ? "-bandit" : "") + ")"
            : "himself";

        log.info("[DEBUG] Player " + player.getName() + " ("
            + player.getCommonData().getRace().name() + ") was killed by " + killer + " in "
            + player.getWorldId());

        if (master != null
            && master != player
            && master.getClientConnection() != null
            && player.getClientConnection() != null
            && master.getClientConnection().getMacAddress() != null
            && player.getClientConnection().getMacAddress() != null
            && !master.getClientConnection().getMacAddress().isEmpty()
            && master.getClientConnection().getMacAddress()
                .equals(player.getClientConnection().getMacAddress())) {
            log.info("[AUDIT] Player " + master.getName() + " killed " + player.getName() + " in "
                + player.getWorldId() + " from same MAC ("
                + player.getClientConnection().getMacAddress() + ")");
        }

        // Effects removed with super.onDie()
        boolean hasSelfRezEffect = player.getReviveController().checkForSelfRezEffect(player)
            && canAutoRevive;

        player.getEffectController().removeAbnormalEffectsByTargetSlot(SkillTargetSlot.DEBUFF);
        player.getEffectController().removeEffectByDispelCat(DispelCategoryType.ALL,
            SkillTargetSlot.DEBUFF, 100, 1);

        this.getOwner().getMoveController().stop();
        this.getOwner().setState(CreatureState.DEAD);
        this.getOwner().getObserveController().notifyDeath(this.getOwner());
        this.getOwner().getAggroList().clear();

        /**
         * Release summon
         */
        Summon summon = player.getSummon();
        if (summon != null)
            summon.getController().release(UnsummonType.UNSPECIFIED);

        if (player.getToyPet() != null)
            ToyPetService.getInstance().dismissPet(player, player.getToyPet().getPetId());

        PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player, EmotionType.DIE, 0,
            lastAttacker == null ? 0 : lastAttacker.getObjectId()), true);

        // SM_DIE Packet
        int kiskTimeRemaining = (player.getKisk() != null ? player.getKisk().getRemainingLifetime()
            : 0);
        boolean hasSelfRezItem = player.getReviveController().checkForSelfRezItem(player)
            && canAutoRevive;

        if (!OutlawService.getInstance().isOutlaw(player)
            && (player.isLawless() || player.isBandit())) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    if (player.getLifeStats().isAlreadyDead()) {
                        player.getReviveController().fullRevive();

                        if (player.getKisk() != null
                            && player.getKisk().getWorldId() == player.getWorldId()) {
                            TeleportService.moveToKiskLocation(player);
                            player.getKisk().resurrectionUsed(player);
                        }
                    }
                }
            }, 10000);
        }
        else
            PacketSendUtility.sendPacket(player, new SM_DIE(hasSelfRezEffect, hasSelfRezItem,
                kiskTimeRemaining));

        OutlawService.getInstance().onDie(player);

        SerialService.getInstance().onDie(player, master);

        PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.DIE);
        QuestEngine.getInstance().onDie(new QuestCookie(null, player, 0, 0));
    }

    @Override
    public void doReward() {
        Player victim = getOwner();
        Player winner = getOwner().getAggroList().getMostPlayerDamage();

        if (winner != null) {
            List<Player> toReward = new ArrayList<Player>();
            toReward.add(winner);

            if (winner.isInGroup()) {
                for (Player pl : winner.getPlayerGroup().getMembers())
                    if (pl.getObjectId() != winner.getObjectId()
                        && MathUtil.isIn3dRange(winner, pl, 60))
                        toReward.add(pl);
            }
            else if (winner.isInAlliance()) {
                for (PlayerAllianceMember pla : winner.getPlayerAlliance().getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    if (pl.getObjectId() != winner.getObjectId()
                        && MathUtil.isIn3dRange(winner, pl, 60))
                        toReward.add(pl);
                }
            }

            int dpReward = 500 / toReward.size();

            for (Player pl : toReward)
                pl.getCommonData().addDp(dpReward);
        }

        PvpService.getInstance().doReward(victim);
    }

    @Override
    public void onRespawn() {
        super.onRespawn();

        startProtectionActiveTask();

        getOwner().getKnownList().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                if (pl.getTarget() == getOwner())
                    PacketSendUtility.sendPacket(pl, new SM_TARGET_SELECTED(pl));

                return true;
            }
        });
    }

    @Override
    public void attackTarget(Creature target, int atknumber, int time, int attackType) {
        super.attackTarget(target, atknumber, time, attackType);
    }

    public void onAttack(final Creature creature, int skillId, TYPE type, int damage, int logId,
        AttackStatus status, boolean notifyAttackedObservers, boolean sendPacket) {
        if (getOwner() == null || getOwner().getLifeStats().isAlreadyDead())
            return;

        if (getOwner().isInvul())
            damage = 0;

        super.onAttack(creature, skillId, type, damage, logId, status, notifyAttackedObservers,
            sendPacket);
    }

    /**
     * @param skillId
     * @param targetType
     * @param x
     * @param y
     * @param z
     */
    public void useSkill(int skillId, int targetType, float x, float y, float z, int time) {
        Player player = getOwner();

        if (player.isUsingMount())
            MountService.stopMount(player);

        Skill skill = SkillEngine.getInstance().getSkillFor(player, skillId, player.getTarget());

        if (skill != null) {
            skill.setTargetType(targetType, x, y, z);
            skill.setTime(time);

            if (skill.getSkillTemplate().getTargetRelationProperty() != null
                && skill.getSkillTemplate().getTargetRelationProperty().getValue() == TargetRelationAttribute.ENEMY
                && player.getTarget() != player)
                player.incrementActivityCounter(1);

            if (!RestrictionsManager.canUseSkill(player, skill))
                return;

            if (player.getSummon() != null
                && DataManager.PET_SKILL_DATA.getPetOrderSkill(skillId, player.getSummon()
                    .getNpcId()) != 0) {
                int cooldown = skill.getSkillTemplate().getCooldown(skill.getSkillLevel());

                player.getSummon().getController()
                    .queueOrderSkill(new Integer[] { skillId, cooldown * 10 });
            }

            skill.useSkill();
        }

        skill = null;
    }

    @Override
    public void onMove() {
        // cancelCurrentAction();
        super.onMove();
        addZoneUpdateMask(ZoneUpdateMode.ZONE_REFRESH);// ZoneUpdateMode.ZONE_UPDATE);
    }

    @Override
    public void onStopMove() {
        // cancelCurrentAction();
        super.onStopMove();
        addZoneUpdateMask(ZoneUpdateMode.ZONE_REFRESH);
        getOwner().getMoveController().stop();
    }

    /**
     * Perform tasks on Player jumping
     */
    public void onJump() {
        cancelCurrentAction();
        getOwner().getObserveController().notifyJumpObservers();
    }

    /**
     * Cancel current skill and remove cooldown
     */
    public void cancelCurrentAction() {
        Player player = getOwner();

        cancelCurrentSkill(true);

        /*
         * if (player.isUsingObject() && player.getUseObject() != null) { player.setUsingObject(false); if
         * (player.getUseObject() instanceof Item) { player.getController().cancelTask(TaskId.ITEM_USE);
         * PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.STR_ITEM_CANCELED());
         * PacketSendUtility.sendPacket(player, new SM_ITEM_USAGE_ANIMATION(player.getObjectId(), player.getUseObject()
         * .getObjectId(), ((Item) player.getUseObject()).getItemTemplate() .getTemplateId(), 0, 3, 0)); } else if
         * (player.getUseObject() instanceof Npc) { player.getController().cancelTask(TaskId.PORTAL_USE);
         * PacketSendUtility.sendPacket(player, new SM_USE_OBJECT(player.getObjectId(), player
         * .getUseObject().getObjectId(), 3000, 0)); PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player,
         * EmotionType.END_QUESTLOOT, 0, player.getUseObject().getObjectId()), true); } player.setUseObject(null); }
         */
    }

    /**
     * Cancel current skill and remove cooldown
     */
    @Override
    public void cancelCurrentSkill(boolean forced) {
        Player player = getOwner();
        Skill castingSkill = player.getCastingSkill();

        if (castingSkill != null) {
            int skillId = castingSkill.getSkillTemplate().getSkillId();
            castingSkill.cancelCast();
            player.removeSkillCoolDown(castingSkill.getSkillTemplate().getDelayId());
            this.setSelfChainCount(0);
            player.setCasting(null);

            if (castingSkill.getSkillType() == SkillActivationType.ITEM) {
                PacketSendUtility.sendPacket(player,
                    new SM_ITEM_USAGE_ANIMATION(player.getObjectId(), castingSkill.getFirstTarget()
                        .getObjectId(), castingSkill.getItemObjectId(), castingSkill
                        .getItemTemplate().getTemplateId(), 0, 3, 0));
                getOwner().removeItemCoolDown(castingSkill.getItemTemplate().getDelayId());
                PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.STR_ITEM_CANCELED());
            }
            else {
                PacketSendUtility.broadcastPacket(player, new SM_SKILL_CANCEL(player, skillId),
                    forced);
                PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.STR_SKILL_CANCELED());
            }
        }
    }

    /**
     *
     */
    public void updatePassiveStats() {
        Player player = getOwner();
        for (SkillListEntry skillEntry : player.getSkillList().getAllSkills()) {
            Skill skill = SkillEngine.getInstance().getSkillFor(player, skillEntry.getSkillId(),
                player.getTarget());
            if (skill != null && skill.isPassive()) {
                skill.useSkill();
            }
        }
    }

    @Override
    public Player getOwner() {
        return (Player) super.getOwner();
    }

    @Override
    public void onRestore(HealType healType, int value) {
        super.onRestore(healType, value);
        switch (healType) {
            case DP:
                getOwner().getCommonData().addDp(value);
                break;
        }
    }

    /**
     * @param player
     * @return
     */
    public boolean isDueling(Player player) {
        return getOwner() != null
            && DuelService.getInstance().isDueling(player.getObjectId(), getOwner().getObjectId());
    }

    public void updateNearbyQuestList() {
        // getOwner().addPacketBroadcastMask(BroadcastMode.UPDATE_NEARBY_QUEST_LIST);
    }

    public void updateNearbyQuestListImpl() {
        PacketSendUtility
            .sendPacket(getOwner(), new SM_NEARBY_QUESTS(getOwner().getNearbyQuests()));
    }

    public boolean isInShutdownProgress() {
        return isInShutdownProgress;
    }

    public void setInShutdownProgress(boolean isInShutdownProgress) {
        this.isInShutdownProgress = isInShutdownProgress;
    }

    /**
     * Handle dialog
     */
    @Override
    public void onDialogSelect(int dialogId, Player player, int questId) {
        switch (dialogId) {
            case 2:
                PacketSendUtility.sendPacket(player, new SM_PRIVATE_STORE(getOwner().getStore()));
                break;
        }
    }

    /**
     * @param level
     */
    public void upgradePlayer(int level) {
        Player player = getOwner();

        PlayerStatsTemplate statsTemplate = DataManager.PLAYER_STATS_DATA.getTemplate(player);
        player.setPlayerStatsTemplate(statsTemplate);

        // update stats after setting new template
        player.getGameStats().doLevelUpgrade();
        player.getLifeStats().synchronizeWithMaxStats();
        player.getLifeStats().updateCurrentStats();

        PacketSendUtility.broadcastPacket(player, new SM_LEVEL_UPDATE(player.getObjectId(), 0,
            level), true);
        PacketSendUtility.sendPacket(player, new SM_CUBE_UPDATE(player, 6, player.getCommonData()
            .getAdvancedStigmaSlotSize()));
        PacketSendUtility.sendPacket(player, new SM_CUBE_UPDATE(player, 5, player.getCommonData()
            .getStigmaSlotSize()));

        // Temporal
        ClassChangeService.showClassChangeDialog(player);

        QuestEngine.getInstance().onLvlUp(new QuestCookie(null, player, 0, 0));
        updateNearbyQuests();
        PacketSendUtility.sendPacket(player, new SM_QUEST_LIST(player));

        PacketSendUtility.sendPacket(player, new SM_STATS_INFO(player));

        /*
         * if (level == 10 && player.getSkillList().getSkillEntry(30001) != null) { int skillLevel =
         * player.getSkillList().getSkillLevel(30001); player.getSkillList().removeSkill(30001);
         * PacketSendUtility.sendPacket(player, new SM_SKILL_LIST(player)); player.getSkillList().addSkill(player,
         * 30002, skillLevel, true); }
         */

        // add new skills
        SkillLearnService.addNewSkills(player, false);
        player.getController().updatePassiveStats();

        /**
         * Broadcast Update to all that may care.
         */
        if (player.isInGroup())
            player.getPlayerGroup().updateGroupUIToEvent(player, GroupEvent.UPDATE);
        if (player.isInAlliance())
            AllianceService.getInstance().updateAllianceUIToEvent(player,
                PlayerAllianceEvent.UPDATE);
        if (player.isLegionMember())
            LegionService.getInstance().updateMemberInfo(player);

        if (CustomConfig.ENABLE_SURVEYS)
            HTMLService.checkSurveys(player);
    }

    /**
     * After entering game player char is "blinking" which means that it's in under some protection, after making an
     * action char stops blinking. - Starts protection active - Schedules task to end protection
     */
    public void startProtectionActiveTask() {
        getOwner().setVisualState(CreatureVisualState.BLINKING);
        PacketSendUtility.broadcastPacket(getOwner(), new SM_PLAYER_STATE(getOwner()), true);

        Future<?> task = ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                stopProtectionActiveTask();
            }
        }, 20 * 1000);

        addTask(TaskId.PROTECTION_ACTIVE, task);
    }

    /**
     * Stops protection active task after first move or use skill
     */
    public void stopProtectionActiveTask() {
        cancelTask(TaskId.PROTECTION_ACTIVE);

        Player player = getOwner();

        if (player != null && player.isSpawned()) {
            player.unsetVisualState(CreatureVisualState.BLINKING);
            PacketSendUtility.broadcastPacket(player, new SM_PLAYER_STATE(player), true);
        }
    }

    /**
     * When player arrives at destination point of flying teleport
     */
    public void onFlyTeleportEnd() {
        Player player = getOwner();
        player.unsetState(CreatureState.FLIGHT_TELEPORT);
        player.setFlightTeleportId(0);
        player.setFlightDistance(0);
        player.setWindstreamId(0);
        player.setWindstreamDistance(0);
        player.setState(CreatureState.ACTIVE);
        addZoneUpdateMask(ZoneUpdateMode.ZONE_REFRESH);
    }

    public void onEnterZone(ZoneInstance zoneInstance) {
        addZoneUpdateMask(ZoneUpdateMode.ZONE_REFRESH);
        QuestEngine.getInstance().onEnterZone(new QuestCookie(null, this.getOwner(), 0, 0),
            zoneInstance.getTemplate().getName());

        Player player = getOwner();
        ZoneInstance currentZone = player.getZoneInstance();
        if (currentZone != null && GSConfig.FREEFLY == true) {
            currentZone.isFlightAllowed();
        }
        if (currentZone != null && !currentZone.isFlightAllowed()
            && player.getAccessLevel() < AdminConfig.GM_FLIGHT_FREE) {
            checkNoFly(player);
        }

        if (player.getWorldId() == WorldMapType.PANGAEA4.getId()) {
            if (zoneInstance != null
                && zoneInstance.getTemplate().getName()
                    .equals("STR_GAb1_04_SZ_Castle_01_400060000")) {
                player.getFlyController().endFly();
            }
        }

        boolean old = player.isInZergingZone();

        player.setInZergingZone(isInZergingZone(player));

        if (old != player.isInZergingZone() && player.getBattleground() == null
            && !ArenaService.getInstance().isInArena(player)) {
            PacketSendUtility.sendSys2Message(player, "Zone",
                old ? "You are no longer in a siege zone" : "You have entered a siege zone");
        }

        CustomSkillsService.getInstance().onEnterZone(player);

        /*
         * if (player.getWorldId() == WorldMapType.SOUTH_KATALAM.getId() && player.getZoneInstance() != null &&
         * player.getZoneInstance().getTemplate().getName().equals("LDF5b_SZ_A4_600060000")) {
         * PacketSendUtility.sendSys2Message(player, "Death",
         * "You have ventured into a zone that is no longer accessible."); OutlawService.getInstance().doom(player); }
         */
        /*
         * if (player.getWorldId() == WorldMapType.SARPAN.getId() && player.getX() > 2100 && player.getZoneInstance() !=
         * null && (player.getZoneInstance().getTemplate().getName().equals("LDF4A_Sz_D_600020000") || player
         * .getZoneInstance().getTemplate().getName().equals("LDF4a_SZ_D_04_600020000"))) {
         * PacketSendUtility.sendSys2Message(player, "Death",
         * "You have ventured outside your safe domain - the punishment is death!");
         * OutlawService.getInstance().doom(player); }
         */

        if (NetworkConfig.GAMESERVER_ID == 17 && zoneInstance != null
            && zoneInstance.getTemplate().getName().equals("LF5_SZ_OP_03_210070000")
            && player.getCommonData().getRace() == Race.ASMODIANS) {
            PacketSendUtility.sendSys2Message(player, "Death",
                "You have ventured into a secured domain - the punishment is death!");
            OutlawService.getInstance().doom(player);
        }
    }

    private boolean isInZergingZone(Player player) {
        if (player.getZoneInstance() != null) {
            if (player.getWorldId() != WorldMapType.NORTH_KATALAM.getId()
                && player.getWorldId() != WorldMapType.SOUTH_KATALAM.getId()
                && player.getWorldId() != WorldMapType.KALDOR.getId()
                && player.getWorldId() != WorldMapType.RESHANTA.getId())
                return false;

            String zone = player.getZoneInstance().getTemplate().getName();

            // SK bottom fort
            if (zone.equals("LDF5b_SZ_F4_600060000") || zone.equals("LDF5b_SZ_F4_1_600060000")
                || zone.equals("LDF5b_SZ_F4_2_600060000") || zone.equals("LDF5b_SZ_F4_3_600060000")
                || zone.equals("LDF5b_SV_6021_600060000") || zone.equals("LDF5b_F4_v19_600060000")
                || zone.equals("LDF5b_F4_v18_600060000"))
                return true;
            // SK flying fort
            else if (zone.equals("LDF5b_SZ_D1_1_600060000") || zone.equals("LDF5b_SZ_D1_600060000")
                || zone.equals("LDF5b_SZ_D1_2_600060000") || zone.equals("LDF5b_SV_6011_600060000")
                || zone.equals("LDF5b_D1_v16_600060000") || zone.equals("LDF5b_D1_v15_600060000"))
                return true;
            // NK fort
            else if (zone.equals("E1_600050000") || zone.equals("E2_600050000")
                || zone.equals("5011_600050000"))
                return true;
            // Kaldor
            else if (zone.equals("Subzone_Fortress_600090000")
                || zone.equals("Subzone_D1_600090000") || zone.equals("Subzone_D1_2_600090000"))
                return true;
            // Reshanta
            else if (zone.equals("Ab1_SZ_1011_A_400010000") || zone.equals("Ab1_SZ_1011_400010000")
                || zone.equals("Ab1_SZ_1011_Enter_400010000"))
                return true;
        }

        return false;
    }

    public void checkNoFly(final Player player) {
        return;

        /*
         * if (player.getBattleground() != null && player.isSpectating()) return; if
         * (player.isInState(CreatureState.FLYING) && !player.isGM()) player.getFlyController().endFly();
         */
    }

    public void onLeaveZone(ZoneInstance zoneInstance) {

    }

    /**
     * Zone update mask management
     * 
     * @param mode
     */
    public final void addZoneUpdateMask(ZoneUpdateMode mode) {
        zoneUpdateMask |= mode.mask();
        ZoneService.getInstance().add(getOwner());
    }

    public final void removeZoneUpdateMask(ZoneUpdateMode mode) {
        zoneUpdateMask &= ~mode.mask();
    }

    public final byte getZoneUpdateMask() {
        return zoneUpdateMask;
    }

    /**
     * Update zone taking into account the current zone
     */
    public void updateZoneImpl() {
        ZoneService.getInstance().checkZone(getOwner());
    }

    /**
     * Refresh completely zone irrespective of the current zone
     */
    public void refreshZoneImpl() {
        ZoneService.getInstance().findZoneInCurrentMap(getOwner());
    }

    /**
     * Check water level (start drowning) and map death level (die)
     */
    public void checkWaterLevel() {
        Player player = getOwner();

        if (player == null)
            return;

        World world = World.getInstance();
        float z = player.getMoveController().getOriginZ();

        if (player.getLifeStats().isAlreadyDead() || player.isInvul())
            return;

        if (z < world.getWorldMap(player.getWorldId()).getDeathLevel()) {
            die();
            return;
        }

        ZoneInstance currentZone = player.getZoneInstance();
        if (currentZone != null && currentZone.isBreath())
            return;

        // TODO need fix character height
        float playerheight = player.getPlayerAppearance().getHeight() * 1.6f;
        if (z < world.getWorldMap(player.getWorldId()).getWaterLevel() - playerheight)
            ZoneService.getInstance().startDrowning(player);
        else
            ZoneService.getInstance().stopDrowning(player);
    }

    @Override
    public void createSummon(int npcId, int skillId, int skillLvl) {
        if (getOwner().getSummon() != null)
            getOwner().getSummon().getController().release(UnsummonType.LOGOUT);

        Player master = getOwner();
        Summon summon = SpawnEngine.getInstance().spawnSummon(master, npcId, skillId, skillLvl);
        master.setSummon(summon);
        PacketSendUtility.sendPacket(master, new SM_SUMMON_PANEL(summon));
    }

    public boolean addItems(int itemId, int count) {
        return ItemService.addItems(getOwner(),
            Collections.singletonList(new QuestItems(itemId, count)));
    }

    public void setCanAutoRevive(boolean canAutoRevive) {
        this.canAutoRevive = canAutoRevive;
    }

    public boolean getCanAutoRevive() {
        return canAutoRevive;
    }

    public boolean checkSkillPacket(int spellid, int time, int targetId) {
        Skill skill = SkillEngine.getInstance().getSkillFor(getOwner(), spellid,
            getOwner().getTarget());

        if (skill.getFirstTargetProperty() == FirstTargetAttribute.TARGET
            && (skill.getFirstTarget() == null || skill.getFirstTarget().getObjectId() != targetId)) {
            return false;
        }

        return true;
    }

    public boolean didReward() {
        return didReward.get();
    }

    public void setDidReward(boolean didReward) {
        this.didReward.set(didReward);
    }

    public long getDieTime() {
        return dieTime;
    }
}