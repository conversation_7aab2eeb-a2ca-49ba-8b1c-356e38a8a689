/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.model.pvpevents.Battleground.BattlegroundMap;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.utils.ThreadPoolManager;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
public class FourTeamBg extends Battleground {
    private boolean endCalled = false;
    private AtomicBoolean firstBlood = new AtomicBoolean(false);

    public FourTeamBg() {
        super.name = "4-Team";
        super.description = "You are on a team and must kill the enemies. The team with the most kills when the time runs out or when all others are defeated wins.";
        super.minSize = 2;
        super.maxSize = 4;
        super.teamCount = 4;
        super.matchLength = 280;

        BattlegroundMap map1 = new BattlegroundMap(300070000);
        map1.addSpawn(new SpawnPosition(503.0f, 631.0f, 104.55f));
        map1.addSpawn(new SpawnPosition(628.0f, 455.0f, 102.64f));
        map1.addSpawn(new SpawnPosition(503.0f, 391.0f, 94.35f));
        map1.addSpawn(new SpawnPosition(377.0f, 512.0f, 102.62f));
        map1.setKillZ(80f);

        BattlegroundMap map2 = new BattlegroundMap(300350000);
        map2.addSpawn(new SpawnPosition(1960.0f, 1002.5f, 230.5f));
        map2.addSpawn(new SpawnPosition(2003.8f, 891.7f, 230.6f));
        map2.addSpawn(new SpawnPosition(1927.0f, 874.0f, 231.4f));
        map2.addSpawn(new SpawnPosition(1899.0f, 999.2f, 230.6f));
        map2.addStaticDoor(225);
        map2.addStaticDoor(247);
        map2.addStaticDoor(227);
        map2.addStaticDoor(230);
        map2.addStaticDoor(234);
        map2.addStaticDoor(231);
        map2.addStaticDoor(232);
        map2.addStaticDoor(228);
        map2.addStaticDoor(226);
        map2.addStaticDoor(233);
        map2.addStaticDoor(229);
        map2.addStaticDoor(235);
        map2.setKillZ(220f);

        BattlegroundMap map3 = new BattlegroundMap(300450000);
        map3.addSpawn(new SpawnPosition(535.0f, 1174.0f, 444.75f));
        map3.addSpawn(new SpawnPosition(459.0f, 1097.0f, 451.76f));
        map3.addSpawn(new SpawnPosition(534.0f, 1102.0f, 432.44f));
        map3.addSpawn(new SpawnPosition(466.0f, 1164.0f, 433.44f));
        map3.addStaticDoor(124);
        map3.addStaticDoor(118);
        map3.addStaticDoor(116);
        map3.addStaticDoor(126);
        map3.addStaticDoor(125);
        map3.addStaticDoor(110);
        map3.addStaticDoor(123);
        map3.addStaticDoor(115);
        map3.addStaticDoor(113);
        map3.addStaticDoor(109);
        map3.addStaticDoor(106);
        map3.addStaticDoor(107);
        map3.setKillZ(425f);

        BattlegroundMap map4 = new BattlegroundMap(301170000); // 4.0
        map4.addSpawn(new SpawnPosition(468.0f, 467.0f, 103.0f));
        map4.addSpawn(new SpawnPosition(519.0f, 545.0f, 110.0f));
        map4.addSpawn(new SpawnPosition(606.0f, 396.0f, 98.0f));
        map4.addSpawn(new SpawnPosition(672.0f, 467.0f, 103.0f));
        map4.addStaticDoor(43);
        map4.addStaticDoor(46);
        map4.addStaticDoor(54);
        map4.addStaticDoor(1);
        map4.setKillZ(95f);

        BattlegroundMap map5 = new BattlegroundMap(301180000); // 4.0
        map5.addSpawn(new SpawnPosition(165.0f, 315.0f, 60.0f));
        map5.addSpawn(new SpawnPosition(198.0f, 176.0f, 59.0f));
        map5.addSpawn(new SpawnPosition(339.0f, 200.0f, 62.0f));
        map5.addSpawn(new SpawnPosition(315.0f, 344.0f, 60.0f));
        map5.setKillZ(53f);

        BattlegroundMap map6 = new BattlegroundMap(400030000);
        map6.addSpawn(new SpawnPosition(570.2f, 476.2f, 676f));
        map6.addSpawn(new SpawnPosition(449.2f, 549.8f, 676f));
        map6.addSpawn(new SpawnPosition(470.4f, 453.4f, 676f));
        map6.addSpawn(new SpawnPosition(547.5f, 572.2f, 675f));
        map6.setKillZ(672f);
        map6.setHighestZ(683f);

        BattlegroundMap map7 = new BattlegroundMap(220040000);
        map7.addSpawn(401.3f, 321.6f, 231f);
        map7.addSpawn(322.1f, 408.6f, 232f);
        map7.addSpawn(320.2f, 302.7f, 232.1f);
        map7.addSpawn(379.7f, 411.5f, 222.1f);
        map7.setKillZ(220f);
        map7.setRestrictFlight(true);

        super.maps.add(map1);
        // super.maps.add(map2);
        super.maps.add(map3);
        super.maps.add(map6);
        super.maps.add(map7);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 30000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 25000);

        startBattleground(25000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups() <= 1)
                    endFourTeamMatch(false);
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endFourTeamMatch(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in FourTeamBg!");
            }
            else {
                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 3);

                if (firstBlood.compareAndSet(false, true)) {
                    killer.getPlayerGroup()
                        .setKillCount(killer.getPlayerGroup().getKillCount() + 1);

                    super.announceAll(LadderService.getInstance().getNameByIndex(
                        killer.getPlayerGroup().getBgIndex())
                        + " has scored First Blood for double points!");
                }
                else {
                    super.announceAll(LadderService.getInstance().getNameByIndex(
                        killer.getPlayerGroup().getBgIndex())
                        + " has slain an enemy!");
                }
            }
        }

        if (player.getPlayerGroup() == null)
            log.error("PlayerGroup == null in FourTeamBg!");

        int deadGroups = 0;
        for (PlayerGroup group : super.getGroups()) {
            int deadCounter = 0;
            for (Player pl : group.getMembers())
                if (pl.getLifeStats().isAlreadyDead())
                    deadCounter++;

            if (deadCounter == group.size())
                deadGroups++;
        }

        if (deadGroups >= super.getGroups().size() - 1)
            endFourTeamMatch(false);
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups() <= 1)
            endFourTeamMatch(false);
    }

    private PlayerGroup getWinner() {
        PlayerGroup winner = null;
        int drawPoints = 0;

        for (PlayerGroup group : super.getGroups()) {
            if (winner == null && group.getKillCount() > drawPoints) {
                winner = group;
            }
            else if (winner == null) {
                continue;
            }
            else if (winner.getKillCount() < group.getKillCount()) {
                winner = group;
            }
            else if (winner.getKillCount() == group.getKillCount()) {
                drawPoints = winner.getKillCount();
                winner = null;
            }
        }

        return winner;
    }

    private void endFourTeamMatch(boolean isDraw) {
        if (endCalled)
            return;

        endCalled = true;

        super.onEndFirstDefault();

        PlayerGroup winner = getWinner();

        if (winner == null || (winner == null && isDraw)) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.scheduleAnnouncement(pl, "The match was a draw! Better luck next time.",
                        0);
                    super.rewardPlayer(pl, 20, false);
                }
            }
            super.specAnnounce("The match was a draw!");
        }
        else {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == winner.getGroupId()) {
                    for (Player pl : group.getMembers()) {
                        int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2 : 1;

                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.scheduleAnnouncement(pl,
                            "Your team has won the match with " + group.getKillCount() + " kills!",
                            0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                        super.rewardPlayer(pl, 30, true);
                    }
                }
                else {
                    for (Player pl : group.getMembers()) {
                        super.playerLoseMatch(pl, -super.K_VALUE / 4);

                        super.scheduleAnnouncement(pl,
                            "Your team has lost the match with " + group.getKillCount()
                                + " kills against " + winner.getKillCount() + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);
                        super.rewardPlayer(pl, 20, false);
                    }
                }
            }
            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match with " + winner.getKillCount() + " kills!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}