/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events.bossevent;

import gameserver.eventengine.events.RandomEvent;
import gameserver.model.templates.bossevent.BossTemplate;
import gameserver.model.templates.bossevent.Position3D;

import java.util.Collection;
import java.util.Set;

/**
 * An event which will fire one random bossevent each call
 * 
 * <AUTHOR>
 * 
 */
public class RandomBossEvent extends RandomEvent<BossEvent> {

    private final int mapId;
    private final Collection<Position3D> spawnpoints;
    private final Set<PointAlgorithm> algorithms;

    @SuppressWarnings("unchecked")
    public RandomBossEvent(Collection<BossTemplate> bosstemplates, int mapid,
        Collection<Position3D> spawnpoints, Set<? extends PointAlgorithm> algorithms) {

        this.mapId = mapid;
        this.spawnpoints = spawnpoints;
        this.algorithms = (Set<PointAlgorithm>) algorithms;

        for (BossTemplate template : bosstemplates) {
            register(template);
        }
    }

    /**
     * Registers a new BossEvent. Please make sure that the boss event must be on the same map
     */
    @Override
    public void register(BossEvent bossEvent) {
        if (bossEvent.getMapId() == mapId) {
            super.register(bossEvent);
        }
        else {

        }
    }

    /**
     * Creates and registers a new BossEvent from a BossTemplate
     * 
     * @param template
     *            the BossTemplate from to create the BossEvent
     */
    public void register(BossTemplate template) {
        register(new BossEvent(template, mapId, spawnpoints, algorithms));
    }
}
