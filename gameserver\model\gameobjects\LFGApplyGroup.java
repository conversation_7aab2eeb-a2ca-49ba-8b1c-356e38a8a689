package gameserver.model.gameobjects;

import gameserver.model.gameobjects.player.Player;

/**
 * <AUTHOR> oni
 * 
 */
public class LFGApplyGroup {
    private Player player;
    private String applyString;
    private int creationTime;
    private int groupType;

    public LFGApplyGroup(Player player, String applyString, int groupType, int creationTime) {
        this.player = player;
        this.applyString = applyString;
        this.creationTime = creationTime;
        this.groupType = groupType;
    }

    public Player getPlayer() {
        return player;
    }

    public void setPlayer(Player player) {
        this.player = player;
    }

    public String getApplyString() {
        return applyString;
    }

    public void setApplyString(String applyString) {
        this.applyString = applyString;
    }

    public int getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(int creationTime) {
        this.creationTime = creationTime;
    }

    public int getGroupType() {
        return groupType;
    }

    public void setGroupType(int groupType) {
        this.groupType = groupType;
    }
}
