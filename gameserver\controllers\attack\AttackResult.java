/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.attack;

import gameserver.skillengine.action.DamageType;

/**
 * <AUTHOR>
 * 
 */
public class AttackResult {
    private int damage;

    private AttackStatus attackStatus;

    private int shieldType;
    private int reflectedDamage;
    private int skillId;
    private int protectorId;
    private int protectedDamage;
    private int protectedSkillId;
    private int manaShieldCost;
    private int manaShieldSkillId;

    private DamageType damageType;

    public AttackResult(int damage, AttackStatus attackStatus) {
        this(damage, attackStatus, 0, 0, DamageType.PHYSICAL);
    }

    public AttackResult(int damage, AttackStatus attackStatus, int reflectedDamage, int skillId,
        DamageType damageType) {
        this.damage = damage;
        this.attackStatus = attackStatus;
        this.reflectedDamage = reflectedDamage;
        this.skillId = skillId;
        this.damageType = damageType;
    }

    /**
     * @return the damage
     */
    public int getDamage() {
        return damage;
    }

    /**
     * @param damage
     *            the damage to set
     */
    public void setDamage(int damage) {
        this.damage = damage;
    }

    /**
     * @return the attackStatus
     */
    public AttackStatus getAttackStatus() {
        return attackStatus;
    }

    /**
     * @return the shieldType
     */
    public int getShieldType() {
        return shieldType;
    }

    /**
     * @param shieldType
     *            the shieldType to set
     */
    public void setShieldType(int shieldType) {
        this.shieldType = shieldType;
    }

    public int getReflectedDamage() {
        return this.reflectedDamage;
    }

    public void setReflectedDamage(int reflectedDamage) {
        this.reflectedDamage = reflectedDamage;
    }

    public int getReflectedSkillId() {
        return this.skillId;
    }

    public void setReflectedSkillId(int skillId) {
        this.skillId = skillId;
    }

    public DamageType getDamageType() {
        return this.damageType;
    }
    
    /**
     * @return the protectorId
     */
    public int getProtectorId() {
        return protectorId;
    }

    /**
     * @param protectorId the protectorId to set
     */
    public void setProtectorId(int protectorId) {
        this.protectorId = protectorId;
    }

    /**
     * @return the protectedDamage
     */
    public int getProtectedDamage() {
        return protectedDamage;
    }

    /**
     * @param protectedDamage the protectedDamage to set
     */
    public void setProtectedDamage(int protectedDamage) {
        this.protectedDamage = protectedDamage;
    }

    /**
     * @return the protectedSkillId
     */
    public int getProtectedSkillId() {
        return protectedSkillId;
    }

    /**
     * @param protectedSkillId the protectedSkillId to set
     */
    public void setProtectedSkillId(int protectedSkillId) {
        this.protectedSkillId = protectedSkillId;
    }

    public int getManaShieldCost() {
        return manaShieldCost;
    }

    public void setManaShieldCost(int manaShieldCost) {
        this.manaShieldCost = manaShieldCost;
    }

    public int getManaShieldSkillId() {
        return manaShieldSkillId;
    }

    public void setManaShieldSkillId(int manaShieldSkillId) {
        this.manaShieldSkillId = manaShieldSkillId;
    }
}