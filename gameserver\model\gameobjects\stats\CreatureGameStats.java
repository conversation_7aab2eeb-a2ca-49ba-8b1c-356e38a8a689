/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.stats;

import gameserver.configs.network.NetworkConfig;
import gameserver.model.EmotionType;
import gameserver.model.SkillElement;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.id.ConditioningStatEffectId;
import gameserver.model.gameobjects.stats.id.ItemStatEffectId;
import gameserver.model.gameobjects.stats.id.StatEffectId;
import gameserver.model.gameobjects.stats.modifiers.MeanModifier;
import gameserver.model.gameobjects.stats.modifiers.SimpleModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.items.ItemSlot;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.taskmanager.tasks.PacketBroadcaster.BroadcastMode;
import gameserver.taskmanager.tasks.StatUpdater;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 */
public class CreatureGameStats<T extends Creature> {
    protected static final Logger log = Logger.getLogger(CreatureGameStats.class);

    private static final int ATTACK_MAX_COUNTER = Integer.MAX_VALUE;

    protected Map<StatEnum, Stat> stats;
    protected Map<StatEffectId, TreeSet<StatModifier>> statsModifiers;

    private int attackCounter = 0;
    protected T owner = null;

    protected ScheduledFuture<?> statTask = null;
    protected AtomicBoolean recomputing = new AtomicBoolean();
    protected Object lock = new Object();

    /**
     * @param owner
     */
    protected CreatureGameStats(T owner) {
        this.owner = owner;
        this.stats = new ConcurrentHashMap<StatEnum, Stat>();
        this.statsModifiers = new ConcurrentHashMap<StatEffectId, TreeSet<StatModifier>>();
    }

    public void setOwner(T owner) {
        this.owner = owner;
    }

    /**
     * @return the atcount
     */
    public int getAttackCounter() {
        return attackCounter;
    }

    /**
     * @param atcount
     *            the atcount to set
     */
    protected void setAttackCounter(int attackCounter) {
        if (attackCounter <= 0) {
            this.attackCounter = 1;
        }
        else {
            this.attackCounter = attackCounter;
        }
    }

    public void increaseAttackCounter() {
        if (attackCounter == ATTACK_MAX_COUNTER) {
            this.attackCounter = 1;
        }
        else {
            this.attackCounter++;
        }
    }

    /**
     * @param stat
     * @param value
     */
    public void setStat(StatEnum stat, int value) {
        setStat(stat, value, false);
    }

    /**
     * @param stat
     * @return
     */
    public int getBaseStat(StatEnum stat) {
        if (stats.containsKey(stat))
            return stats.get(stat).getBase();
        else
            return 0;
    }

    /**
     * @param stat
     * @return
     */
    public int getStatBonus(StatEnum stat) {
        if (stats.containsKey(stat))
            return stats.get(stat).getBonus();
        else
            return 0;
    }

    /**
     * @param stat
     * @return
     */
    public int getCurrentStat(StatEnum stat) {
        if (stats.containsKey(stat)) {
            Stat statObject = stats.get(stat);
            if (statObject == null)
                return 0;
            else
                return statObject.getCurrent();
        }
        else
            return 0;
    }

    /**
     * @param stat
     * @return
     */
    public int getOldStat(StatEnum stat) {
        if (stats.containsKey(stat))
            return stats.get(stat).getOld();
        else
            return 0;
    }

    /**
     * @param id
     * @param modifiers
     */
    public void addModifiers(StatEffectId id, TreeSet<StatModifier> modifiers) {
        addModifiers(id, modifiers, true);
    }

    public void addModifiers(StatEffectId id, TreeSet<StatModifier> modifiers, boolean recompute) {
        if (modifiers == null || statsModifiers.containsKey(id))
            return;

        synchronized (statsModifiers) {
            statsModifiers.put(id, modifiers);
        }

        if (recompute)
            StatUpdater.getInstance().startTask(owner);
    }

    /**
     * @return True if the StatEffectId is already added
     */
    public boolean effectAlreadyAdded(StatEffectId id) {
        return statsModifiers.containsKey(id);
    }

    /**
     * Recomputation of all stats
     */
    public void recomputeStats() {
        try {
            if (!recomputing.compareAndSet(false, true)) {
                log.info("Stat recomputation failed due to already running!");
                return;
            }

            synchronized (lock) {
                Map<StatEnum, StatModifiers> orderedModifiers = new LinkedHashMap<StatEnum, StatModifiers>();

                synchronized (statsModifiers) {
                    for (Entry<StatEffectId, TreeSet<StatModifier>> modifiers : statsModifiers
                        .entrySet()) {
                        StatEffectId eid = modifiers.getKey();
                        long slots;

                        if (modifiers.getValue() == null)
                            continue;

                        for (StatModifier modifier : modifiers.getValue()) {
                            slots = 0;

                            if (eid instanceof ItemStatEffectId)
                                slots = ((ItemStatEffectId) eid).getSlot();
                            else if (eid instanceof ConditioningStatEffectId)
                                slots = ((ConditioningStatEffectId) eid).getSlot();

                            if (slots == 0)
                                slots = ItemSlot.NONE.getSlotIdMask();

                            if (modifier.getStat().isMainOrSubHandStat() && owner instanceof Player) {
                                if (slots != ItemSlot.MAIN_HAND.getSlotIdMask()
                                    && slots != ItemSlot.SUB_HAND.getSlotIdMask()) {
                                    if (((Player) owner).getEquipment().getOffHandWeaponType() != null)
                                        slots = ItemSlot.MAIN_OR_SUB.getSlotIdMask();
                                    else {
                                        slots = ItemSlot.MAIN_HAND.getSlotIdMask();
                                        setStat(StatEnum.OFF_HAND_ACCURACY, 0, false);
                                    }
                                }
                                else if (slots == ItemSlot.MAIN_HAND.getSlotIdMask())
                                    setStat(StatEnum.MAIN_HAND_POWER, 0);
                            }

                            List<ItemSlot> oSlots = ItemSlot.getSlotsFor(slots);

                            if (modifier.getStat().isMainOrSubHandStat() && modifier.isBonus()) {
                                if (oSlots.contains(ItemSlot.MAIN_HAND)
                                    && !oSlots.contains(ItemSlot.SUB_HAND))
                                    oSlots.add(ItemSlot.SUB_HAND);
                                else if (oSlots.contains(ItemSlot.SUB_HAND)
                                    && !oSlots.contains(ItemSlot.MAIN_HAND))
                                    oSlots.add(ItemSlot.MAIN_HAND);
                            }

                            for (ItemSlot slot : oSlots) {
                                List<StatEnum> statToModifies = new ArrayList<StatEnum>();
                                if (modifier.getStatToModifies().size() > 0) {
                                    statToModifies = modifier.getStatToModifies();// for WeaponMastery
                                }
                                else {
                                    statToModifies.add(modifier.getStat()
                                        .getMainOrSubHandStat(slot));
                                }

                                for (StatEnum statToModify : statToModifies) {
                                    if (slot == ItemSlot.SUB_HAND
                                        && ((statToModify == StatEnum.PARRY && !modifier.isBonus())
                                            || (statToModify == StatEnum.MAGICAL_ACCURACY && !modifier
                                                .isBonus())
                                            || (statToModify == StatEnum.BOOST_MAGICAL_SKILL && !modifier
                                                .isBonus()) || (statToModify == StatEnum.MAGICAL_CRITICAL && !modifier
                                            .isBonus())))
                                        continue;

                                    if (slot == ItemSlot.SUB_HAND
                                        && (statToModify == StatEnum.PVP_ATTACK_RATIO
                                            || statToModify == StatEnum.PVP_ATTACK_RATIO_MAGICAL || statToModify == StatEnum.PVP_ATTACK_RATIO_PHYSICAL)
                                        && eid instanceof ConditioningStatEffectId)
                                        continue;

                                    if (!orderedModifiers.containsKey(statToModify)) {
                                        orderedModifiers.put(statToModify, new StatModifiers());
                                    }

                                    if (!modifier.isCanDuplicate()
                                        && orderedModifiers.get(statToModify)
                                            .getModifiers(modifier.getPriority())
                                            .contains(modifier)) {
                                        continue;
                                    }

                                    orderedModifiers.get(statToModify).add(modifier);
                                }
                            }
                        }
                    }
                }

                resetStats();

                if (owner instanceof Player
                    && orderedModifiers.containsKey(StatEnum.MAIN_HAND_POWER))
                    setStat(StatEnum.MAIN_HAND_POWER, 0);

                //StringBuilder sb = new StringBuilder();

                for (Entry<StatEnum, StatModifiers> entry : orderedModifiers.entrySet()) {
                    /*if (NetworkConfig.GAMESERVER_ID == 100 && owner instanceof Player) {
                        sb.append("\nStat: ");
                        sb.append(entry.getKey());
                        sb.append(" =>\n");

                        for (Entry<StatModifierPriority, List<StatModifier>> e : entry.getValue()
                            .getAllModifiers().entrySet()) {
                            sb.append("\t");
                            sb.append(e.getKey());
                            sb.append("\n");

                            for (StatModifier mod : e.getValue()) {
                                sb.append("\t\t");
                                sb.append(mod.getClass().getSimpleName()
                                    + (mod.isBonus() ? " (bonus)" : "") + " => ");

                                if (mod instanceof SimpleModifier)
                                    sb.append(((SimpleModifier) mod).getValue());
                                else if (mod instanceof MeanModifier)
                                    sb.append(((MeanModifier) mod).getMin() + "/"
                                        + ((MeanModifier) mod).getMax());

                                sb.append("\n");
                            }
                        }
                    }*/

                    applyModifiers(entry.getKey(), entry.getValue());
                }

                //if (sb.length() > 0)
                //    log.info(sb.toString());

                if (owner instanceof Player && getBaseStat(StatEnum.MAIN_HAND_POWER) == 0)
                    setStat(StatEnum.MAIN_HAND_POWER,
                        Math.round(18 * (getCurrentStat(StatEnum.POWER) * 0.01f)));

                if (owner instanceof Player) {
                    setStat(
                        StatEnum.ATTACK_SPEED,
                        Math.round(getBaseStat(StatEnum.MAIN_HAND_ATTACK_SPEED)
                            + getBaseStat(StatEnum.OFF_HAND_ATTACK_SPEED) * 0.25f), false);

                    int bonusAtkSpeed;
                    if (getBaseStat(StatEnum.OFF_HAND_ATTACK_SPEED) > 0)
                        bonusAtkSpeed = Math
                            .round(Math
                                .min(
                                    getStatBonus(StatEnum.MAIN_HAND_ATTACK_SPEED)
                                        * (getBaseStat(StatEnum.ATTACK_SPEED) / (float) getBaseStat(StatEnum.MAIN_HAND_ATTACK_SPEED)),
                                    getStatBonus(StatEnum.OFF_HAND_ATTACK_SPEED)
                                        * (getBaseStat(StatEnum.ATTACK_SPEED) / (float) getBaseStat(StatEnum.OFF_HAND_ATTACK_SPEED))));
                    else
                        bonusAtkSpeed = getStatBonus(StatEnum.MAIN_HAND_ATTACK_SPEED);

                    setStat(
                        StatEnum.ATTACK_SPEED,
                        bonusAtkSpeed
                            + Math.round(getStatBonus(StatEnum.ATTACK_SPEED)
                                * getBaseStat(StatEnum.ATTACK_SPEED) / (float) 1500), true);

                    if (((Player) owner).getEquipment().getOffHandWeaponType() == null) {
                        setStat(StatEnum.OFF_HAND_CRITICAL, 0);
                        setStat(StatEnum.OFF_HAND_CRITICAL, 0, true);
                        setStat(StatEnum.OFF_HAND_POWER, 0);
                        setStat(StatEnum.OFF_HAND_POWER, 0, true);
                        setStat(StatEnum.OFF_HAND_ACCURACY, 0);
                        setStat(StatEnum.OFF_HAND_ACCURACY, 0, true);
                        setStat(StatEnum.OFF_HAND_MAGICAL_ATTACK, 0);
                        setStat(StatEnum.OFF_HAND_MAGICAL_ATTACK, 0, true);
                    }
                }

                orderedModifiers.clear();

                applyLimits();
            }
        }
        finally {
            recomputing.set(false);
        }
    }

    protected void applyLimits() {
        int MIN_RESISTANCE = -400;
        int MIN_BOOST_MAGICAL_SKILL = 0;
        int MAX_BOOST_CASTING_TIME = 170;

        StatEnum[] resistances = { StatEnum.FIRE_RESISTANCE, StatEnum.EARTH_RESISTANCE,
            StatEnum.WIND_RESISTANCE, StatEnum.WATER_RESISTANCE };

        int bonus = 0;

        // magic boost limit
        if (getCurrentStat(StatEnum.BOOST_MAGICAL_SKILL) < MIN_BOOST_MAGICAL_SKILL) {
            bonus = this.getBaseStat(StatEnum.BOOST_MAGICAL_SKILL) - MIN_BOOST_MAGICAL_SKILL;
            this.setStat(StatEnum.BOOST_MAGICAL_SKILL, -bonus, true);
            bonus = 0;
        }

        // boost casting time limit
        if (getCurrentStat(StatEnum.BOOST_CASTING_TIME) > MAX_BOOST_CASTING_TIME) {
            bonus = MAX_BOOST_CASTING_TIME - this.getBaseStat(StatEnum.BOOST_CASTING_TIME);
            this.setStat(StatEnum.BOOST_CASTING_TIME, bonus, true);
            bonus = 0;
        }

        // fire, earth, wind, water resistance limit
        for (StatEnum stat : resistances) {
            if (getCurrentStat(stat) < MIN_RESISTANCE) {
                bonus = this.getBaseStat(stat) - MIN_RESISTANCE;
                this.setStat(stat, -bonus, true);
                bonus = 0;
            }
        }
    }

    /**
     * @param id
     */
    public void endEffect(StatEffectId id) {
        endEffect(id, true);
        /*
         * synchronized (statsModifiers) { statsModifiers.remove(id); } // if (statTask != null && !statTask.isDone())
         * // statTask.cancel(false); // statTask = ThreadPoolManager.getInstance().schedule(new Runnable() { //
         * @Override // public void run() { // recomputeStats(); // } // }, 10);
         * StatUpdater.getInstance().startTask(owner);
         */
    }

    public void endEffect(StatEffectId id, boolean recompute) {
        synchronized (statsModifiers) {
            statsModifiers.remove(id);
        }

        if (recompute)
            StatUpdater.getInstance().startTask(owner);
    }

    public boolean hasEffect(StatEffectId id) {
        return statsModifiers.containsKey(id);
    }

    /**
     * @param element
     * @return
     */
    public int getMagicalDefenseFor(SkillElement element) {
        if (element == null)
            return 0;

        switch (element) {
            case EARTH:
                return getCurrentStat(StatEnum.EARTH_RESISTANCE);
            case FIRE:
                return getCurrentStat(StatEnum.FIRE_RESISTANCE);
            case WATER:
                return getCurrentStat(StatEnum.WATER_RESISTANCE);
            case WIND:
                return getCurrentStat(StatEnum.WIND_RESISTANCE);
            default:
                return 0;
        }
    }

    /**
     * Reset all stats
     */
    protected void resetStats() {
        synchronized (stats) {
            for (Stat stat : stats.values()) {
                stat.reset();
            }
        }
    }

    /**
     * @param stat
     * @param modifiers
     */
    protected void applyModifiers(final StatEnum stat, StatModifiers modifiers) {
        if (modifiers == null || owner == null)
            return;

        if (!stats.containsKey(stat)) {
            initStat(stat, 0);
        }

        Stat oStat = stats.get(stat);
        int newValue;

        for (StatModifierPriority priority : StatModifierPriority.values()) {
            for (StatModifier modifier : modifiers.getModifiers(priority)) {
                newValue = modifier.apply(oStat.getBase(), oStat.getCurrent());
                oStat.increase(newValue, modifier.isBonus());
            }
        }

        if (stat == StatEnum.MAXHP || stat == StatEnum.MAXMP) {
            final int oldValue = getOldStat(stat);
            final int newVal = getCurrentStat(stat);
            if (oldValue == newVal) {
                return;
            }

            final CreatureLifeStats<? extends Creature> lifeStats = owner.getLifeStats();

            int set = 0;
            switch (stat) {
                case MAXHP:
                    int hp = lifeStats.getCurrentHp();
                    hp = (int) (hp * ((float) newVal / oldValue));
                    if (hp < 1)
                        hp = 1;
                    set = hp;
                    break;
                case MAXMP:
                    int mp = lifeStats.getCurrentMp();
                    mp = (int) (mp * ((float) newVal / oldValue));
                    set = mp;
                    break;
            }

            final int newSet = set;

            ThreadPoolManager.getInstance().execute(new Runnable() {
                @Override
                public void run() {
                    switch (stat) {
                        case MAXHP:
                            if (lifeStats.isAlreadyDead())
                                break;

                            if (oldValue == 0 || newVal == 0) {
                                lifeStats.setCurrentHp(newVal);
                                break;
                            }

                            lifeStats.setCurrentHp(newSet);
                            break;
                        case MAXMP:
                            if (lifeStats.isAlreadyDead())
                                break;

                            if (oldValue == 0 || newVal == 0) {
                                lifeStats.setCurrentMp(newVal);
                                break;
                            }

                            lifeStats.setCurrentMp(newSet);
                            break;
                    }
                }
            });
        }
    }

    /**
     * @param stat
     * @param value
     */
    public void initStat(StatEnum stat, int value) {
        if (!stats.containsKey(stat))
            stats.put(stat, new Stat(stat, value));
        else {
            stats.get(stat).reset();
            stats.get(stat).set(value, false);
        }
    }

    /**
     * @param stat
     * @param value
     * @param bonus
     */
    public void setStat(StatEnum stat, int value, boolean bonus) {
        if (!stats.containsKey(stat)) {
            stats.put(stat, new Stat(stat, 0));
        }
        stats.get(stat).set(value, bonus);
    }

    public void clear() {
        // if (statTask != null)
        // statTask.cancel(true);

        StatUpdater.getInstance().stopTask(owner);

        synchronized (stats) {
            stats.clear();
        }

        synchronized (statsModifiers) {
            statsModifiers.clear();
        }
    }

    public Map<StatEffectId, TreeSet<StatModifier>> getStatsModifiers() {
        return statsModifiers;
    }

    public void updateStats() {
        // empty -- to be overriden
    }

    public void updateSpeed() {
        PacketSendUtility.broadcastPacketAndReceive(owner, new SM_EMOTION(owner,
            EmotionType.START_EMOTE2, 0, 0));
    }

    public void updateVisualStats() {
        owner.addPacketBroadcastMask(BroadcastMode.UPDATE_STATS);
        owner.addPacketBroadcastMask(BroadcastMode.UPDATE_SPEED);
    }

    public void updateVisualSpeed() {
        owner.addPacketBroadcastMask(BroadcastMode.UPDATE_SPEED);
    }

    public void updateVisualStatOnly() {
        owner.addPacketBroadcastMask(BroadcastMode.UPDATE_STATS);
    }

    public boolean isRecomputing() {
        return recomputing.get();
    }

    public Object getLock() {
        return lock;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append('{');
        sb.append("owner:" + owner.getObjectId());
        for (Stat stat : stats.values()) {
            sb.append(stat);
        }
        sb.append('}');
        return sb.toString();
    }
}
