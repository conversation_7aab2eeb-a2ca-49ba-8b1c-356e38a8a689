/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class NahorCastleEvent extends MobEvent {
    public NahorCastleEvent() {
        super.mapId = 220050000;
        super.center = new SpawnPosition(1814, 885, 59);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("An event at Nahor Castle in Brusthonin will commence in 3 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at Nahor Castle starts in 1 minute", 2 * 60 * 1000);
        announceAll("The event at Nahor Castle starts in 30 seconds", 30 * 1000 + 2
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 3 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters at Nahor Castle in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217079, 1824, 907, 59);
        spawnMob(217079, 1831, 900, 59);
        spawnMob(217079, 1832, 891, 59);
        spawnMob(217079, 1833, 882, 59);
        spawnMob(217079, 1831, 872, 59);
        spawnMob(217079, 1811, 863, 59);
        spawnMob(217079, 1803, 866, 59);
        spawnMob(217079, 1798, 874, 59);
        spawnMob(217079, 1797, 889, 59);
        spawnMob(217079, 1796, 898, 59);
        spawnMob(217079, 1802, 906, 59);
        spawnMob(217079, 1781, 880, 59);
        spawnMob(217079, 1766, 880, 59);
        spawnMob(217079, 1757, 879, 59);
        spawnMob(217079, 1811, 919, 59);
        spawnMob(217079, 1810, 932, 59);
        spawnMob(217079, 1809, 949, 59);
        spawnMob(217079, 1817, 851, 59);
        spawnMob(217079, 1818, 837, 59);
        spawnMob(217079, 1820, 822, 59);
    }
}
