/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class AlquimiaApproachEvent extends MobEvent {
    public AlquimiaApproachEvent() {
        super.mapId = 220040000;
        super.center = new SpawnPosition(2738, 2586, 658);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("An event at Alquimia Approach in Beluslan will commence in 3 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at Alquimia Approach starts in 1 minute", 2 * 60 * 1000);
        announceAll("The event at Alquimia Approach starts in 30 seconds", 30 * 1000 + 2
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 3 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters at Alquimia Approach in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217079, 2761, 2579, 654);
        spawnMob(217079, 2771, 2583, 654);
        spawnMob(217079, 2777, 2590, 654);
        spawnMob(217079, 2778, 2600, 655);
        spawnMob(217079, 2762, 2598, 656);
        spawnMob(217079, 2741, 2563, 654);
        spawnMob(217079, 2733, 2556, 654);
        spawnMob(217079, 2725, 2552, 654);
        spawnMob(217079, 2714, 2554, 654);
        spawnMob(217079, 2723, 2570, 656);
        spawnMob(217079, 2739, 2578, 656);
        spawnMob(217079, 2746, 2584, 656);
        spawnMob(217079, 2730, 2581, 656);
        spawnMob(217079, 2745, 2593, 656);
        spawnMob(217079, 2730, 2591, 656);
        spawnMob(217079, 2734, 2595, 656);
        spawnMob(217079, 2718, 2600, 656);
        spawnMob(217079, 2727, 2604, 656);
        spawnMob(217079, 2736, 2607, 656);
        spawnMob(217079, 2746, 2606, 656);
    }
}
