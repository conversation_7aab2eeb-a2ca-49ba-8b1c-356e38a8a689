/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.player;

import gameserver.model.gameobjects.PersistentState;
import gameserver.utils.stats.AbyssRankEnum;

import java.util.Calendar;

/**
 * <AUTHOR> Divinity
 */
public class AbyssRank {
    private int dailyAP;
    private int weeklyAP;
    private int ap;
    private int dailyGlory;
    private int weeklyGlory;
    private int glory;
    private AbyssRankEnum rank;
    private int topRanking;
    private PersistentState persistentState;
    private int dailyKill;
    private int weeklyKill;
    private int allKill;
    private int maxRank;
    private int lastKill;
    private int lastAP;
    private int lastGlory;
    private long lastUpdate;

    /**
     * @param dailyAP
     * @param weeklyAP
     * @param ap
     * @param rank
     * @param dailyKill
     * @param weeklyKill
     * @param allKill
     * @param maxRank
     * @param lastKill
     * @param lastAP
     * @param lastUpdate
     */
    public AbyssRank(int dailyAP, int weeklyAP, int ap, int dailyGlory, int weeklyGlory, int glory,
        int rank, int topRanking, int dailyKill, int weeklyKill, int allKill, int maxRank,
        int lastKill, int lastAP, int lastGlory, long lastUpdate) {
        super();

        this.dailyAP = dailyAP;
        this.weeklyAP = weeklyAP;
        this.ap = ap;
        this.dailyGlory = dailyGlory;
        this.weeklyGlory = weeklyGlory;
        this.glory = glory;
        this.rank = AbyssRankEnum.getRankById(rank);
        this.topRanking = topRanking;
        this.dailyKill = dailyKill;
        this.weeklyKill = weeklyKill;
        this.allKill = allKill;
        this.maxRank = maxRank;
        this.lastKill = lastKill;
        this.lastAP = lastAP;
        this.lastGlory = lastGlory;
        this.lastUpdate = lastUpdate;

        doUpdate();
    }

    public void addDWAp(int ap) {
        dailyAP += ap;
        if (dailyAP < 0)
            dailyAP = 0;

        weeklyAP += ap;
        if (weeklyAP < 0)
            weeklyAP = 0;
    }

    public void addDWGlory(int glory) {
        dailyGlory += glory;
        if (dailyGlory < 0)
            dailyGlory = 0;

        weeklyGlory += glory;
        if (weeklyGlory < 0)
            weeklyGlory = 0;
    }

    /**
     * @return The daily Abyss Pointn count
     */
    public int getDailyAP() {
        return dailyAP;
    }

    /**
     * @return The weekly Abyss Point count
     */
    public int getWeeklyAP() {
        return weeklyAP;
    }

    /**
     * @return The all time Abyss Point count
     */
    public int getAp() {
        return ap;
    }

    public int getDailyGlory() {
        return dailyGlory;
    }

    public int getWeeklyGlory() {
        return weeklyGlory;
    }

    public int getGlory() {
        return glory;
    }

    /**
     * Set a new AP count
     * 
     * @param ap
     *            The ap to set
     */
    public void setAp(int ap) {
        if (ap < 0)
            ap = 0;

        this.ap = ap;

        AbyssRankEnum newRank = AbyssRankEnum.getRankForApAndGlory(this.ap, this.glory);
        if (newRank != this.rank)
            setRank(newRank);

        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    public void setGlory(int glory) {
        if (glory < 0)
            glory = 0;

        this.glory = glory;

        AbyssRankEnum newRank = AbyssRankEnum.getRankForApAndGlory(this.ap, this.glory);
        if (newRank != this.rank)
            setRank(newRank);

        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the rank
     */
    public AbyssRankEnum getRank() {
        return rank;
    }

    /**
     * @return The top ranking of the current rank
     */
    public int getTopRanking() {
        return topRanking;
    }

    /**
     * @param topRanking
     */
    public void setTopRanking(int topRanking) {
        this.topRanking = topRanking;
    }

    /**
     * @return The daily count kill
     */
    public int getDailyKill() {
        return dailyKill;
    }

    /**
     * @return The weekly count kill
     */
    public int getWeeklyKill() {
        return weeklyKill;
    }

    /**
     * @return all Kill
     */
    public int getAllKill() {
        return allKill;
    }

    /**
     * Add one kill to a player
     */
    public void setAllKill() {
        this.dailyKill += 1;
        this.weeklyKill += 1;
        this.allKill += 1;
        
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return The last week count kill
     */
    public int getLastKill() {
        return lastKill;
    }

    /**
     * @return The last week Abyss Point count
     */
    public int getLastAP() {
        return lastAP;
    }

    public int getLastGlory() {
        return lastGlory;
    }

    /**
     * @return max Rank
     */
    public int getMaxRank() {
        return maxRank;
    }

    /**
     * @param rank
     *            the rank to set
     */
    public void setRank(AbyssRankEnum rank) {
        if (rank.getQuota() != 0) {
            if (topRanking == 0) {
                rank = AbyssRankEnum.getRankById(AbyssRankEnum.getLastRankWithQuota().getId() - 1);
            }
            else {
                while (topRanking > rank.getQuota()) {
                    rank = AbyssRankEnum.getRankById(rank.getId() - 1);
                }
            }
        }
        
        // set maxrank
        if (rank.getId() > this.maxRank)
            this.maxRank = rank.getId();

        this.rank = rank;

        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the persistentState
     */
    public PersistentState getPersistentState() {
        return persistentState;
    }

    /**
     * @param persistentState
     *            the persistentState to set
     */
    public void setPersistentState(PersistentState persistentState) {
        switch (persistentState) {
            case UPDATE_REQUIRED:
                if (this.persistentState == PersistentState.NEW)
                    break;
            default:
                this.persistentState = persistentState;
        }
    }

    /**
     * @return The last update of the AbyssRank
     */
    public long getLastUpdate() {
        return lastUpdate;
    }

    /**
     * Make an update for the daily/weekly/last kill & ap counts
     */
    public void doUpdate() {
        boolean needUpdate = false;
        Calendar lastCal = Calendar.getInstance();
        lastCal.setTimeInMillis(lastUpdate);

        Calendar curCal = Calendar.getInstance();
        curCal.setTimeInMillis(System.currentTimeMillis());

        // Checking the day - month & year are checked to prevent if a player come back after 1 month, the same day
        if (lastCal.get(Calendar.DAY_OF_MONTH) != curCal.get(Calendar.DAY_OF_MONTH)
            || lastCal.get(Calendar.MONTH) != curCal.get(Calendar.MONTH)
            || lastCal.get(Calendar.YEAR) != curCal.get(Calendar.YEAR)) {
            this.dailyAP = 0;
            this.dailyKill = 0;
            this.dailyGlory = 0;

            needUpdate = true;
        }

        // Checking the week - year is checked to prevent if a player come back after 1 year, the same week
        if (lastCal.get(Calendar.WEEK_OF_YEAR) != curCal.get(Calendar.WEEK_OF_YEAR)
            || lastCal.get(Calendar.YEAR) != curCal.get(Calendar.YEAR)) {
            this.lastKill = this.weeklyKill;
            this.lastAP = this.weeklyAP;
            this.lastGlory = this.weeklyGlory;
            this.weeklyKill = 0;
            this.weeklyAP = 0;
            this.weeklyGlory = 0;
            needUpdate = true;
        }

        // Finally, update the the last update
        this.lastUpdate = System.currentTimeMillis();

        if (needUpdate)
            setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

}
