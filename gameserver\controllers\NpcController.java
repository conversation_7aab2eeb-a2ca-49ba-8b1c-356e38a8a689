/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.ai.AI;
import gameserver.ai.events.Event;
import gameserver.ai.npcai.DummyAi;
import gameserver.configs.main.CustomConfig;
import gameserver.configs.main.LegionConfig;
import gameserver.configs.network.NetworkConfig;
import gameserver.controllers.attack.AttackStatus;
import gameserver.dataholders.DataManager;
import gameserver.model.ChatType;
import gameserver.model.EmotionType;
import gameserver.model.LegionManor;
import gameserver.model.TaskId;
import gameserver.model.alliance.PlayerAlliance;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.QuestStateList;
import gameserver.model.gameobjects.player.RequestResponseHandler;
import gameserver.model.gameobjects.player.RewardType;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.group.PlayerGroup;
import gameserver.model.templates.quest.NpcQuestData;
import gameserver.model.templates.siege.SiegeSpawnLocationTemplate;
import gameserver.model.templates.teleport.TelelocationTemplate;
import gameserver.model.templates.teleport.TeleportLocation;
import gameserver.model.templates.teleport.TeleporterTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_DIALOG_WINDOW;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_LOOKATOBJECT;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_PET;
import gameserver.network.aion.serverpackets.SM_PLASTIC_SURGERY;
import gameserver.network.aion.serverpackets.SM_QUESTION_WINDOW;
import gameserver.network.aion.serverpackets.SM_REPURCHASE;
import gameserver.network.aion.serverpackets.SM_SELL_ITEM;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_TRADELIST;
import gameserver.questEngine.QuestEngine;
import gameserver.questEngine.model.QuestCookie;
import gameserver.questEngine.model.QuestState;
import gameserver.questEngine.model.QuestStatus;
import gameserver.restrictions.RestrictionsManager;
import gameserver.services.AllianceService;
import gameserver.services.CraftSkillUpdateService;
import gameserver.services.CubeExpandService;
import gameserver.services.DropService;
import gameserver.services.GroupService;
import gameserver.services.LegionService;
import gameserver.services.OpenWorldService;
import gameserver.services.PvpService;
import gameserver.services.RespawnService;
import gameserver.services.SerialService;
import gameserver.services.SiegeService;
import gameserver.services.TeleportService;
import gameserver.services.TeleportService.TeleportAnimation;
import gameserver.services.TradeService;
import gameserver.services.WarehouseService;
import gameserver.skillengine.SkillEngine;
import gameserver.skillengine.model.Skill;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.stats.StatFunctions;
import gameserver.world.World;
import gameserver.world.WorldType;

import java.util.List;
import java.util.concurrent.Future;

import org.apache.log4j.Logger;

/**
 * This class is for controlling Npc's
 * 
 * <AUTHOR> ATracer (2009-09-29), Sarynth
 */
public class NpcController extends CreatureController<Npc> {
    private static final Logger log = Logger.getLogger(NpcController.class);

    @Override
    public void notSee(VisibleObject object, boolean isOutOfRange) {
        super.notSee(object, isOutOfRange);
        if (object instanceof Player || object instanceof Summon) {
            ThreadPoolManager.getInstance().execute(new Runnable() {
                @Override
                public void run() {
                    getOwner().getAi().handleEvent(Event.NOT_SEE_PLAYER);
                }
            });
        }
    }

    @Override
    public void see(VisibleObject object) {
        super.see(object);
        Npc owner = getOwner();
        owner.getAi().handleEvent(Event.SEE_CREATURE);
        if (object instanceof Player) {
            owner.getAi().handleEvent(Event.SEE_PLAYER);

            if (owner.getMoveController().isScheduled()) {
                if (owner.getMoveController().isWalking())
                    PacketSendUtility.sendPacket((Player) object, new SM_EMOTION(owner,
                        EmotionType.WALK));
                else {
                    PacketSendUtility.sendPacket((Player) object, new SM_EMOTION(owner,
                        EmotionType.ATTACKMODE));
                }
            }
            /*
             * if (owner.getMoveController().isWalking() && owner.canSee((Player) object)) {
             * owner.getAi().clearDesires(); owner.getAi().addDesire(new AggressionDesire(owner, 100)); }
             */
        }
        else if (object instanceof Summon) {
            owner.getAi().handleEvent(Event.SEE_PLAYER);
        }
    }

    public void onCreation() {
        // To be overriden
    }

    @Override
    public void onRespawn() {
        super.onRespawn();

        cancelTask(TaskId.DECAY);

        Npc owner = getOwner();

        // if (owner != null && owner.isCustom()) {
        // DAOManager.getDAO(SpawnDAO.class).setSpawned(owner.getSpawn().getSpawnId(),
        // owner.getObjectId(), true);
        // }

        // set state from npc templates
        if (owner.getObjectTemplate() != null && owner.getObjectTemplate().getState() != 0)
            owner.setState(owner.getObjectTemplate().getState());
        else
            owner.setState(CreatureState.NPC_IDLE);

        owner.getLifeStats().setCurrentHpPercent(100);
        owner.getLifeStats().setCurrentMpPercent(100);
        owner.getAi().handleEvent(Event.RESPAWNED);

        if (owner.getSpawn().getNpcFlyState() != 0) {
            owner.setState(CreatureState.FLYING);
        }

        DropService.getInstance().unregisterDrop(getOwner());
    }

    public void onDespawn(boolean forced) {
        if (forced)
            cancelTask(TaskId.DECAY);

        Npc owner = getOwner();

        if (owner == null || !owner.isSpawned())
            return;

        owner.getAi().handleEvent(Event.DESPAWN);
        World.getInstance().despawn(owner);
    }

    @Override
    public void doReward() {
        AionObject winner = getOwner().getAggroList().getMostDamage();

        if (winner == null || winner instanceof Npc) {
            winner = getOwner().getAggroList().getMostPlayerDamage();

            if (winner == null)
                return;
        }

        if (winner instanceof Creature)
            winner = ((Creature) winner).getMaster();

        float rankMultiplier = StatFunctions.calculateRankMultipler(getOwner().getObjectTemplate()
            .getRank()) - 1;

        if (rankMultiplier < 1)
            rankMultiplier = 1;

        // if (getOwner().getWorldType() == WorldType.BALAUREA && getOwner().getInstanceId() == 1) {
        Player killer = getOwner().getAggroList().getMostPlayerDamage();

        if (killer != null) {
            if (OpenWorldService.getInstance().isInBaseOrOpenWorld(getOwner())) {
                // float value = rankMultiplier * (NetworkConfig.GAMESERVER_ID == 17 ? 3f : 2f);
                float value = rankMultiplier
                    * (SerialService.getInstance().isEnemyMap(killer) ? 4f : 2f);

                PvpService.getInstance().addMight(killer, value);
            }
        }
        // }

        if (winner instanceof PlayerAlliance) {
            AllianceService.getInstance().doReward((PlayerAlliance) winner, getOwner());
        }
        else if (winner instanceof PlayerGroup) {
            GroupService.getInstance().doReward((PlayerGroup) winner, getOwner());
        }
        else if (((Player) winner).isInGroup()) {
            GroupService.getInstance().doReward(((Player) winner).getPlayerGroup(), getOwner());
        }
        else {
            super.doReward();

            Player player = (Player) winner;

            // Exp reward
            long expReward = StatFunctions.calculateSoloExperienceReward(player, getOwner());
            player.getCommonData().addExp(expReward, RewardType.HUNTING);

            // DP reward
            int currentDp = player.getCommonData().getDp();
            int dpReward = StatFunctions.calculateSoloDPReward(player, getOwner());
            player.getCommonData().setDp(dpReward + currentDp);

            // AP reward
            WorldType worldType = World.getInstance().getWorldMap(player.getWorldId())
                .getWorldType();
            if (worldType == WorldType.ABYSS || worldType == WorldType.BALAUREA) {
                int apReward = (int) Math.round(rankMultiplier * rankMultiplier
                    * StatFunctions.calculateSoloAPReward(player, getOwner()));
                apReward = Math.round(apReward);

                player.getCommonData().addAp(apReward);
            }

            QuestEngine.getInstance().onKill(new QuestCookie(getOwner(), player, 0, 0));

            // Give Drop
            DropService.getInstance().registerDrop(getOwner(), player, player.getLevel());
        }
    }

    @Override
    public void onDie(Creature lastAttacker) {
        super.onDie(lastAttacker);

        Npc owner = getOwner();

        addTask(TaskId.DECAY, RespawnService.scheduleDecayTask(this.getOwner()));
        scheduleRespawn();

        PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner, EmotionType.DIE, 0,
            lastAttacker == null ? 0 : lastAttacker.getObjectId()));

        if (owner.getBattleground() != null)
            owner.getBattleground().onKill(owner, lastAttacker);
        else
            this.doReward();

        owner.getAi().handleEvent(Event.DIED);

        // deselect target at the end
        owner.setTarget(null);
        PacketSendUtility.broadcastPacket(owner, new SM_LOOKATOBJECT(owner));
    }

    @Override
    public Npc getOwner() {
        return (Npc) super.getOwner();
    }

    @Override
    public void onDialogRequest(Player player) {
        getOwner().getAi().handleEvent(Event.TALK);

        if (QuestEngine.getInstance().onDialog(new QuestCookie(getOwner(), player, 0, -1)))
            return;

        if (getOwner().getBattleground() != null) {
            if (getOwner().getBattleground().onTalk(getOwner(), player))
                return;
        }

        // Zephyr Deliveryman
        if (getOwner().getObjectId() == player.getZephyrObjectId()) {
            PacketSendUtility
                .sendPacket(player, new SM_DIALOG_WINDOW(getOwner().getObjectId(), 18));
            return;
        }

        int titleId = getOwner().getObjectTemplate().getTitleId();
        if ((
        // title ids of npcs
        titleId == 315018 || titleId == 350474 || titleId == 350473 || titleId == 350212
            || titleId == 350304 || titleId == 350305 || titleId == 370000 || titleId == 370003
            || titleId == 462724
        // aerolinks
        || (getOwner().getNpcId() >= 730265 && getOwner().getNpcId() <= 730269))) {
            NpcQuestData npcQD = QuestEngine.getInstance().getNpcQuestData(getOwner().getNpcId());
            QuestStateList list = player.getQuestStateList();
            List<Integer> events = npcQD.getOnTalkEvent();
            boolean hasQuestFromNpc = false;
            for (int e : events) {
                QuestState qs = list.getQuestState(e);
                if (qs != null && qs.getStatus() != QuestStatus.COMPLETE) {
                    hasQuestFromNpc = true;
                    break;
                }
                else {
                    continue;
                }
            }

            if (hasQuestFromNpc)
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(getOwner().getObjectId(),
                    10));
            else
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(getOwner().getObjectId(),
                    1011));

        }
        else {
            // Legion manor entry/exit
            LegionManor manor = SiegeService.getInstance().getManorNpcLocation(
                getOwner().getObjectId());

            if (manor != null) {
                if (manor.getLegionId() == 0 || !player.isLegionMember()
                    || manor.getLegionId() != player.getLegion().getLegionId()) {
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(getOwner()
                        .getObjectId(), 1011));
                    return;
                }
            }

            if (getOwner().isGuard())
                return;

            PacketSendUtility
                .sendPacket(player, new SM_DIALOG_WINDOW(getOwner().getObjectId(), 10));
        }
    }

    /**
     * This method should be called to make forced despawn of NPC and delete it from the world
     */
    public void onDelete() {
        if (getOwner().isInWorld()) {
            this.getOwner().getAi().clearDesires();
            this.onDespawn(true);
            this.delete();
        }
    }

    /**
     * Handle dialog
     */
    @Override
    public void onDialogSelect(int dialogId, final Player player, int questId) {
        Npc npc = getOwner();
        int targetObjectId = npc.getObjectId();

        if (QuestEngine.getInstance().onDialog(new QuestCookie(npc, player, questId, dialogId)))
            return;

        switch (dialogId) {
            case 2:
                PacketSendUtility.sendPacket(player, new SM_TRADELIST(npc, TradeService
                    .getTradeListData().getTradeListTemplate(npc.getNpcId()), player.getPrices()
                    .getVendorBuyModifier()));
                break;
            case 3:
                PacketSendUtility.sendPacket(player, new SM_SELL_ITEM(targetObjectId, player
                    .getPrices().getVendorSellModifier(player.getCommonData().getRace())));
                break;
            case 4:
                // stigma
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 1));
                break;
            case 5:
                // create legion
                if (MathUtil.isInRange(npc, player, 10)) // avoiding exploit with sending fake dialog_select packet
                {
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 2));
                }
                else {
                    PacketSendUtility.sendPacket(player,
                        SM_SYSTEM_MESSAGE.LEGION_CREATE_TOO_FAR_FROM_NPC());
                }
                break;
            case 6:
                // disband legion
                if (MathUtil.isInRange(npc, player, 10)) // avoiding exploit with sending fake dialog_select packet
                {
                    LegionService.getInstance().requestDisbandLegion(npc, player);
                }
                else {
                    PacketSendUtility.sendPacket(player,
                        SM_SYSTEM_MESSAGE.LEGION_DISPERSE_TOO_FAR_FROM_NPC());
                }
                break;
            case 7:
                // recreate legion
                if (MathUtil.isInRange(npc, player, 10)) // voiding exploit with sending fake client dialog_select
                // packet
                {
                    LegionService.getInstance().recreateLegion(npc, player);
                }
                else {
                    PacketSendUtility.sendPacket(player,
                        SM_SYSTEM_MESSAGE.LEGION_DISPERSE_TOO_FAR_FROM_NPC());
                }
                break;
            case 26:
                // warehouse
                if (MathUtil.isInRange(npc, player, 10)) // voiding exploit with sending fake client dialog_select
                // packet
                {
                    if (!RestrictionsManager.canUseWarehouse(player))
                        return;

                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 26));
                    WarehouseService.sendWarehouseInfo(player, true);
                }
                break;
            case 31:
                // TODO hotfix to prevent opening the legion wh when a quest returns false.
                break;
            case 33:
                // Consign trade?? npc karinerk, koorunerk
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 13));
                break;
            case 35:
                // soul healing
                final long expLost = player.getCommonData().getExpRecoverable();
                if (expLost == 0)
                    player.getEffectController().removeEffect(8291);
                final double factor = (expLost < 1000000 ? 0.25 - (0.00000015 * expLost) : 0.1);
                final int price = (int) (expLost * factor * CustomConfig.SOULHEALING_PRICE_MULTIPLIER);

                RequestResponseHandler responseHandler = new RequestResponseHandler(npc) {
                    @Override
                    public void acceptRequest(Creature requester, Player responder) {
                        if (player.getInventory().getKinahItem().getItemCount() >= price) {
                            PacketSendUtility.sendPacket(player,
                                SM_SYSTEM_MESSAGE.EXP(String.valueOf(expLost)));
                            PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.SOUL_HEALED());
                            player.getCommonData().resetRecoverableExp();
                            player.getInventory().decreaseKinah(price);
                            player.getEffectController().removeEffect(8291);
                        }
                        else {
                            PacketSendUtility.sendPacket(player,
                                SM_SYSTEM_MESSAGE.NOT_ENOUGH_KINAH(price));
                        }
                    }

                    @Override
                    public void denyRequest(Creature requester, Player responder) {
                        // no message
                    }
                };
                if (player.getCommonData().getExpRecoverable() > 0) {
                    boolean result = player.getResponseRequester().putRequest(
                        SM_QUESTION_WINDOW.STR_SOUL_HEALING, responseHandler);
                    if (result) {
                        PacketSendUtility.sendPacket(player, new SM_QUESTION_WINDOW(
                            SM_QUESTION_WINDOW.STR_SOUL_HEALING, 0, String.valueOf(price)));
                    }
                }
                else {
                    PacketSendUtility.sendPacket(player,
                        SM_SYSTEM_MESSAGE.DONT_HAVE_RECOVERED_EXP());
                }
                break;
            case 36:
                switch (npc.getNpcId()) {
                    case 204089:
                        TeleportService.teleportTo(player, 120010000, 1, 984f, 1543f, 222.1f, 0);
                        break;
                    case 203764:
                        TeleportService.teleportTo(player, 110010000, 1, 1462.5f, 1326.1f, 564.1f,
                            0);
                        break;
                    case 203981:
                        TeleportService.teleportTo(player, 210020000, 1, 439.3f, 422.2f, 274.3f, 0);
                        break;
                }
                break;
            case 37:
                switch (npc.getNpcId()) {
                    case 204087:
                        TeleportService.teleportTo(player, 120010000, 1, 1005.1f, 1528.9f, 222.1f,
                            0);
                        break;
                    case 203875:
                        TeleportService.teleportTo(player, 110010000, 1, 1470.3f, 1343.5f, 563.7f,
                            0);
                        break;
                    case 203982:
                        TeleportService.teleportTo(player, 210020000, 1, 446.2f, 431.1f, 274.5f, 0);
                        break;
                }
                break;
            case 41:
                // Godstone socketing
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 21));
                break;
            case 42:
                // remove mana stone
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 20));
                break;
            case 43:
                // modify appearance
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 19));
                break;
            case 44:
                // flight and teleport
                TeleportService.showMap(player, targetObjectId, npc.getNpcId());
                break;
            case 45:
                // improve extraction
            case 46:
                // learn tailoring armor smithing etc...
                CraftSkillUpdateService.getInstance().learnSkill(player, npc);
                break;
            case 47:
                // expand cube
                CubeExpandService.expandCube(player, npc);
                break;
            case 48:
                WarehouseService.expandWarehouse(player, npc);
                break;
            case 53:
                // legion warehouse
                if (LegionConfig.LEGION_WAREHOUSE)
                    if (MathUtil.isInRange(npc, player, 10))
                        LegionService.getInstance().openLegionWarehouse(player);
                break;
            case 56:
                // WTF??? Quest dialog packet
                break;
            case 58:
                if (MathUtil.isInRange(npc, player, 10)) // avoiding exploit with sending fake dialog_select packet
                {
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 28));
                }
                break;
            case 59:
                // coin reward
                PacketSendUtility.sendPacket(player, new SM_MESSAGE(0, null,
                    "This feature is not available yet", ChatType.ANNOUNCEMENTS));
                break;
            case 61:
            case 62:
                byte changesex = 1; // 0 plastic surgery, 1 gender switch
                byte check_ticket = 2; // 2 no ticket, 1 have ticket
                if (dialogId == 61) {
                    // Plastic Surgery
                    changesex = 0;
                    if (player.getInventory().getItemCountByItemId(169650000) > 0
                        || player.getInventory().getItemCountByItemId(169650001) > 0)
                        check_ticket = 1;
                }
                else {
                    // Gender Switch
                    if (player.getInventory().getItemCountByItemId(169660000) > 0
                        || player.getInventory().getItemCountByItemId(169660001) > 0)
                        check_ticket = 1;
                }
                PacketSendUtility.sendPacket(player, new SM_PLASTIC_SURGERY(player, check_ticket,
                    changesex));
                player.setEditMode(true);
                break;
            case 66:
                // armsfusion
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 29));
                break;
            case 67:
                // armsbreaking
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 30));
                break;
            case 70:
                // repurchase
                PacketSendUtility.sendPacket(player, new SM_REPURCHASE(npc, player));
                break;
            case 71:
                // adopt pet
                PacketSendUtility.sendPacket(player, new SM_PET(6));
                break;
            case 72:
                // surrender pet
                PacketSendUtility.sendPacket(player, new SM_PET(7));
                break;
            case 73:
                // housing build
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 32));
                break;
            case 74:
                // housing destruct
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 33));
                break;
            case 75:
                // condition an individual item
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 35));
                break;
            case 76:
                // condition all equipped items
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 35));
                break;
            case 77:
                // arena of discipline training
                // [TODO]
                break;
            case 78:
                // trade in exchange
                // [TODO]
                break;
            case 92:
                // greet sidekick
                PacketSendUtility.sendPacket(player, new SM_PET(16));
                break;
            case 93:
                // banish sidekick
                PacketSendUtility.sendPacket(player, new SM_PET(17));
                break;
            case 94:
                // augment an individual item
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 42));
                break;
            case 95:
                // augment all equipped items
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 42));
                break;
            case 104:
                // legion manor entry/exit
                LegionManor manor = SiegeService.getInstance().getManorNpcLocation(targetObjectId);

                if (manor == null)
                    break;

                if (npc.getName().contains("_In")) {
                    // Only allow entry to players who own the manor
                    if (!player.isLegionMember() || manor.getLegionId() == 0
                        || manor.getLegionId() != player.getLegion().getLegionId())
                        break;

                    SiegeSpawnLocationTemplate entry = manor.getEntry();
                    if (entry != null) {
                        TeleportService.teleportTo(player, player.getWorldId(),
                            player.getInstanceId(), entry.getX(), entry.getY(), entry.getZ(),
                            (byte) entry.getH(), TeleportService.TELEPORT_BEAM_DELAY);
                    }
                }
                else if (npc.getName().contains("_Out")) {
                    // But allow anyone to exit in case they are still inside
                    SiegeSpawnLocationTemplate exit = manor.getExit();
                    if (exit != null) {
                        TeleportService.teleportTo(player, player.getWorldId(),
                            player.getInstanceId(), exit.getX(), exit.getY(), exit.getZ(),
                            (byte) exit.getH(), TeleportService.TELEPORT_BEAM_DELAY);
                    }
                }
                break;
            case 125:
                // stigma enchant
                PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId, 53));
                break;
            case 10000:
                // generic npc reply (most are teleporters)
                TeleporterTemplate template = DataManager.TELEPORTER_DATA.getTeleporterTemplate(npc
                    .getNpcId());
                if (template != null) {
                    TeleportLocation loc = template.getTeleLocIdData().getTelelocations().get(0);
                    if (loc != null) {
                        player.getInventory().decreaseKinah(loc.getPrice());
                        TelelocationTemplate tlt = DataManager.TELELOCATION_DATA
                            .getTelelocationTemplate(loc.getLocId());
                        TeleportService.teleportTo(player, tlt.getMapId(), tlt.getX(), tlt.getY(),
                            tlt.getZ(), 1000);
                    }
                }
                else if (npc.getObjectTemplate().getTitleId() == 462724) { // Event buff NPC
                    Skill skill = SkillEngine.getInstance().getSkill(npc, 20950, 1, player);
                    skill.useSkill();
                }

                break;
            default:
                if (questId > 0)
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId,
                        dialogId, questId));
                else
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(targetObjectId,
                        dialogId));
                break;
        }
    }

    @Override
    public void onAttack(final Creature creature, int skillId, TYPE type, int damage, int logId,
        AttackStatus status, boolean notifyAttackedObservers, boolean sendPacket) {
        if (getOwner().getLifeStats().isAlreadyDead())
            return;

        super.onAttack(creature, skillId, type, damage, logId, status, notifyAttackedObservers,
            sendPacket);

        Npc npc = getOwner();

        Creature actingCreature = creature.getMaster();
        if (actingCreature instanceof Player)
            if (QuestEngine.getInstance().onAttack(
                new QuestCookie(npc, (Player) actingCreature, 0, 0)))
                return;

        AI<?> ai = npc.getAi();
        if (ai instanceof DummyAi) {
            // log.warn("CHECKPOINT: npc attacked without ai "
            // + npc.getObjectTemplate().getTemplateId());
            return;
        }
    }

    @Override
    public void onStartMove() {
        super.onStartMove();
    }

    @Override
    public void onMove() {
        super.onMove();
    }

    @Override
    public void onStopMove() {
        super.onStopMove();
    }

    /**
     * Schedule respawn of npc In instances - no npc respawn
     */
    public void scheduleRespawn() {
        if (getOwner().isInInstance())
            return;

        int instanceId = getOwner().getInstanceId();
        if (!getOwner().getSpawn().isNoRespawn(instanceId)) {
            Future<?> respawnTask = RespawnService.scheduleRespawnTask(getOwner());
            addTask(TaskId.RESPAWN, respawnTask);
        }
    }

    /**
     * @param lastAttacker
     */
    public void onCreatureDie(Creature lastAttacker) {
        getOwner().getAggroList().clear();
        super.onDie(lastAttacker);
    }
}