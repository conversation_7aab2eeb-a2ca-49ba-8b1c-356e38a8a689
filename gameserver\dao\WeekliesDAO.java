/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.dao;

import com.aionemu.commons.database.dao.DAO;

/**
 * 
 * <AUTHOR>
 */

public abstract class WeekliesDAO implements DAO {

    @Override
    public final String getClassName() {
        return WeekliesDAO.class.getName();
    }
    
    public abstract int getWeeklyValue(int objectid, WeeklyType type);
    public abstract boolean addWeeklyValue(int objectId, WeeklyType type, int value);
    
    public abstract boolean resetWeeklies(int objectId, WeeklyType... types);
    public abstract boolean resetWeeklies(WeeklyType... types);
    public abstract boolean resetAllWeeklies();
    
    public enum WeeklyType {
        LEGION_RECHARGER(1),
        PLAYER_NOTHING_YET(2);
        
        private int id;
        
        private WeeklyType(int id) {
            this.id = id;
        }
        
        public int getId() {
            return id;
        }
    }
}