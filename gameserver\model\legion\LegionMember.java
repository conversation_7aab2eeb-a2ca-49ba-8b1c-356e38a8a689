/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.legion;

/**
 * <AUTHOR>
 */
public class LegionMember {
    private int objectId = 0;
    protected Legion legion = null;
    protected String nickname = "";
    protected String selfIntro = "";

    protected LegionRank rank = LegionRank.LEGIONARY;

    /**
     * If player is defined later on this constructor is called
     */
    public LegionMember(int objectId) {
        this.objectId = objectId;
    }

    /**
     * This constructor is called when a legion is created
     */
    public LegionMember(int objectId, Legion legion, LegionRank rank) {
        this.setObjectId(objectId);
        this.setLegion(legion);
        this.setRank(rank);
    }

    /**
     * This constructor is called when a LegionMemberEx is called
     */
    public LegionMember() {
    }

    /**
     * @param legion
     *            the legion to set
     */
    public void setLegion(Legion legion) {
        this.legion = legion;
    }

    /**
     * @return the legion
     */
    public Legion getLegion() {
        return legion;
    }

    /**
     * @param rank
     *            the rank to set
     */
    public void setRank(LegionRank rank) {
        this.rank = rank;
    }

    /**
     * @return the rank
     */
    public LegionRank getRank() {
        return rank;
    }

    public boolean isBrigadeGeneral() {
        return rank == LegionRank.BRIGADE_GENERAL;
    }

    /**
     * @param nickname
     *            the nickname to set
     */
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    /**
     * @return the nickname
     */
    public String getNickname() {
        return nickname;
    }

    /**
     * @param selfIntro
     *            the selfIntro to set
     */
    public void setSelfIntro(String selfIntro) {
        this.selfIntro = selfIntro;
    }

    /**
     * @return the selfIntro
     */
    public String getSelfIntro() {
        return selfIntro;
    }

    /**
     * @param objectId
     *            the objectId to set
     */
    public void setObjectId(int objectId) {
        this.objectId = objectId;
    }

    /**
     * @return the objectId
     */
    public int getObjectId() {
        return objectId;
    }

    public boolean hasRights(int type) {
        int permission1 = 0;
        int permission2 = 0;
        switch (getRank()) {
            case BRIGADE_GENERAL:
                return true;
            case DEPUTY:
                permission1 = getLegion().getDeputyPermission1();
                permission2 = getLegion().getDeputyPermission2();
                break;
            case CENTURION:
                permission1 = getLegion().getCenturionPermission1();
                permission2 = getLegion().getCenturionPermission2();
                break;
            case LEGIONARY:
                permission1 = getLegion().getLegionaryPermission1();
                permission2 = getLegion().getLegionaryPermission2();
                break;
            case VOLUNTEER:
                permission1 = getLegion().getVolunteerPermission1();
                permission2 = getLegion().getVolunteerPermission2();
                break;
        }

        switch (type) {
            case 1:
                if (getRank().canInviteToLegion(permission1))
                    return true;

            case 2:
                if (getRank().canKickFromLegion(permission1))
                    return true;

            case 3:
                if (getRank().canUseLegionWarehouse(permission1, permission2))
                    return true;

            case 4:
                if (getRank().canEditAnnouncement(permission2))
                    return true;

            case 5:
                if (getRank().canUseArtifact(permission2))
                    return true;

            case 6:
                if (getRank().canUseGateGuardianStone(permission2))
                    return true;
        }
        return false;

    }
}
