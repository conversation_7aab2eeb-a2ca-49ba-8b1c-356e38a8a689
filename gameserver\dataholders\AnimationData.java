/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dataholders;

import gameserver.model.templates.AnimationTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.Unmarshaller;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 */
@XmlRootElement(name = "animations")
@XmlAccessorType(XmlAccessType.FIELD)
public class AnimationData {
    @XmlElement(name = "animation")
    private List<AnimationTemplate> animations;

    private Map<String, AnimationTemplate> templates;

    void afterUnmarshal(Unmarshaller u, Object parent) {
        templates = new HashMap<String, AnimationTemplate>();

        for (AnimationTemplate animation : animations)
            templates.put(animation.getName(), animation);

        animations = null;
    }

    public int size() {
        return templates.size();
    }

    public AnimationTemplate getAnimationTemplate(String name) {
        return templates.get(name);
    }
}
