/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.model.alliance.PlayerAlliance;
import gameserver.model.alliance.PlayerAllianceMember;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.DropService;
import gameserver.services.ItemService;
import gameserver.services.PvpService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AnohaChallenge extends Battleground {
    private boolean endCalled = false;

    private static final int INSTANCE_ID = 1005;

    private static final int INITIAL_DELAY = 15000;

    private int finishSeconds = 0;

    public AnohaChallenge() {
        super.name = "Anoha's Challenge";
        super.displayName = "Instance";
        super.minSize = 1;
        super.maxSize = 12;
        super.teamCount = 1;
        super.matchLength = INITIAL_DELAY / 1000 + 21 * 60;
        super.isAnonymous = false;
        super.afkKick = false;
        super.shouldDisband = false;
        super.isPvE = true;
        super.canResurrect = true;

        BattlegroundMap map1 = new BattlegroundMap(600090000);
        map1.addSpawn(new SpawnPosition(791, 450, 144));
        map1.setKillZ(135f);

        super.maps.add(map1);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueAlliance(players);

        if (super.getAlliances().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        synchronized (super.getAlliances()) {
            for (PlayerAlliance alliance : super.getAlliances()) {
                for (PlayerAllianceMember pla : alliance.getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    super.preparePlayerInstance(pl, INITIAL_DELAY);

                    SpawnPosition pos = getSpawnPositions().get(alliance.getBgIndex());
                    performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                }
            }
        }

        super.announceAll("Anoha's Challenge",
            "Summon Anoha to start the challenge. If you die, another player must resurrect you.",
            10000);

        createSpawns();

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endInstance(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    private void createSpawns() {
        // Spawn Room
        spawnNpc(804576, 791.03f, 489.02f, 143.47517f, 91, true); // Anoha's Sword

        spawnNpc(832923, 787.6f, 413.0f, 143f, 29, true); // Exit Shugo
    }

    private void rewardItem(Player player, int itemId, long amount) {
        ItemService.addItem(player, itemId, amount);
        PacketSendUtility.sendMessage(player, "You have received " + amount + " x [item:" + itemId
            + "].");
    }

    @Override
    public void onKill(Npc npc, Creature lastAttacker) {
        if (lastAttacker == null && super.getAlliances().size() > 0) {
            PlayerAlliance alliance = super.getAlliances().get(0);

            if (alliance.getCaptain() != null && alliance.getCaptain().isOnline())
                lastAttacker = alliance.getCaptain().getPlayer();
        }

        if (!(lastAttacker.getMaster() instanceof Player))
            return;

        Player killer = (Player) lastAttacker.getMaster();

        switch (npc.getNpcId()) {
            case 804576: // Anoha Sword
                PacketSendUtility.broadcastPacket(npc,
                    SM_SYSTEM_MESSAGE.STR_MSG_LDF5_FORTRESS_NAMED_SPAWN());
                PacketSendUtility.sendPacket(killer,
                    SM_SYSTEM_MESSAGE.STR_MSG_LDF5_FORTRESS_NAMED_SPAWN_ITEM());

                this.scheduleBossAppearance(npc.getWorldId(), npc.getInstanceId(), npc.getX(),
                    npc.getY(), npc.getZ(), npc.getHeading());

                npc.getController().onDelete();
                break;
            case 855263: // Anoha
                endInstance(false);
                break;
        }

        if (!DropService.getInstance().hasDrops(npc)) {
            npc.getController().onDelete();
        }
    }

    private void scheduleBossAppearance(final int worldId, final int instanceId, final float x,
        final float y, final float z, final byte h) {
        final int spawnDelay = Math.max(getSecondsLeft() - 20 * 60, 5) * 1000;

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                ((Npc) spawn(702644, x, y, z, h, true)).delete(spawnDelay - 1 * 1000);
            }
        }, 1 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                int bossId = 855263;

                Npc boss = ((Npc) spawn(bossId, x, y, z, h, true)).delete(60 * 60 * 1000);
            }
        }, spawnDelay);
    }

    private void alterStats(Npc npc) {
        // nothing
    }

    private void saveTime() {
        int seconds = getSecondsLeft();

        finishSeconds = seconds;
    }

    private Npc spawnNpc(int npcId, SpawnPosition pos) {
        return spawnNpc(npcId, pos.getX(), pos.getY(), pos.getZ(), pos.getHeading(), true, true);
    }

    private Npc spawnNpc(int npcId, SpawnPosition pos, boolean noHome) {
        return spawnNpc(npcId, pos.getX(), pos.getY(), pos.getZ(), pos.getHeading(), true, noHome);
    }

    private Npc spawnNpc(int npcId, float x, float y, float z, int heading, boolean noHome) {
        return spawnNpc(npcId, x, y, z, heading, true, noHome);
    }

    private Npc spawnNpc(int npcId, float x, float y, float z, int heading, boolean geo,
        boolean noHome) {
        Npc npc = (Npc) spawn(npcId, x, y, z, heading, geo);
        npc.setNoHome(noHome);

        alterStats(npc);

        return npc;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        int deadCounter = 0;
        for (PlayerAllianceMember pla : player.getPlayerAlliance().getMembers()) {
            Player pl = pla.getPlayer();
            if (pl == null) {
                deadCounter++;
                continue;
            }

            if (pl.getLifeStats().isAlreadyDead())
                deadCounter++;
        }

        if (deadCounter == player.getPlayerAlliance().size())
            endInstance(true);
    }

    public void onLeave(final Player player, boolean isLogout, boolean isAfk) {
        if (!player.isSpectating()) {
            log.info("[DEBUG] Anoha's Challenge: " + player.getName()
                + " disconnected from instance " + super.getInstanceId() + " with "
                + super.getSecondsLeft() + " seconds left.");
        }

        super.onLeaveDefault(player, true, isAfk);

        if (getRemainingAlliances() < 1)
            endInstance(true);
    }

    public void onReconnect(Player player) {
        log.info("[DEBUG] Anoha's Challenge: " + player.getName() + " reconnected to instance "
            + super.getInstanceId() + " with " + super.getSecondsLeft() + " seconds left.");
    }

    private void endInstance(boolean timer) {
        if (endCalled)
            return;

        endCalled = true;

        super.onEndFirstDefault();

        super.deleteNpcs();

        if (timer) {
            super.announceAll("Anoha's Challenge", "You have failed your mission.");

            super.onEndDefault();
            return;
        }

        saveTime();

        final FinishGrade grade = FinishGrade.getFinishGrade(finishSeconds);

        String names = "";
        for (PlayerAlliance alliance : super.getAlliances()) {
            for (PlayerAllianceMember pla : alliance.getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null)
                    continue;

                if (!names.isEmpty())
                    names += ", ";

                names += pl.getName();
            }
        }

        log.info("[DEBUG] Anoha's Challenge: " + names + " finished instance "
            + super.getInstanceId() + ". Grade " + grade.name() + " with " + finishSeconds
            + " seconds left.");

        super.announceAll("Anoha's Challenge", "You have vanquished Anoha with " + grade.name()
            + " Grade!");

        if (grade == FinishGrade.XXX)
            super.announceAll("Anoha's Challenge", "G-G-G-GODLIKE PVE HEROES!!!", 5000);

        for (PlayerAlliance alliance : super.getAlliances()) {
            for (PlayerAllianceMember pla : alliance.getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null)
                    continue;

                if (pl.getLifeStats().isAlreadyDead())
                    pl.getReviveController().fullRevive();

                super.createTimer(pl, 30);
            }
        }

        for (Player pl : super.getSpectators())
            super.createTimer(pl, 30);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                for (PlayerAlliance alliance : getAlliances()) {
                    for (PlayerAllianceMember pla : alliance.getMembers()) {
                        Player pl = pla.getPlayer();
                        if (pl == null)
                            continue;

                        switch (grade) {
                            case XXX:
                                PvpService.getInstance().addMight(pl, 666);

                                rewardItem(pl, 166030005, 15);
                                rewardItem(pl, 166020000, 15);
                                rewardItem(pl, 160005052, 20);
                                rewardItem(pl, 160005053, 20);
                                break;
                            case S:
                                PvpService.getInstance().addMight(pl, 200);

                                rewardItem(pl, 166030005, 10);
                                rewardItem(pl, 166020000, 10);
                                rewardItem(pl, 160005052, 15);
                                rewardItem(pl, 160005053, 15);
                                break;
                            case A:
                                PvpService.getInstance().addMight(pl, 100);

                                rewardItem(pl, 166030005, 7);
                                rewardItem(pl, 166020000, 7);
                                rewardItem(pl, 160005052, 10);
                                rewardItem(pl, 160005053, 10);
                                break;
                            case B:
                                PvpService.getInstance().addMight(pl, 75);

                                rewardItem(pl, 166030005, 5);
                                rewardItem(pl, 166020000, 5);
                                rewardItem(pl, 160005052, 7);
                                rewardItem(pl, 160005053, 7);
                                break;
                            case C:
                                PvpService.getInstance().addMight(pl, 50);

                                rewardItem(pl, 166030005, 3);
                                rewardItem(pl, 166020000, 3);
                                rewardItem(pl, 160005052, 5);
                                rewardItem(pl, 160005053, 5);
                                break;
                            case D:
                                PvpService.getInstance().addMight(pl, 40);

                                rewardItem(pl, 166030005, 2);
                                rewardItem(pl, 166020000, 2);
                                rewardItem(pl, 160005052, 3);
                                rewardItem(pl, 160005053, 3);
                                break;
                            case E:
                                PvpService.getInstance().addMight(pl, 30);

                                rewardItem(pl, 166030005, 1);
                                rewardItem(pl, 166020000, 1);
                                break;
                            case F:
                                PvpService.getInstance().addMight(pl, 20);

                                rewardItem(pl, 166020000, 1);
                                break;
                        }
                    }
                }
            }
        }, 5 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                onEndDefault();
            }
        }, 30 * 1000);
    }

    private enum FinishGrade {
        XXX(780),
        S(720),
        A(540),
        B(480),
        C(420),
        D(300),
        E(180),
        F(0);

        private int seconds;

        private FinishGrade(int seconds) {
            this.seconds = seconds;
        }

        public int getSeconds() {
            return seconds;
        }

        public static FinishGrade getFinishGrade(int seconds) {
            FinishGrade grade = null;

            for (FinishGrade g : FinishGrade.values()) {
                if (seconds >= g.getSeconds()
                    && (grade == null || grade.getSeconds() < g.getSeconds())) {
                    grade = g;
                }
            }

            return grade;
        }
    }
}