<?php
// Example fix for web shop purchase.php to avoid ID conflicts
// This shows how to properly handle item ID generation for web purchases

// Database connection (adjust your connection details)
$host = 'localhost';
$dbname = 'not-aion';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Function to get the next safe item ID for web shop purchases
function getNextWebShopItemId($pdo) {
    // Use a high range for web shop items to avoid conflicts
    // Start from 1000000 (1 million) to ensure no conflicts with game-generated IDs
    $stmt = $pdo->query("SELECT MAX(itemUniqueId) as max_id FROM inventory WHERE itemUniqueId >= 1000000");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['max_id'] === null) {
        return 1000000; // Start from 1 million if no web shop items exist
    } else {
        return $result['max_id'] + 1;
    }
}

// Function to safely add item to player inventory (web shop purchase)
function addWebShopItem($pdo, $playerId, $itemId, $quantity = 1) {
    try {
        // Get next safe item ID
        $itemUniqueId = getNextWebShopItemId($pdo);
        
        // Insert the item with proper default values for all columns
        $stmt = $pdo->prepare("
            INSERT INTO inventory (
                itemUniqueId, itemId, itemCount, itemColor, itemOwner, 
                isEquiped, isSoulBound, slot, itemLocation, enchant, 
                itemCreator, itemSkin, fusionedItem, optionalSocket, 
                optionalFusionSocket, conditioning, temperance, randomOption, 
                bonusEnchant, randomFusionOption, wrapped, expireTime
            ) VALUES (
                ?, ?, ?, 0, ?, 
                0, 0, 0, 0, 0, 
                'WEBSHOP', 0, 0, 0, 
                0, 0, 0, 0, 
                0, 0, 0, NULL
            )
        ");
        
        $stmt->execute([
            $itemUniqueId,
            $itemId,
            $quantity,
            $playerId
        ]);
        
        return true;
    } catch (PDOException $e) {
        error_log("Web shop item insertion failed: " . $e->getMessage());
        return false;
    }
}

// Example usage in your purchase.php:
/*
if (isset($_POST['purchase'])) {
    $playerId = $_POST['player_id'];
    $itemId = $_POST['item_id'];
    $quantity = $_POST['quantity'];
    
    if (addWebShopItem($pdo, $playerId, $itemId, $quantity)) {
        echo "Item purchased successfully!";
    } else {
        echo "Purchase failed. Please try again.";
    }
}
*/

// Alternative: Use the game server's shop system instead
// Add to shop_purchases table and let the game server handle it
function addShopPurchase($pdo, $charId, $itemId, $quantity, $isGift = false, $gifter = '') {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO shop_purchases (char_id, item_id, quantity, gift, gifter) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([$charId, $itemId, $quantity, $isGift ? 1 : 0, $gifter]);
        return true;
    } catch (PDOException $e) {
        error_log("Shop purchase insertion failed: " . $e->getMessage());
        return false;
    }
}
?>

<!-- 
IMPORTANT NOTES:

1. The main issue is that your web shop is inserting items with IDs that conflict 
   with the game server's ID factory system.

2. SOLUTION 1 (Recommended): Use the addShopPurchase() function above instead of 
   directly inserting into inventory. This uses the game server's built-in shop 
   system which handles ID generation properly.

3. SOLUTION 2: If you must insert directly, use the addWebShopItem() function 
   which uses a high ID range (1,000,000+) to avoid conflicts.

4. You need to create the shop_purchases table if it doesn't exist:

CREATE TABLE IF NOT EXISTS `shop_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `char_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` bigint(20) NOT NULL DEFAULT '1',
  `gift` tinyint(1) NOT NULL DEFAULT '0',
  `gifter` varchar(50) DEFAULT '',
  `added` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

5. The game server will automatically process items from shop_purchases table 
   when players log in (see CashShopManager.java).
-->
