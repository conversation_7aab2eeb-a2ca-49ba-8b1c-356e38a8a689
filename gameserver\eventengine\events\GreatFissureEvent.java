/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class GreatFissureEvent extends MobEvent {
    public GreatFissureEvent() {
        super.mapId = 220070000;
        super.center = new SpawnPosition(1389, 1670, 323);
        super.apBasePlayer = 500;
        super.apPoolPerPlayer = 500;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("The Great Fissure in Gelkmaros will be assaulted in 3 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at the Great Fissure in Gelkmaros starts in 2 minutes",
            1 * 60 * 1000);
        announceAll("The event at the Great Fissure in Gelkmaros starts in 1 minute", 2 * 60 * 1000);
        announceAll("The event at the Great Fissure in Gelkmaros starts in 30 seconds", 30 * 1000
            + 2 * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 3 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters in the Great Fissure in the next 3 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 3 * 60));
        }

        super.scheduleEnd(3 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217937, 1382, 1672, 324);
        spawnMob(217937, 1391, 1674, 324);
        spawnMob(217937, 1389, 1684, 325);
        spawnMob(217937, 1381, 1683, 325);
        spawnMob(217937, 1387, 1697, 326);
        spawnMob(217937, 1378, 1696, 327);
        spawnMob(217937, 1381, 1658, 323);
        spawnMob(217937, 1397, 1661, 323);
        spawnMob(217937, 1405, 1667, 326);
        spawnMob(217937, 1412, 1662, 326);
        spawnMob(217937, 1418, 1655, 327);
        spawnMob(217937, 1408, 1647, 323);
        spawnMob(217937, 1397, 1640, 323);
        spawnMob(217937, 1387, 1639, 324);
        spawnMob(217937, 1388, 1663, 323);
    }
}
