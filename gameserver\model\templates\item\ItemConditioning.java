/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.model.templates.item;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ItemConditioning")
public class ItemConditioning {
    @XmlAttribute
    private int level;

    @XmlAttribute(name = "burn_attack")
    private int burnAttack;

    @XmlAttribute(name = "burn_defend")
    private int burnDefend;

    @XmlAttribute(name = "charge_price1")
    private float chargePrice1;

    @XmlAttribute(name = "charge_price2")
    private float chargePrice2;

    @XmlElement(name = "stats", required = true)
    private ConditioningLevel[] stats;
    
    @XmlAttribute(name = "charge_way")
    private int chargeWay;

    /**
     * @return the level
     */
    public int getLevel() {
        return level;
    }

    /**
     * @return the burnAttack
     */
    public int getBurnAttack() {
        return burnAttack;
    }

    /**
     * @return the burnDefend
     */
    public int getBurnDefend() {
        return burnDefend;
    }

    /**
     * @return the chargePrice1
     */
    public float getChargePrice1() {
        return chargePrice1;
    }

    /**
     * @return the chargePrice2
     */
    public float getChargePrice2() {
        return chargePrice2;
    }

    /**
     * @return the stats
     */
    public ConditioningLevel[] getStats() {
        return stats;
    }

    /**
     * @return the chargeWay
     */
    public int getChargeWay() {
        return chargeWay;
    }
}
