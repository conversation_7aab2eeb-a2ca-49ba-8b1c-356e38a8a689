/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.model.alliance.PlayerAlliance;
import gameserver.model.alliance.PlayerAllianceMember;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Gatherable;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.skillengine.effect.EffectTemplate;
import gameserver.skillengine.effect.PulledEffect;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.ThreadPoolManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class TwoAllianceResourceBg extends Battleground {
    private int extraCounter = 0;
    private Map<Integer, Integer> teamPoints = new ConcurrentHashMap<Integer, Integer>();
    private List<Gatherable> resources = new ArrayList<Gatherable>();
    private List<SpawnPosition> resourcePositions;

    public TwoAllianceResourceBg() {
        super.name = "2-Alliance War on Resources";
        super.description = "You are on a team and must gather as many resources as possible (Ore). Hinder the enemy as much as possible, but remember also to gather. You will respawn at your base every 30 seconds.";
        super.minSize = 6;
        super.maxSize = 12;
        super.teamCount = 2;
        super.matchLength = 330;

        BattlegroundMap map1 = new BattlegroundMap(220020000);
        map1.addSpawn(new SpawnPosition(2895.0f, 2866.0f, 193.89f));
        map1.addSpawn(new SpawnPosition(2834.0f, 2655.0f, 202.82f));
        map1.setKillZ(180f);

        super.maps.add(map1);
    }

    private void createResourcePositionList() {
        if (getMapId() == 220020000) {
            resourcePositions = new ArrayList<SpawnPosition>();
            resourcePositions.add(new SpawnPosition(2982f, 2709f, 218.31036f));
            resourcePositions.add(new SpawnPosition(2988f, 2726f, 218.31036f));
            resourcePositions.add(new SpawnPosition(2973f, 2695f, 218.31036f));
            resourcePositions.add(new SpawnPosition(2977f, 2777f, 216.41699f));
            resourcePositions.add(new SpawnPosition(2955f, 2797f, 213.02615f));
            resourcePositions.add(new SpawnPosition(2912f, 2682f, 212.54659f));
            resourcePositions.add(new SpawnPosition(2886f, 2741f, 202.875f));
            resourcePositions.add(new SpawnPosition(2900f, 2742f, 202.875f));
            resourcePositions.add(new SpawnPosition(2903f, 2753f, 202.875f));
            resourcePositions.add(new SpawnPosition(2971f, 2742f, 213.25f));
        }
    }

    @Override
    public boolean isEffectAllowed(EffectTemplate et) {
        if (et instanceof PulledEffect)
            return false;
        return true;
    }

    public void createMatch(List<Player> players) {
        super.handleQueueAlliance(players);

        if (super.getAlliances().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        synchronized (super.getAlliances()) {
            for (PlayerAlliance alliance : super.getAlliances()) {
                for (PlayerAllianceMember pla : alliance.getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    super.preparePlayer(pl, 30000);

                    if (!pl.getSkillList().isSkillPresent(30001))
                        pl.getSkillList().addSkill(pl, 30001, 15, true);

                    SpawnPosition pos = getSpawnPositions().get(alliance.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 30000);

        startBattleground(30000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingAlliances() <= 1)
                    endTwoAllianceResourceMatch();
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoAllianceResourceMatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    @Override
                    public void run() {
                        extraCounter++;

                        if ((extraCounter % 2) == 0)
                            spawnGatherNode();

                        if ((extraCounter % 3) == 0)
                            showTeamPositions();

                        if ((extraCounter % 6) == 0) {
                            extraCounter = 0;
                            autoResurrection();
                        }
                    }
                }, 5 * 1000, 5 * 1000));
            }
        }, 30 * 1000);
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    @Override
    public void onResourceGathered(Gatherable resource, int teamIndex) {
        resources.remove(resource);

        if (super.isDone())
            return;

        if (super.getAlliances().get(teamIndex) != null) {
            for (PlayerAllianceMember pla : super.getAlliances().get(teamIndex).getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null)
                    continue;

                PvpService.getInstance().addMight(pl, 2);
            }
        }

        addPoints(super.getAlliances().get(teamIndex), 1);
    }

    private void addPoints(PlayerAlliance alliance, int points) {
        if (alliance == null)
            return;

        Integer result = teamPoints.get(alliance.getBgIndex());
        if (result != null)
            teamPoints.put(alliance.getBgIndex(), result + points);
        else
            teamPoints.put(alliance.getBgIndex(), points);
    }

    private void spawnGatherNode() {
        if (resourcePositions == null || resourcePositions.size() < 1)
            createResourcePositionList();

        SpawnPosition pos = resourcePositions.get(Rnd.get(resourcePositions.size()));
        if (pos == null)
            return;

        Gatherable resource = SpawnEngine.getInstance().spawnResource(this, getMapId(), 400201,
            pos.getX(), pos.getY(), pos.getZ(), (byte) 0);
        if (resource != null)
            resources.add(resource);
    }

    private void showTeamPositions() {
        String msg = "";
        msg += String.format("Blue: %d - Green: %d", teamPoints.containsKey(0) ? teamPoints.get(0)
            : 0, teamPoints.containsKey(1) ? teamPoints.get(1) : 0);

        for (PlayerAlliance alliance : super.getAlliances()) {
            for (PlayerAllianceMember pla : alliance.getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null)
                    continue;

                super.scheduleAnnouncement(pl, msg, 0);
            }
        }
        super.specAnnounce(msg);
    }

    private void autoResurrection() {
        for (PlayerAlliance alliance : super.getAlliances()) {
            for (PlayerAllianceMember pla : alliance.getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null)
                    continue;

                if (pl.getLifeStats().isAlreadyDead()) {
                    pl.getReviveController().fullRevive();
                    super.spawnProtection(pl);
                    SpawnPosition pos = getSpawnPositions().get(alliance.getBgIndex());
                    TeleportService.teleportTo(pl, getMapId(), super.getInstanceId(), pos.getX(),
                        pos.getY(), pos.getZ(), 0);
                    super
                        .scheduleAnnouncement(pl, "You have been automatically resurrected!", 1500);
                }
            }
        }
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super.scheduleAnnouncement(player, "You will automatically resurrect every 30 seconds.", 0);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerAlliance() == null) {
                log.error("PlayerAlliance == null in TwoAllianceResourceBg!");
            }
            else {
                for (PlayerAllianceMember pla : killer.getPlayerAlliance().getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    PvpService.getInstance().addMight(pl, 1);
                }
            }
        }
    }

    private int getTeamPoints(int teamIndex) {
        if (!teamPoints.containsKey((Integer) teamIndex))
            return 0;
        return teamPoints.get((Integer) teamIndex);
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingAlliances() <= 1)
            endTwoAllianceResourceMatch();
    }

    private void endTwoAllianceResourceMatch() {
        super.onEndFirstDefault();

        PlayerAlliance winner = null;
        boolean isDraw = false;
        if (getTeamPoints(0) == getTeamPoints(1))
            isDraw = true;
        else if (getTeamPoints(0) > getTeamPoints(1))
            winner = super.getAlliances().get(0);
        else if (getTeamPoints(1) > getTeamPoints(0))
            winner = super.getAlliances().get(1);

        if (isDraw) {
            for (PlayerAlliance alliance : super.getAlliances()) {
                for (PlayerAllianceMember pla : alliance.getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    super.scheduleAnnouncement(pl, "The battleground has ended in a draw!", 0);
                    super.scheduleAnnouncement(pl,
                        "For your effort you have been rewarded with some might.", 3000);
                    super.rewardPlayer(pl, 10, false);
                }
            }
            super.specAnnounce("The battleground has ended in a draw!");
        }
        else if (winner != null) {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageAllianceRating(super.getAlliances());

            for (PlayerAlliance alliance : super.getAlliances()) {
                for (PlayerAllianceMember pla : alliance.getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    if (alliance.getObjectId() == winner.getObjectId()) {
                        int premadeCheck = super.premadeOpponentCheck(alliance.getBgIndex()) ? 2
                            : 1;

                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff()
                            * averageRating
                            / (float) alliance.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE / 2);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.scheduleAnnouncement(pl, "Your team has won the match with "
                            + teamPoints.get(alliance.getBgIndex()) + " points!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                        super.rewardPlayer(pl, 20, true);
                    }
                    else {
                        super.playerLoseMatch(pl, -super.K_VALUE / 3);

                        super.scheduleAnnouncement(
                            pl,
                            "Your team has lost the match with "
                                + getTeamPoints(alliance.getBgIndex()) + " to "
                                + getTeamPoints(winner.getBgIndex()) + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);
                        super.rewardPlayer(pl, 10, false);
                    }
                }
            }
            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match!");
        }
        else {
            log.error("No winner or draw in a TwoAllianceResource match!??!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}