/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.dao;

import gameserver.model.gameobjects.player.Player;

import com.aionemu.commons.database.dao.DAO;

/**
 * 
 * <AUTHOR>
 */

public abstract class BgLogDAO implements DAO {

    @Override
    public final String getClassName() {
        return BgLogDAO.class.getName();
    }
    
    public abstract int insertBg(String name);
    public abstract boolean addPlayerToBg(int bgId, Player player);
    public abstract boolean addPremadeToBg(int bgId, int premadeId, int size);
    public abstract boolean updateWinner(int bgId, int winnerId);
    public abstract boolean updateLeaver(int bgId, int playerId, boolean left);
}