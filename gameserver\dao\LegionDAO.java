/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.dao;

import gameserver.model.legion.Legion;
import gameserver.model.legion.LegionEmblem;
import gameserver.model.legion.LegionHistory;
import gameserver.model.legion.LegionWarehouse;
import gnu.trove.TIntIntHashMap;

import java.sql.Timestamp;
import java.util.TreeMap;

/**
 * Class that is responsible for storing/loading legion data
 * 
 * <AUTHOR>
 */

public abstract class LegionDAO implements IDFactoryAwareDAO {

    /**
     * Returns true if name is used, false in other case
     * 
     * @param name
     *            name to check
     * @return true if name is used, false in other case
     */
    public abstract boolean isNameUsed(String name);

    /**
     * Creates legion in DB
     * 
     * @param legion
     */
    public abstract boolean saveNewLegion(Legion legion);

    /**
     * Stores legion to DB
     * 
     * @param legion
     */
    public abstract void storeLegion(Legion legion);

    /**
     * Loads a legion
     * 
     * @param legionName
     * @return
     */
    public abstract Legion loadLegion(String legionName);

    /**
     * Loads a legion
     * 
     * @param legionId
     * @return Legion
     */
    public abstract Legion loadLegion(int legionId);

    /**
     * Removes legion and all related data (Done by CASCADE DELETION)
     * 
     * @param legionId
     *            legion to delete
     */
    public abstract void deleteLegion(int legionId);

    /**
     * Returns the announcement list of a legion
     * 
     * @param legion
     * @return announcementList
     */
    public abstract TreeMap<Timestamp, String> loadAnnouncementList(int legionId);

    /**
     * Creates announcement in DB
     * 
     * @param legionId
     * @param currentTime
     * @param message
     * @return true or false
     */
    public abstract boolean saveNewAnnouncement(int legionId, Timestamp currentTime, String message);

    /**
     * Identifier name for all LegionDAO classes
     * 
     * @return LegionDAO.class.getName()
     */
    @Override
    public final String getClassName() {
        return LegionDAO.class.getName();
    }

    /**
     * Stores a legion emblem in the database
     * 
     * @param legionId
     * @param emblemId
     * @param red
     * @param green
     * @param blue
     */
    public abstract void storeLegionEmblem(int legionId, LegionEmblem legionEmblem);

    /**
     * @param legionId
     * @param key
     * @return
     */
    public abstract void removeAnnouncement(int legionId, Timestamp key);

    /**
     * Loads a legion emblem
     * 
     * @param legion
     * @return LegionEmblem
     */
    public abstract LegionEmblem loadLegionEmblem(int legionId);

    /**
     * Loads the warehouse of legions
     * 
     * @param legion
     * @return Storage
     */
    public abstract LegionWarehouse loadLegionStorage(Legion legion);

    /**
     * @return the legion ranking
     */
    public abstract TIntIntHashMap loadLegionRanking();

    /**
     * @param legion
     */
    public abstract void loadLegionHistory(Legion legion);

    /**
     * @param legionId
     * @param legionHistory
     * @return true if query successful
     */
    public abstract boolean saveNewLegionHistory(int legionId, LegionHistory legionHistory);
}
