/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.itemengine.actions;

import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.ActionObserver.ObserverType;
import gameserver.controllers.movement.StartMovingListener;
import gameserver.dataholders.DataManager;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.MountTemplate;
import gameserver.network.aion.serverpackets.SM_ITEM_USAGE_ANIMATION;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.ArenaService;
import gameserver.services.MountService;
import gameserver.skillengine.effect.EffectId;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.StringTokenizer;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MountAction")
public class MountAction extends AbstractItemAction {
    @XmlAttribute
    protected int mountId;

    @Override
    public boolean canAct(Player player, Item parentItem, Item targetItem) {
        if (player.isUsingMount()) {
            MountService.stopMount(player);
            return false;
        }

        MountTemplate mountTemplate = DataManager.MOUNT_DATA.getMountTemplate(mountId);
        if (mountTemplate == null)
            return false;
        else if (player.getLifeStats().getCurrentFp() < mountTemplate.getStats().getFpstart())
            return false;
        else if (player.getBattleground() != null || ArenaService.getInstance().isInArena(player))
            return false;
        else if (player.isInCombat())
            return false;
        else if (player.getEffectController().isAbnormalSet(EffectId.INVISIBLE_RELATED))
            return false;

        StringTokenizer st = new StringTokenizer(mountTemplate.getItemId(), ",");
        if (st != null) {
            int count = st.countTokens();

            for (int i = 0; i < count; i++) {
                if (player.getInventory().getItemCountByItemId(Integer.parseInt(st.nextToken())) > 0)
                    return true;
            }
        }

        return false;
    }

    @Override
    public void act(final Player player, final Item parentItem, Item targetItem) {
        player.getController().cancelTask(TaskId.ITEM_USE);

        player.setUsingObject(true);

        final int itemObjId = parentItem.getObjectId();
        final int itemId = parentItem.getItemTemplate().getTemplateId();

        PacketSendUtility.broadcastPacket(player, new SM_ITEM_USAGE_ANIMATION(player.getObjectId(),
            itemObjId, itemId, 3000, 0, 0), true);

        final ActionObserver moveObserver = new StartMovingListener() {
            @Override
            public void moved() {
                if (player.getController().hasActiveTask(TaskId.ITEM_USE)) {
                    player.getController().cancelTask(TaskId.ITEM_USE);
                    PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.STR_ITEM_CANCELED());
                    PacketSendUtility.broadcastPacket(player,
                        new SM_ITEM_USAGE_ANIMATION(player.getObjectId(), itemObjId, itemId, 0, 3,
                            0), true);
                }
            }
        };

        final ActionObserver attackedObserver = new ActionObserver(ObserverType.ATTACKED) {
            @Override
            public void attacked(Creature creature) {
                if (player.getController().hasActiveTask(TaskId.ITEM_USE)) {
                    player.getController().cancelTask(TaskId.ITEM_USE);
                    PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.STR_ITEM_CANCELED());
                    PacketSendUtility.broadcastPacket(player,
                        new SM_ITEM_USAGE_ANIMATION(player.getObjectId(), itemObjId, itemId, 0, 3,
                            0), true);
                }
            }
        };

        player.getObserveController().attach(moveObserver);
        player.getObserveController().attach(attackedObserver);

        player.getController().addTask(TaskId.ITEM_USE,
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    player.setUsingObject(false);

                    player.getObserveController().removeObserver(moveObserver);
                    player.getObserveController().removeObserver(attackedObserver);

                    if (player.isUsingMount())
                        MountService.stopMount(player);
                    
                    if (player.getEffectController().isAbnormalSet(EffectId.INVISIBLE_RELATED))
                        return;

                    MountService.startMount(player, mountId);
                    PacketSendUtility.broadcastPacket(player,
                        new SM_ITEM_USAGE_ANIMATION(player.getObjectId(), itemObjId, itemId, 0, 1,
                            0), true);
                }
            }, 3000));
    }
}
