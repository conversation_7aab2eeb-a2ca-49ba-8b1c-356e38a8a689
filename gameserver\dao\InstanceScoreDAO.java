/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dao;

import gameserver.model.gameobjects.player.Player;

import java.util.Collection;

import com.aionemu.commons.database.dao.DAO;

/**
 * <AUTHOR>
 */
public abstract class InstanceScoreDAO implements DAO {
    @Override
    public String getClassName() {
        return InstanceScoreDAO.class.getName();
    }
    
    public class InstanceScore {
        private int playerId;
        private int instanceId;
        private int score;
        private int objectives;
        private int secondsLeft;
        private int extra;
        
        public InstanceScore(int playerId, int instanceId, int score, int objectives,
            int secondsLeft, int extra) {
            this.playerId = playerId;
            this.instanceId = instanceId;
            this.score = score;
            this.objectives = objectives;
            this.secondsLeft = secondsLeft;
            this.extra = extra;
        }

        public int getPlayerId() {
            return playerId;
        }

        public int getInstanceId() {
            return instanceId;
        }

        public int getScore() {
            return score;
        }

        public int getObjectives() {
            return objectives;
        }

        public int getSecondsLeft() {
            return secondsLeft;
        }

        public int getExtra() {
            return extra;
        }
    }

    public abstract boolean insertScore(Player player, int instanceId, int score, int objectives, int secondsLeft, int extra);
    public abstract Collection<InstanceScore> getScores(Player player);
    public abstract boolean deleteScores(int playerObjId);
}
