package gameserver.model.templates.bossevent;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "boss", propOrder = { "npcId" })
public class BossTemplate {

    @XmlElement(name = "npc_id", required = true)
    @XmlSchemaType(name = "nonNegativeInteger")
    protected Integer npcId = 0;
    @XmlAttribute(name = "scale_factor")
    protected Integer scaleFactor;

    public int getNpcId() {
        return npcId.intValue();
    }

    public int getScaleFactor() {
        if (scaleFactor == null) {
            return 1;
        }
        else {
            return scaleFactor;
        }
    }
}
