/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class RedManeCavernEvent extends MobEvent {
    public RedManeCavernEvent() {
        super.mapId = 220040000;
        super.center = new SpawnPosition(2304, 1253, 506);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("An event at the Red Mane Cavern in Beluslan will commence in 3 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at the Red Mane Cavern starts in 1 minute", 2 * 60 * 1000);
        announceAll("The event at the Red Mane Cavern starts in 30 seconds", 30 * 1000 + 2
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 3 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters at the Red Mane Cavern in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217079, 2280, 1202, 467);
        spawnMob(217079, 2300, 1202, 466);
        spawnMob(217079, 2313, 1213, 466);
        spawnMob(217079, 2301, 1231, 470);
        spawnMob(217079, 2316, 1232, 468);
        spawnMob(217079, 2343, 1209, 464);
        spawnMob(217079, 2327, 1250, 469);
        spawnMob(217079, 2348, 1232, 466);
        spawnMob(217079, 2349, 1254, 466);
        spawnMob(217079, 2357, 1268, 468);
        spawnMob(217079, 2332, 1270, 464);
        spawnMob(217079, 2306, 1276, 465);
        spawnMob(217079, 2296, 1257, 465);
        spawnMob(217079, 2285, 1242, 465);
        spawnMob(217079, 2276, 1222, 468);
        spawnMob(217079, 2289, 1213, 468);
        spawnMob(217079, 2336, 1218, 466);
        spawnMob(217079, 2358, 1215, 464);
        spawnMob(217079, 2360, 1237, 470);
        spawnMob(217079, 2316, 1262, 467);
    }
}
