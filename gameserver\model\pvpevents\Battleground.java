/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.controllers.SummonController.UnsummonType;
import gameserver.dao.BgLogDAO;
import gameserver.dao.LadderDAO;
import gameserver.dao.MightDAO;
import gameserver.dataholders.DataManager;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.EmotionType;
import gameserver.model.Race;
import gameserver.model.TaskId;
import gameserver.model.alliance.PlayerAlliance;
import gameserver.model.alliance.PlayerAllianceEvent;
import gameserver.model.alliance.PlayerAllianceMember;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Gatherable;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.StaticDoor;
import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.state.CreatureSeeState;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.gameobjects.state.CreatureVisualState;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.group.GroupEvent;
import gameserver.model.group.PlayerGroup;
import gameserver.model.templates.StaticDoorTemplate.DoorState;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_ABYSS_RANK;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_MOTION;
import gameserver.network.aion.serverpackets.SM_PLAYER_INFO;
import gameserver.network.aion.serverpackets.SM_PLAYER_STATE;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.AllianceService;
import gameserver.services.DuelService;
import gameserver.services.GloryService;
import gameserver.services.GroupService;
import gameserver.services.InstanceService;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.services.TeleportService.TeleportAnimation;
import gameserver.skillengine.effect.EffectId;
import gameserver.skillengine.effect.EffectTemplate;
import gameserver.skillengine.model.DispelCategoryType;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillTargetSlot;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.spawnengine.SpawnEngine;
import gameserver.taskmanager.tasks.StatUpdater;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.idfactory.IDFactory;
import gameserver.world.Executor;
import gameserver.world.World;
import gameserver.world.WorldMapInstance;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.dao.DAOManager;
import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public abstract class Battleground {
    protected static Logger log = Logger.getLogger(Battleground.class);

    protected static final int TELEPORT_DEFAULT_DELAY = 2100;
    protected static final int K_VALUE = 20;
    protected static final int MAX_RATING_DIFF = 200;
    protected static final int NPC_SPAWN_DELAY = 50;

    protected static final Map<String, Class<?>> aliases = new HashMap<String, Class<?>>() {
        {
            put("smallresource", TwoTeamResourceBg.class);
            put("2koth", TwoTeamKingBg.class);
            put("koth", ThreeTeamKingBg.class);
            put("dm", DeathmatchBg.class);
            put("solo", SoloSurvivorBg.class);
            put("4team", FourTeamBg.class);
            put("2team", TwoTeamBg.class);
            put("small", TwoTeamSmallBg.class);
            put("resource", TwoAllianceResourceBg.class);
            put("3killcount", ThreeTeamKillCountBg.class);
            put("2ally", TwoAllianceKillCountBg.class);
            put("6killcount", SixTeamKillCountBg.class);
            put("hungergames", HungerGamesEvent.class);
            put("2killcount", TwoTeamKillCountBg.class);
            put("capturepoints", TwoTeamCapturePointsBg.class);
            put("capturesmall", TwoTeamCaptureSmallBg.class);
            put("frozen", DefenseOfTheFrozenTemple.class);
            put("slippery", SlipperySlope.class);
            put("drana", DefenseOfTheDrana.class);
            put("jotun", JotunStronghold.class);
            put("anoha", AnohaChallenge.class);
        }
    };

    protected String name = "";
    protected String description = "";
    protected String displayName = "Battleground";
    protected int minSize = 0;
    protected int maxSize = 0;
    protected int teamCount = 0;
    protected int matchLength = 0;
    protected boolean minMaxOnly = false;
    protected List<BattlegroundMap> maps = new ArrayList<BattlegroundMap>();

    protected int instanceId = -1;
    protected Integer bgId = -1;
    protected long startStamp = 0;
    protected boolean isTournament = false;
    protected boolean isEvent = false;
    protected boolean is1v1 = false;
    protected boolean isPvE = false;
    protected boolean is2v2 = false;
    protected WorldMapInstance instance = null;
    protected ScheduledFuture<?> expireTask = null;
    protected ScheduledFuture<?> backgroundTask = null;
    protected ScheduledFuture<?> extraTask = null;
    protected int backgroundCounter = 0;
    protected BattlegroundMap map = null;
    protected int mapId = 0;
    protected boolean done = false;
    protected boolean started = false;
    protected boolean special = false;

    protected boolean shouldDisband = true;
    protected boolean teamBased = false;
    protected boolean isAnonymous = true;
    protected boolean afkKick = true;
    protected boolean canResurrect = false;

    private int averageParticipantRating = 1000;

    private int databaseId = 0;

    protected List<Player> _players = Collections.synchronizedList(new ArrayList<Player>());
    protected List<PlayerGroup> _groups = Collections
        .synchronizedList(new ArrayList<PlayerGroup>());
    protected List<PlayerAlliance> _alliances = Collections
        .synchronizedList(new ArrayList<PlayerAlliance>());

    protected List<Player> _spectators = Collections.synchronizedList(new ArrayList<Player>());

    protected Map<Integer, AionObject> _leavers = new ConcurrentHashMap<Integer, AionObject>();
    protected Map<Integer, Boolean> _leaversDead = new ConcurrentHashMap<Integer, Boolean>();

    public abstract void createMatch(List<Player> players);

    public abstract void startMatch();

    public abstract void onDie(final Player player, Creature lastAttacker);

    public abstract void onLeave(Player player, boolean isLogout, boolean isAfk);

    public void onArtifactDie(int teamIndex) {
        // @Override by KotH
    }

    public void onResourceGathered(Gatherable resource, int teamIndex) {
        // @Override by War on Resources
    }

    public boolean isStealthRestricted() {
        // @Override by BG's where stealth is not allowed
        return false;
    }

    public boolean isEffectAllowed(EffectTemplate et) {
        // @Override by BG's with custom effect restrictions
        return true;
    }

    public void onKill(Npc npc, Creature lastAttacker) {
        // @Override by instances
    }

    public boolean onTalk(Npc npc, Player player) {
        // @Override by instances
        return false;
    }

    public void onReconnect(Player player) {
        // @Override
    }

    public void onStatRecomputation(Player player) {
        if (player.isSpectating()) {
            player.getGameStats().setStat(StatEnum.SPEED, 14000);
            player.getGameStats().setStat(StatEnum.SPEED, 0, true);
            player.getGameStats().setStat(StatEnum.FLY_SPEED, 16000);
            player.getGameStats().setStat(StatEnum.FLY_SPEED, 0, true);
        }
    }

    public boolean isFlightRestricted() {
        if (map != null && map.isRestrictFlight())
            return true;

        return false;
    }

    public boolean createTournament(List<List<Player>> teams) {
        // @Override by Tournament BG's
        return false;
    }

    public List<SpawnPosition> getSpawnPositions() {
        if (map == null) {
            log.error("map == null for " + this.toString() + "!");
            return new ArrayList<SpawnPosition>();
        }
        else
            return map.getSpawnPoints();
    }

    protected int getRandomSize(int playerCount) {
        int avgCount = (int) Math.floor(playerCount / getTeamCount());

        if (isTeamBased())
            return avgCount;
        else if (isMinMaxOnly() && avgCount <= getMaxSize() && avgCount >= getMinSize())
            return getMinSize();
        else if (avgCount <= getMaxSize() && avgCount >= getMinSize())
            return avgCount;
        else if (avgCount < getMinSize())
            return getMinSize();
        else if (isEvent() || isTournament())
            return getMaxSize();
        else if (isMinMaxOnly())
            return getMaxSize();
        else
            return Rnd.get(getMinSize(), getMaxSize());
    }

    protected boolean handleQueueSolo(List<Player> players) {
        int size = getRandomSize(players.size());

        if (players.size() < size)
            return false;

        int playerIndex = 0;
        while (players.size() > 0) {
            Player pl = players.remove(0);

            if (pl == null)
                continue;

            pl.setBgIndex(playerIndex);
            pl.setBattleground(this);
            addPlayer(pl);

            removePlayerFromTeam(pl);
            LadderService.getInstance().unregisterFromQueue(pl);

            playerIndex++;

            if (getPlayers().size() >= size)
                break;
        }

        return true;
    }

    protected boolean handleQueueGroup(List<Player> players) {
        int groupSize = getRandomSize(players.size());

        if (players.size() < groupSize * getTeamCount())
            return false;

        int groupIndex = 0;
        while (players.size() >= groupSize) {
            List<Player> groupPlayers = new ArrayList<Player>();

            Player captain = null;

            while (groupPlayers.size() < groupSize && players.size() > 0) {
                Player pl = players.remove(0);

                if (pl != null) {
                    groupPlayers.add(pl);
                    pl.setBattleground(this);

                    if (captain == null && pl.isInGroup()
                        && pl.getPlayerGroup().getGroupLeader() == pl)
                        captain = pl;

                    removePlayerFromTeam(pl);
                    LadderService.getInstance().unregisterFromQueue(pl);
                }
            }

            PlayerGroup group = new PlayerGroup(IDFactory.getInstance().nextId(),
                groupPlayers.get(0));
            for (int i = 1; i < groupPlayers.size(); i++)
                group.addPlayerToGroup(groupPlayers.get(i));

            if (captain != null) {
                group.setGroupLeader(captain);
                group.updateGroupUIToEvent(captain, GroupEvent.CHANGELEADER);
            }

            group.setBgIndex(groupIndex);
            group.computeAverageRating();

            addGroup(group);

            groupIndex++;

            if (getGroups().size() >= getTeamCount())
                break;
        }

        return true;
    }

    protected boolean handleQueueAlliance(List<Player> players) {
        int allianceSize = getRandomSize(players.size());

        if (players.size() < allianceSize * getTeamCount())
            return false;

        int allianceIndex = 0;
        while (players.size() >= allianceSize) {
            List<Player> alliancePlayers = new ArrayList<Player>();

            Player captain = null;

            while (alliancePlayers.size() < allianceSize && players.size() > 0) {
                Player pl = players.remove(0);

                if (pl != null) {
                    alliancePlayers.add(pl);
                    pl.setBattleground(this);

                    if (captain == null && pl.isInAlliance()
                        && pl.getPlayerAlliance().getCaptainObjectId() == pl.getObjectId())
                        captain = pl;

                    removePlayerFromTeam(pl);
                    LadderService.getInstance().unregisterFromQueue(pl);
                }
            }

            PlayerAlliance alliance = new PlayerAlliance(IDFactory.getInstance().nextId(),
                alliancePlayers.get(0).getObjectId());
            for (int i = 0; i < alliancePlayers.size(); i++)
                AllianceService.getInstance().addMemberToAlliance(alliance, alliancePlayers.get(i));

            if (captain != null) {
                alliance.setLeader(captain.getObjectId());
                AllianceService.getInstance().broadcastAllianceInfo(alliance,
                    PlayerAllianceEvent.SYSTEM_CAPTAIN);
            }

            alliance.setBgIndex(allianceIndex);
            alliance.computeAverageRating();

            addAlliance(alliance);

            allianceIndex++;

            if (getAlliances().size() >= getTeamCount())
                break;
        }

        return true;
    }

    protected boolean createGroups(List<List<Player>> teams) {
        if (teams.size() < getTeamCount())
            return false;

        int groupSize = 100; // any high value
        for (List<Player> team : teams)
            if (team.size() < groupSize)
                groupSize = team.size();

        if (groupSize < 1)
            return false;

        int groupIndex = 0;
        while (getGroups().size() < getTeamCount()) {
            List<Player> players = teams.remove(0);

            while (players.size() > groupSize)
                players.remove(players.size());

            for (Player pl : players) {
                pl.setBattleground(this);
                removePlayerFromTeam(pl);
                LadderService.getInstance().unregisterFromQueue(pl);
            }

            PlayerGroup group = new PlayerGroup(IDFactory.getInstance().nextId(), players.get(0));
            for (int i = 1; i < players.size(); i++)
                group.addPlayerToGroup(players.get(i));

            group.setBgIndex(groupIndex);
            addGroup(group);

            groupIndex++;
        }

        return true;
    }

    protected boolean createAlliances(List<List<Player>> teams) {
        if (teams.size() < getTeamCount())
            return false;

        int allianceSize = 100; // any high value
        for (List<Player> team : teams)
            if (team.size() < allianceSize)
                allianceSize = team.size();

        if (allianceSize < 1)
            return false;

        int allianceIndex = 0;
        while (getAlliances().size() < getTeamCount()) {
            List<Player> players = teams.remove(0);

            while (players.size() > allianceSize)
                players.remove(players.size());

            for (Player pl : players) {
                pl.setBattleground(this);
                removePlayerFromTeam(pl);
                LadderService.getInstance().unregisterFromQueue(pl);
            }

            PlayerAlliance alliance = new PlayerAlliance(IDFactory.getInstance().nextId(), players
                .get(0).getObjectId());
            for (int i = 0; i < players.size(); i++)
                AllianceService.getInstance().addMemberToAlliance(alliance, players.get(i));

            alliance.setBgIndex(allianceIndex);
            addAlliance(alliance);

            allianceIndex++;
        }

        return true;
    }

    protected boolean createPlayers(List<List<Player>> players) {
        List<Player> playerList = new ArrayList<Player>();

        for (List<Player> plList : players)
            for (Player pl : plList)
                playerList.add(pl);

        while (playerList.size() > getMaxSize())
            playerList.remove(playerList.size() - 1);

        return handleQueueSolo(playerList);
    }

    protected void addPlayer(Player player) {
        if (!getPlayers().contains(player))
            getPlayers().add(player);
    }

    protected void addGroup(PlayerGroup group) {
        if (!getGroups().contains(group))
            getGroups().add(group);
    }

    protected void addAlliance(PlayerAlliance alliance) {
        if (!getAlliances().contains(alliance))
            getAlliances().add(alliance);
    }

    protected void freezeNoEnd(Player player) {
        player.getEffectController().setAbnormal(EffectId.PETRIFICATION.getEffectId());
        player.getEffectController().updatePlayerEffectIcons();
        player.getEffectController().broadCastEffects();
    }

    protected void freezePlayer(final Player player, int duration) {
        player.getEffectController().setAbnormal(EffectId.PETRIFICATION.getEffectId());
        player.getEffectController().updatePlayerEffectIcons();
        player.getEffectController().broadCastEffects();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                player.getEffectController().unsetAbnormal(EffectId.PETRIFICATION.getEffectId());
                player.getEffectController().updatePlayerEffectIcons();
                player.getEffectController().broadCastEffects();
            }
        }, duration);
    }

    protected void healPlayer(Player player) {
        this.healPlayer(player, true);
    }

    protected void healPlayer(Player player, boolean resetDp) {
        player.getLifeStats().increaseHp(TYPE.HP, player.getLifeStats().getMaxHp() + 1);
        player.getLifeStats().increaseMp(TYPE.MP, player.getLifeStats().getMaxMp() + 1);

        if (resetDp)
            player.getCommonData().setDp(0);
    }

    protected void performTeleport(Player player, float x, float y, float z) {
        this.performTeleport(player, x, y, z, TELEPORT_DEFAULT_DELAY);
    }

    protected void performTeleport(Player player, float x, float y, float z, int delay) {
        player.getController().cancelCurrentSkill(true);

        LadderService.getInstance().registerPreviousLocation(player);

        TeleportService.teleportTo(player, getMapId(), getInstanceId(), x, y, z, delay);

        player.setTeleportAnimation(TeleportAnimation.NONE);
    }

    protected void returnToPreviousLocation(Player player) {
        player.setBattleground(null);

        if (player.getLifeStats().isAlreadyDead())
            player.getReviveController().fullRevive();

        healPlayer(player, false);
        endTimer(player);

        LadderService.getInstance().returnToPreviousLocation(player);
    }

    protected void announce(Player player, String sender, String msg) {
        this.scheduleAnnouncement(player, sender, msg, 0);
    }

    protected void scheduleAnnouncement(final Player player, final String sender, final String msg,
        int delay) {
        if (delay > 0) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    PacketSendUtility.sendSys2Message(player, sender, msg);
                }
            }, delay);
        }
        else {
            PacketSendUtility.sendSys2Message(player, sender, msg);
        }
    }

    protected void scheduleAnnouncement(Player player, String msg, int delay) {
        this.scheduleAnnouncement(player, "BG", msg, delay);
    }

    protected void specAnnounce(String msg) {
        this.specAnnounce(msg, "BG");
    }

    protected void specAnnounce(String msg, String sender) {
        for (Iterator<Player> it = getSpectators().iterator(); it.hasNext();)
            PacketSendUtility.sendSys2Message(it.next(), sender, msg);
    }

    protected void specAnnounce(final String msg, int delay) {
        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                for (Iterator<Player> it = getSpectators().iterator(); it.hasNext();)
                    PacketSendUtility.sendSys2Message(it.next(), "BG", msg);
            }
        }, delay);
    }

    protected void announceAll(String msg) {
        this.announceAll("BG", msg);
    }

    protected void announceAll(String sender, String msg) {
        this.announceAll(sender, msg, 0);
    }

    protected void announceAll(final String sender, final String msg, int delay) {
        if (delay > 0) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    getInstance().doOnAllPlayers(new Executor<Player>() {
                        @Override
                        public boolean run(Player pl) {
                            PacketSendUtility.sendSys2Message(pl, sender, msg);
                            return true;
                        }
                    });
                }
            }, delay);
        }
        else {
            getInstance().doOnAllPlayers(new Executor<Player>() {
                @Override
                public boolean run(Player pl) {
                    PacketSendUtility.sendSys2Message(pl, sender, msg);
                    return true;
                }
            });
        }
    }

    protected void message(Player pl, String msg) {
        this.message(pl, msg, 0);
    }

    protected void message(final Player pl, final String msg, int delay) {
        if (delay > 0) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    PacketSendUtility.sendMessage(pl, msg);
                }
            }, delay);
        }
        else {
            PacketSendUtility.sendMessage(pl, msg);
        }
    }

    protected void scheduleCountdown(Player player, int length, int startTime) {
        for (int i = length; i > 0; i--) {
            scheduleAnnouncement(player, "The match starts in " + i + " seconds!", startTime - i
                * 1000);
        }
    }

    protected void scheduleGroupDisband(final PlayerGroup group, int delay) {
        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                while (group.size() > 0)
                    GroupService.getInstance().removePlayerFromGroup(
                        (Player) group.getMembers().toArray()[0], true);
            }
        }, delay);
    }

    protected void scheduleAllianceDisband(final PlayerAlliance alliance, int delay) {
        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                while (alliance.size() > 0)
                    AllianceService.getInstance().removeMemberFromAlliance(
                        alliance,
                        ((PlayerAllianceMember) alliance.getMembers().toArray()[0]).getPlayer()
                            .getObjectId(), PlayerAllianceEvent.LEAVE);
            }
        }, delay);
    }

    protected void preparePlayer(final Player pl, int time) {
        this.preparePlayer(pl, time, true);
    }

    protected void preparePlayer(final Player pl, int time, boolean announce) {
        this.preparePlayer(pl, time, announce, true, false);
    }

    protected void preparePlayer(final Player pl, int time, boolean announce, boolean reset,
        boolean reconnect) {
        DuelService.getInstance().loseDuel(pl);
        pl.setKillStreak(0);
        pl.setLastAction();
        pl.getFlyController().endFly();

        if (pl.getLifeStats().isAlreadyDead())
            pl.getReviveController().fullRevive();

        healPlayer(pl);

        InstanceService.registerPlayerWithInstance(getInstance(), pl);

        pl.getEffectController().removeAbnormalEffectsByTargetSlot(SkillTargetSlot.DEBUFF);
        pl.getEffectController().removeEffectByDispelCat(DispelCategoryType.ALL,
            SkillTargetSlot.DEBUFF, 100, 1);

        if (time > 0) {
            freezePlayer(pl, time);
            scheduleCountdown(pl, 5, time);
        }

        if (announce) {
            pl.getEffectController().removeRobotEffects();

            if (reset) {
                pl.resetSkillCoolDowns();
                pl.resetItemCoolDowns();
            }

            pl.setTotalKills(0);
            pl.setActivityCounter(0);

            if (time > 0) {
                scheduleAnnouncement(pl, "You have joined a " + getName() + " battleground!", 0);
                scheduleAnnouncement(pl, "Description: " + getDescription(), 15000);
                scheduleAnnouncement(pl, "The match has begun!!!", time);
            }

            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    freezeNoEnd(pl);
                    pl.getEffectController().removeAllNonItemEffects();
                    healPlayer(pl);
                }
            }, 3000);

            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    createTimer(pl, getSecondsLeft());
                }
            }, time - 5000);
        }
        else {
            if (time >= 3000) {
                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        healPlayer(pl);
                    }
                }, 3000);
            }

            if (!reconnect)
                scheduleAnnouncement(pl, "The round has begun!!!", time);

            createTimer(pl, getSecondsLeft());
        }
    }

    protected void preparePlayerInstance(final Player pl, int time) {
        DuelService.getInstance().loseDuel(pl);
        pl.setKillStreak(0);
        pl.setLastAction();
        pl.getFlyController().endFly();

        if (pl.getLifeStats().isAlreadyDead())
            pl.getReviveController().fullRevive();

        healPlayer(pl);

        InstanceService.registerPlayerWithInstance(getInstance(), pl);

        pl.getEffectController().removeAbnormalEffectsByTargetSlot(SkillTargetSlot.DEBUFF);
        pl.getEffectController().removeEffectByDispelCat(DispelCategoryType.ALL,
            SkillTargetSlot.DEBUFF, 100, 1);

        pl.getEffectController().removeRobotEffects();
        pl.resetSkillCoolDowns();
        pl.resetItemCoolDowns();
        pl.setTotalKills(0);
        pl.setActivityCounter(0);

        if (time > 0) {
            freezePlayer(pl, time);

            scheduleAnnouncement(pl, "You have entered the " + getName(), 5000);

            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    freezeNoEnd(pl);
                    pl.getEffectController().removeAllNonItemEffects();
                    healPlayer(pl);
                }
            }, 3000);
        }

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                createTimer(pl, getSecondsLeft());
            }
        }, time - 5000);
    }

    protected void resetPlayerKnownlist(final Player player, int delay) {
        if (delay > 0) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    player.clearKnownlist();
                    PacketSendUtility.sendPacket(player, new SM_PLAYER_INFO(player, false));
                    PacketSendUtility.sendPacket(player, new SM_MOTION(player));
                    player.getEffectController().updatePlayerEffectIcons();
                    player.getKnownList().forceUpdate();
                }
            }, delay);
        }
        else {
            player.clearKnownlist();
            PacketSendUtility.sendPacket(player, new SM_PLAYER_INFO(player, false));
            PacketSendUtility.sendPacket(player, new SM_MOTION(player));
            player.getEffectController().updatePlayerEffectIcons();
            player.getKnownList().forceUpdate();
        }
    }

    public int getSecondsLeft() {
        return (getMatchLength() - Math
            .round((float) (System.currentTimeMillis() - getStartStamp()) / 1000));
    }

    protected void createTimer(Player player, int seconds) {
        PacketSendUtility.sendPacket(player, new SM_QUEST_ACCEPTED(0, seconds));
    }

    protected void endTimer(Player player) {
        PacketSendUtility.sendPacket(player, new SM_QUEST_ACCEPTED(0, 0));
    }

    protected void startBattleground(int delay, final Runnable leaveCheck) {
        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                started = true;
                leaveCheck.run();
            }
        }, delay);
    }

    protected void playerWinMatch(Player player, int ratingChange) {
        if (is1v1() || is2v2() || isTournament())
            return;

        int premadeCounter = Math.max(LadderService.getInstance().getPremadeCounter(player), 1);

        getLadderDAO().addWin(player);
        getLadderDAO().addRating(
            player,
            (int) Math.round(ratingChange
                / (getLadderDAO().getRating(player) * Math.exp(premadeCounter * 0.3d) * 0.0007d)));
    }

    protected void playerLoseMatch(Player player, int ratingChange) {
        if (is1v1() || is2v2() || isTournament())
            return;

        getLadderDAO().addLoss(player);
        getLadderDAO().addRating(
            player,
            (int) Math.round(ratingChange * Math.exp(getLadderDAO().getRating(player) * 0.002d)
                * 0.13d));
    }

    protected void performLadderUpdate(Collection<Player> winner, Collection<Player> loser) {
        int avgWinnerRating = 0;
        int avgLoserRating = 0;

        for (Player pl : winner) {
            getLadderDAO().addWin(pl);
            avgWinnerRating += getLadderDAO().getRating(pl);
        }
        for (Player pl : winner) {
            getLadderDAO().addLoss(pl);
            avgLoserRating += getLadderDAO().getRating(pl);
        }

        if (winner.size() > 0)
            avgWinnerRating = avgWinnerRating / winner.size();
        if (loser.size() > 0)
            avgLoserRating = avgLoserRating / loser.size();

        int ratingChange = calcRatingChange(avgWinnerRating, avgLoserRating);

        for (Player pl : winner) {
            getLadderDAO().addRating(pl, +ratingChange);
        }
        for (Player pl : loser) {
            getLadderDAO().addRating(pl, -ratingChange);
        }
    }

    protected int calcRatingChange(int ratingA, int ratingB) {
        return (int) Math.round(K_VALUE
            * (1 / (1 + Math.pow(10, ((float) ratingB - (float) ratingA) / 400))));
    }

    protected void removePlayerFromTeam(Player player) {
        if (player.isInGroup())
            GroupService.getInstance().removePlayerFromGroup(player, true);
        if (player.isInAlliance())
            AllianceService.getInstance().removeMemberFromAlliance(player.getPlayerAlliance(),
                player.getObjectId(), PlayerAllianceEvent.LEAVE);
    }

    protected void startBackgroundTask() {
        setBackgroundTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                backgroundCounter++;

                zCheck();

                if ((backgroundCounter % 5) == 0) {
                    backgroundCounter = 0;
                    handleAfkCheck();
                    speedUpSpectators();
                }
            }
        }, 30 * 1000, 1 * 1000));

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (getBackgroundTask() != null)
                    getBackgroundTask().cancel(true);
                if (getExtraTask() != null)
                    getExtraTask().cancel(true);
            }
        }, 10 * getMatchLength() * 1000);
    }

    protected void zCheck() {
        if (getGroups().size() > 0) {
            for (PlayerGroup group : getGroups()) {
                for (Player pl : group.getMembers()) {
                    if (!pl.isOnline() || pl.getLifeStats().isAlreadyDead()
                        || pl.getController().hasActiveTask(TaskId.TELEPORT))
                        continue;

                    if (pl.getMoveController().getOriginZ() < map.getKillZ()
                        || pl.getMoveController().getOriginZ() > map.getHighestZ())
                        pl.getLifeStats().reduceHp(pl.getLifeStats().getMaxHp() + 1,
                            pl.getAggroList().getMostPlayerDamage());
                }
            }
        }

        if (getAlliances().size() > 0) {
            for (PlayerAlliance alliance : getAlliances()) {
                for (PlayerAllianceMember pla : alliance.getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null || pl.getLifeStats().isAlreadyDead()
                        || pl.getController().hasActiveTask(TaskId.TELEPORT))
                        continue;

                    if (pl.getMoveController().getOriginZ() < map.getKillZ()
                        || pl.getMoveController().getOriginZ() > map.getHighestZ())
                        pl.getLifeStats().reduceHp(pl.getLifeStats().getMaxHp() + 1,
                            pl.getAggroList().getMostPlayerDamage());
                }
            }
        }

        if (getPlayers().size() > 0) {
            for (Player pl : getPlayers()) {
                if (!pl.isOnline() || pl.getLifeStats().isAlreadyDead()
                    || pl.getController().hasActiveTask(TaskId.TELEPORT))
                    continue;

                if (pl.getMoveController().getOriginZ() < map.getKillZ()
                    || pl.getMoveController().getOriginZ() > map.getHighestZ()) {
                    pl.getLifeStats().reduceHp(pl.getLifeStats().getMaxHp() + 1,
                        pl.getAggroList().getMostPlayerDamage());
                }
            }
        }
    }

    protected void handleAfkCheck() {
        if (!afkKick() || is1v1() || is2v2() || isTournament())
            return;

        long currentTime = System.currentTimeMillis();
        long afkTime = 60 * 1000;

        if (getPlayers().size() > 0) {
            List<Player> players = new ArrayList<Player>();

            players.addAll(getPlayers());

            for (Iterator<Player> it = players.iterator(); it.hasNext();) {
                Player pl = it.next();
                if (pl.isSpawned() && (pl.getLastAction() + afkTime) < currentTime
                    && !pl.getLifeStats().isAlreadyDead()) {
                    onLeave(pl, false, true);
                }
            }
        }
        if (getGroups().size() > 0) {
            List<PlayerGroup> groups = new ArrayList<PlayerGroup>();

            groups.addAll(getGroups());

            for (Iterator<PlayerGroup> it = groups.iterator(); it.hasNext();) {
                PlayerGroup group = it.next();
                for (Player pl : group.getMembers()) {
                    if (pl.isSpawned() && (pl.getLastAction() + afkTime) < currentTime
                        && !pl.getLifeStats().isAlreadyDead()) {
                        onLeave(pl, false, true);
                    }
                }
            }
        }
        if (getAlliances().size() > 0) {
            List<PlayerAlliance> alliances = new ArrayList<PlayerAlliance>();

            alliances.addAll(getAlliances());

            for (Iterator<PlayerAlliance> it = alliances.iterator(); it.hasNext();) {
                PlayerAlliance alliance = it.next();
                for (PlayerAllianceMember pla : alliance.getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    if (pl.isSpawned() && (pl.getLastAction() + afkTime) < currentTime
                        && !pl.getLifeStats().isAlreadyDead()) {
                        onLeave(pl, false, true);
                    }
                }
            }
        }
    }

    protected void speedUpSpectators() {
        for (Player pl : getSpectators()) {
            pl.getGameStats().setStat(StatEnum.SPEED, 14000);
            pl.getGameStats().setStat(StatEnum.SPEED, 0, true);
            pl.getGameStats().setStat(StatEnum.FLY_SPEED, 16000);
            pl.getGameStats().setStat(StatEnum.FLY_SPEED, 0, true);
            pl.getGameStats().updateVisualSpeed();
        }
    }

    public void killBattleground() {
        String msg = "The battleground has been terminated! You will now be returned.";

        for (Player pl : getPlayers())
            PacketSendUtility.sendMessage(pl, msg);

        for (PlayerGroup group : getGroups())
            for (Player pl : group.getMembers())
                PacketSendUtility.sendMessage(pl, msg);

        for (PlayerAlliance alliance : getAlliances()) {
            for (PlayerAllianceMember pla : alliance.getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null)
                    continue;

                PacketSendUtility.sendMessage(pl, msg);
            }
        }

        this.onEndFirstDefault();
        this.onEndDefault();
    }

    protected void onLeaveDefault(Player player, boolean isLogout, boolean isAfk) {
        if (player.getBattleground() == null)
            return;

        if (player.isSpectating()) {
            onSpectatorLeave(player, false);
            return;
        }

        if (player.isInAlliance()) {
            getLeavers().put(player.getObjectId(), player.getPlayerAlliance());

            for (PlayerAllianceMember pla : player.getPlayerAlliance().getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null || pl == player)
                    continue;

                scheduleAnnouncement(pl, "An ally has disconnected.", 1000);
            }
        }
        else if (player.isInGroup()) {
            getLeavers().put(player.getObjectId(), player.getPlayerGroup());

            for (Player pl : player.getPlayerGroup().getMembers()) {
                if (pl == player)
                    continue;

                scheduleAnnouncement(pl, "An ally has disconnected.", 1000);
            }
        }
        else
            getLeavers().put(player.getObjectId(), player);

        getLeaversDead().put(player.getObjectId(), player.getLifeStats().isAlreadyDead());

        removePlayerFromTeam(player);

        if (!player.isTemporary()) {
            if (isLogout) {
                returnToPreviousLocation(player);
            }
            else if (!isAfk) {
                if (!this.done && !player.getController().isInShutdownProgress() && !is1v1()
                    && !is2v2())
                    scheduleAnnouncement(
                        player,
                        "You have been penalized for leaving the battleground! Type .reconnect to rejoin.",
                        10000);
            }
            else {
                returnToPreviousLocation(player);
                scheduleAnnouncement(
                    player,
                    "You have been penalized for being AFK. It counts as a leave! Type .reconnect to rejoin.",
                    10000);
            }
        }

        List<Player> players = getPlayers();
        synchronized (players) {
            players.remove(player);
        }

        player.setBattleground(null);

        if (!this.done && !player.getController().isInShutdownProgress() && !is1v1() && !isPvE()
            && !is2v2() && !player.isTemporary() && getSecondsLeft() > 0) {
            getLadderDAO().addLeave(player);

            if (LadderService.getInstance().getCachedRating(player) > 1500)
                getLadderDAO().addRating(player, -K_VALUE * 2);
            else
                getLadderDAO().addRating(player, -K_VALUE);

            DAOManager.getDAO(MightDAO.class).addMight(player, -K_VALUE);
        }

        endTimer(player);
    }

    protected void onDieDefault(Player player, Creature lastAttacker) {
        Summon summon = player.getSummon();
        if (summon != null)
            summon.getController().release(UnsummonType.UNSPECIFIED);

        PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player, EmotionType.DIE, 0,
            lastAttacker == null ? 0 : lastAttacker.getObjectId()), true);

        PacketSendUtility.sendPacket(player, SM_SYSTEM_MESSAGE.DIE);
        player.getMoveController().stop();
        player.setState(CreatureState.DEAD);
        player.getObserveController().notifyDeath(player);

        player.getEffectController().removeAbnormalEffectsByTargetSlot(SkillTargetSlot.DEBUFF);
        player.getEffectController().removeEffectByDispelCat(DispelCategoryType.ALL,
            SkillTargetSlot.DEBUFF, 100, 1);

        if (lastAttacker instanceof Player && lastAttacker.getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;

            killer.setTotalKills(killer.getTotalKills() + 1);
            killer.getAbyssRank().setAllKill();

            PacketSendUtility.sendPacket(killer, new SM_ABYSS_RANK(killer.getAbyssRank()));

            if (killer.getPlayerGroup() != null)
                killer.getPlayerGroup().setKillCount(killer.getPlayerGroup().getKillCount() + 1);
            else if (killer.getPlayerAlliance() != null)
                killer.getPlayerAlliance().setKillCount(
                    killer.getPlayerAlliance().getKillCount() + 1);
        }
    }

    protected void onEndFirstDefault() {
        try {
            if (getExpireTask() != null)
                getExpireTask().cancel(true);

            if (getBackgroundTask() != null)
                getBackgroundTask().cancel(true);

            if (getExtraTask() != null)
                getExtraTask().cancel(true);

            if (getPlayers().size() > 0) {
                for (Player pl : getPlayers()) {
                    if (!pl.getLifeStats().isAlreadyDead())
                        healPlayer(pl, false);

                    pl.getEffectController().removeAbnormalEffectsByTargetSlot(
                        SkillTargetSlot.DEBUFF);
                    pl.getEffectController().removeEffectByDispelCat(DispelCategoryType.ALL,
                        SkillTargetSlot.DEBUFF, 100, 1);
                }
            }
            if (getGroups().size() > 0) {
                for (PlayerGroup group : getGroups()) {
                    for (Player pl : group.getMembers()) {
                        if (!pl.getLifeStats().isAlreadyDead())
                            healPlayer(pl, false);

                        pl.getEffectController().removeAbnormalEffectsByTargetSlot(
                            SkillTargetSlot.DEBUFF);
                        pl.getEffectController().removeEffectByDispelCat(DispelCategoryType.ALL,
                            SkillTargetSlot.DEBUFF, 100, 1);
                    }
                }
            }
            if (getAlliances().size() > 0) {
                for (PlayerAlliance alliance : getAlliances()) {
                    for (PlayerAllianceMember pla : alliance.getMembers()) {
                        Player pl = pla.getPlayer();
                        if (pl == null)
                            continue;

                        if (!pl.getLifeStats().isAlreadyDead())
                            healPlayer(pl, false);

                        pl.getEffectController().removeAbnormalEffectsByTargetSlot(
                            SkillTargetSlot.DEBUFF);
                        pl.getEffectController().removeEffectByDispelCat(DispelCategoryType.ALL,
                            SkillTargetSlot.DEBUFF, 100, 1);
                    }
                }
            }
        }
        catch (Exception e) {
            log.error("Failed to finish onEndFirstDefault", e);
        }
    }

    protected void onEndDefault() {
        LadderService.getInstance().onBgEnd(this);

        if (!isPvE() && !is1v1() && !is2v2() && databaseId != 0) {
            Collection<Player> participants = getAllParticipants();

            for (Player pl : participants) {
                if (getInstance().findPlayer(pl.getObjectId()) != null && pl.isSpawned())
                    continue;

                DAOManager.getDAO(BgLogDAO.class).updateLeaver(databaseId, pl.getObjectId(), true);
            }

            for (Integer playerId : getLeavers().keySet()) {
                if (getInstance().findPlayer(playerId) != null)
                    continue;

                DAOManager.getDAO(BgLogDAO.class).updateLeaver(databaseId, playerId, true);
            }
        }

        List<Integer> counters = new ArrayList<Integer>();

        if (getPlayers().size() > 0) {
            for (Player pl : getPlayers()) {
                freezePlayer(pl, 7500);

                counters.add(pl.getActivityCounter());
            }

            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    for (Player pl : getPlayers()) {
                        returnToPreviousLocation(pl);
                    }
                }
            }, 5000);
        }

        if (getGroups().size() > 0) {
            for (PlayerGroup group : getGroups()) {
                for (Player pl : group.getMembers()) {
                    freezePlayer(pl, 7500);

                    counters.add(pl.getActivityCounter());
                }
            }

            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    for (PlayerGroup group : getGroups()) {
                        Race race = group.getGroupLeader().getCommonData().getRace();

                        for (Player pl : group.getMembers()) {
                            returnToPreviousLocation(pl);

                            if (race != null && pl.getCommonData().getRace() != race)
                                race = null;
                        }

                        if (!isTournament() && (shouldDisband() || race == null))
                            scheduleGroupDisband(group, 2000);
                    }
                }
            }, 5000);
        }

        if (getAlliances().size() > 0) {
            for (PlayerAlliance alliance : getAlliances()) {
                for (PlayerAllianceMember pla : alliance.getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    freezePlayer(pl, 7500);

                    counters.add(pl.getActivityCounter());
                }
            }

            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    for (PlayerAlliance alliance : getAlliances()) {
                        Race race = alliance.getCaptain() != null ? alliance.getCaptain()
                            .getCommonData().getRace() : null;

                        for (PlayerAllianceMember pla : alliance.getMembers()) {
                            Player pl = pla.getPlayer();
                            if (pl == null)
                                continue;

                            returnToPreviousLocation(pl);

                            if (race != null && pl.getCommonData().getRace() != race)
                                race = null;
                        }

                        if (!isTournament() && (shouldDisband() || race == null))
                            scheduleAllianceDisband(alliance, 2000);
                    }
                }
            }, 5000);
        }

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                List<Player> spectators = getSpectators();
                synchronized (spectators) {
                    for (Iterator<Player> it = spectators.iterator(); it.hasNext();) {
                        Player pl = it.next();
                        onSpectatorLeave(pl, true);
                        it.remove();
                    }
                }
            }
        }, 5000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                for (Iterator<Player> it = getInstance().getPlayers().iterator(); it.hasNext();) {
                    Player pl = it.next();
                    returnToPreviousLocation(pl);
                }
            }
        }, 15000);

        /*
         * StringBuilder sb = new StringBuilder(); String name = this.getName() + (is1v1() ? " (1v1)" : "");
         * sb.append("[DEBUG] " + name + " statistics: "); int averageCounter = 0; for (Integer counter : counters)
         * averageCounter += counter; averageCounter = averageCounter / counters.size(); sb.append("Average: " +
         * averageCounter + ", Values: " + StringUtils.join(counters, ", ")); log.info(sb.toString());
         */

        this.done = true;
    }

    protected void rewardPlayer(Player player, int might, boolean win) {
        if (isEligibleForRewards(player)) {
            PvpService.getInstance().addMight(player, might);
            GloryService.getInstance().rewardBattleground(player, win);
        }
    }

    protected boolean isEligibleForRewards(Player player) {
        if (player.getActivityCounter() >= 2)
            return true;

        String bg = this.getName() + (is1v1() ? " [1v1]" : "") + (is2v2() ? " [2v2]" : "");

        log.info("[DEBUG] Player " + player.getName() + " has activity below 2 in " + bg
            + " battleground!");

        return false;
    }

    public void onSpectatorJoin(Player spectator) {
        if (!spectator.isSpectating())
            LadderService.getInstance().registerPreviousLocation(spectator);

        spectator.setBattleground(this);
        spectator.setSpectating(true);
        getSpectators().add(spectator);

        // InstanceService.registerPlayerWithInstance(getInstance(), spectator);
        /*
         * previousLocations.put(spectator.getObjectId(), spectator.getPosition().clone());
         * wasInFfa.put(spectator.getObjectId(), ArenaService.getInstance().isInArena(spectator));
         */

        SpawnPosition pos = getSpawnPositions().get(Rnd.get(getSpawnPositions().size()));
        TeleportService.teleportTo(spectator, getMapId(), getInstanceId(), pos.getX(), pos.getY(),
            pos.getZ(), TELEPORT_DEFAULT_DELAY);

        spectator.getEffectController().setAbnormal(EffectId.INVISIBLE_RELATED.getEffectId());
        spectator.setVisualState(CreatureVisualState.HIDE3);
        spectator.setInvul(true);
        spectator.setSeeState(CreatureSeeState.SEARCH2);
        PacketSendUtility.broadcastPacket(spectator, new SM_PLAYER_STATE(spectator), true);

        createTimer(spectator, getSecondsLeft());
        scheduleAnnouncement(spectator, "You have joined a " + getName()
            + (isPvE() ? "" : " battleground") + " [" + this.getBgId() + "] as spectator!", 0);
    }

    public void onSpectatorSoftLeave(Player spectator) {
        endTimer(spectator);
        StatUpdater.getInstance().startTask(spectator);
        GroupService.getInstance().stopObserving(spectator);

        List<Player> spectators = getSpectators();
        synchronized (spectators) {
            spectators.remove(spectator);
        }
    }

    public void onSpectatorLeave(final Player spectator, boolean isIterating) {
        endTimer(spectator);
        returnToPreviousLocation(spectator);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                spectator.getEffectController().unsetAbnormal(
                    EffectId.INVISIBLE_RELATED.getEffectId());
                spectator.unsetVisualState(CreatureVisualState.HIDE3);
                spectator.setInvul(false);
                spectator.unsetSeeState(CreatureSeeState.SEARCH2);
                spectator.setSpectating(false);
                StatUpdater.getInstance().startTask(spectator);
                PacketSendUtility.broadcastPacket(spectator, new SM_PLAYER_STATE(spectator), true);

                GroupService.getInstance().stopObserving(spectator);
            }
        }, TELEPORT_DEFAULT_DELAY + 500);

        if (!isIterating) {
            List<Player> spectators = getSpectators();
            synchronized (spectators) {
                spectators.remove(spectator);
            }
        }
    }

    public void reconnectPlayer(Player player) {
        if (player.getBattleground() != null)
            return;

        AionObject obj = null;
        synchronized (getLeavers()) {
            obj = getLeavers().remove(player.getObjectId());
        }

        String msg = player.getName() + " has reconnected to the Battleground!";

        boolean success = false;
        SpawnPosition pos = null;

        if (obj instanceof Player) {
            success = true;
            addPlayer(player);

            player.setBgIndex(((Player) obj).getBgIndex());

            pos = getSpawnPositions().get(player.getBgIndex());

            for (Player pl : getPlayers())
                scheduleAnnouncement(pl, msg, 0);
        }
        else if (obj instanceof PlayerAlliance) {
            success = true;
            PlayerAlliance alliance = (PlayerAlliance) obj;
            AllianceService.getInstance().addMemberToAlliance(alliance, player);

            pos = getSpawnPositions().get(alliance.getBgIndex());

            for (PlayerAlliance ally : getAlliances()) {
                for (PlayerAllianceMember pla : ally.getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    scheduleAnnouncement(pl, msg, 0);
                }
            }
        }
        else if (obj instanceof PlayerGroup) {
            success = true;
            PlayerGroup group = (PlayerGroup) obj;
            group.addPlayerToGroup(player);

            pos = getSpawnPositions().get(group.getBgIndex());

            for (PlayerGroup grp : getGroups())
                for (Player pl : grp.getMembers())
                    scheduleAnnouncement(pl, msg, 0);
        }

        if (success) {
            player.setBattleground(this);

            preparePlayer(player, 0, false, false, true);

            LadderService.getInstance().clearPreviousLocation(player);

            if (!player.isSpawned()) {
                World.getInstance().setPosition(player, this.getMapId(), this.getInstanceId(),
                    pos.getX(), pos.getY(), pos.getZ(), player.getHeading());
            }
            else {
                performTeleport(player, pos.getX(), pos.getY(), pos.getZ(), 0);
            }

            if (getLeaversDead().containsKey(player.getObjectId())
                && getLeaversDead().get(player.getObjectId()))
                player.getController().die();

            if (!is1v1() && !isPvE() && !player.isTemporary() && !is2v2()) {
                getLadderDAO().setLeaves(player, getLadderDAO().getLeaves(player) - 1);

                if (getLadderDAO().getRating(player) > (1500 - K_VALUE * 2))
                    getLadderDAO().addRating(player, K_VALUE * 2);
                else
                    getLadderDAO().addRating(player, K_VALUE);

                DAOManager.getDAO(MightDAO.class).addMight(player, K_VALUE);
            }

            onReconnect(player);
        }
    }

    protected static LadderDAO getLadderDAO() {
        return DAOManager.getDAO(LadderDAO.class);
    }

    protected WorldMapInstance createInstance() {
        if (maps == null || maps.size() == 0)
            return null;

        // Pull random map from list
        this.map = maps.get(Rnd.get(maps.size()));
        this.mapId = map.getMapId();

        WorldMapInstance instance = InstanceService.getNextBgInstance(getMapId());
        if (instance != null)
            setInstanceId(instance.getInstanceId());

        setInstance(instance);
        setStartStamp(System.currentTimeMillis());
        setAverageParticipantRating(computeAverageParticipantRating());
        logStatistics();

        return instance;
    }

    protected WorldMapInstance createNewInstance() {
        if (maps == null || maps.size() == 0 || map == null)
            return null;

        // Pull a random NEW map
        List<BattlegroundMap> newMaps = new ArrayList<BattlegroundMap>(maps.size());
        newMaps.addAll(maps);

        newMaps.remove(this.map);

        this.map = newMaps.get(Rnd.get(newMaps.size()));
        this.mapId = map.getMapId();

        WorldMapInstance instance = InstanceService.getNextBgInstance(getMapId());
        if (instance != null)
            setInstanceId(instance.getInstanceId());

        setInstance(instance);

        return instance;
    }

    protected void openStaticDoors() {
        if (getMap().getStaticDoors() == null || getMap().getStaticDoors().size() == 0)
            return;

        for (Integer doorId : getMap().getStaticDoors()) {
            StaticDoor door = null;

            for (AionObject ao : getInstance().getObjects()) {
                if (!(ao instanceof StaticDoor))
                    continue;

                if (((StaticDoor) ao).getObjectTemplate().getId() == doorId) {
                    door = (StaticDoor) ao;
                    break;
                }
            }

            if (door == null) {
                getInstance().openDoor(doorId, 0);
            }
            else {
                door.setOpen(true);
            }
        }
    }

    protected void killSummonedCreatures() {
        getInstance().doOnAllNpcs(new Executor<Npc>() {
            @Override
            public boolean run(Npc npc) {
                if (npc.getMaster() != npc)
                    npc.getLifeStats().reduceHp(npc.getLifeStats().getMaxHp() + 1, npc);

                return true;
            }
        });
    }

    protected void deleteNpcs() {
        if (getInstance() == null)
            return;

        getInstance().doOnAllNpcs(new Executor<Npc>() {
            @Override
            public boolean run(Npc npc) {
                npc.getController().onDelete();
                return true;
            }
        });
    }

    protected int getRemainingPlayers() {
        return getPlayers().size();
    }

    protected int getRemainingGroups() {
        return getRemainingGroups(1);
    }

    protected int getRemainingGroups(int limit) {
        int leaveCount = 0;
        for (PlayerGroup group : getGroups())
            if (group.size() <= limit)
                leaveCount++;

        return getGroups().size() - leaveCount;
    }

    protected int getRemainingAlliances() {
        return getRemainingAlliances(1);
    }

    protected int getRemainingAlliances(int limit) {
        int leaveCount = 0;
        for (PlayerAlliance alliance : getAlliances())
            if (alliance.size() <= limit)
                leaveCount++;

        return getAlliances().size() - leaveCount;
    }

    protected void propagateDone() {
        if (is1v1() || isPvE() || is2v2())
            return;

        for (Player pl : getPlayers()) {
            GloryService.getInstance().onBattlegroundDone(pl);
        }

        for (PlayerGroup group : getGroups()) {
            for (Player pl : group.getMembers()) {
                GloryService.getInstance().onBattlegroundDone(pl);
            }
        }

        for (PlayerAlliance alliance : getAlliances()) {
            for (PlayerAllianceMember pla : alliance.getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null)
                    continue;

                GloryService.getInstance().onBattlegroundDone(pl);
            }
        }
    }

    protected void propagateWin(Player player) {
        if (!is1v1() && !is2v2())
            GloryService.getInstance().onBattlegroundWin(player);
        else if (is1v1())
            GloryService.getInstance().on1v1Win(player);
    }

    protected void propagateWin(PlayerGroup group) {
        if (!is1v1() && !is2v2()) {
            for (Player player : group.getMembers()) {
                GloryService.getInstance().onBattlegroundWin(player);
            }
        }
    }

    protected void propagateWin(PlayerAlliance alliance) {
        for (PlayerAllianceMember pla : alliance.getMembers()) {
            Player player = pla.getPlayer();
            if (player == null)
                continue;

            GloryService.getInstance().onBattlegroundWin(player);
        }
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public int getMinSize() {
        return minSize;
    }

    public void setMinSize(int minSize) {
        this.minSize = minSize;
    }

    public int getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(int maxSize) {
        this.maxSize = maxSize;
    }

    public int getTeamCount() {
        return teamCount;
    }

    public int getMapId() {
        return mapId;
    }

    public void setMatchLength(int matchLength) {
        this.matchLength = matchLength;
    }

    public int getMatchLength() {
        return matchLength;
    }

    public BattlegroundMap getMap() {
        return map;
    }

    protected void setInstanceId(int instanceId) {
        this.instanceId = instanceId;
    }

    public int getInstanceId() {
        return instanceId;
    }

    public void setBgId(Integer bgId) {
        this.bgId = bgId;
    }

    public Integer getBgId() {
        return bgId;
    }

    protected void setStartStamp(long startStamp) {
        this.startStamp = startStamp;
    }

    public long getStartStamp() {
        return startStamp;
    }

    public void setIsTournament(boolean isTournament) {
        this.isTournament = isTournament;
    }

    public boolean isTournament() {
        return isTournament;
    }

    public void setIsEvent(boolean isEvent) {
        this.isEvent = isEvent;
    }

    public boolean isEvent() {
        return isEvent;
    }

    public boolean is1v1() {
        return is1v1;
    }

    public void setIs1v1(boolean is1v1) {
        this.is1v1 = is1v1;
    }

    public boolean isPvE() {
        return isPvE;
    }

    public void setIsPvE(boolean isPvE) {
        this.isPvE = isPvE;
    }

    public boolean is2v2() {
        return is2v2;
    }

    public void setIs2v2(boolean is2v2) {
        this.is2v2 = is2v2;
    }

    public boolean shouldDisband() {
        return shouldDisband;
    }

    public void setShouldDisband(boolean shouldDisband) {
        this.shouldDisband = shouldDisband;
    }

    public boolean isMinMaxOnly() {
        return minMaxOnly;
    }

    public boolean isTeamBased() {
        return teamBased;
    }

    public void setTeamBased(boolean teamBased) {
        this.teamBased = teamBased;
    }

    public boolean isAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(boolean isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public boolean afkKick() {
        return afkKick;
    }

    public void setAfkKick(boolean afkKick) {
        this.afkKick = afkKick;
    }

    public boolean canResurrect() {
        return canResurrect;
    }

    public void setCanResurrect(boolean canResurrect) {
        this.canResurrect = canResurrect;
    }

    public boolean isDone() {
        return done;
    }

    public boolean isStarted() {
        return started;
    }

    public boolean isSpecial() {
        return special;
    }

    public WorldMapInstance getInstance() {
        return instance;
    }

    public void setInstance(WorldMapInstance instance) {
        this.instance = instance;
    }

    protected List<Player> getPlayers() {
        return _players;
    }

    protected List<PlayerGroup> getGroups() {
        return _groups;
    }

    protected List<PlayerAlliance> getAlliances() {
        return _alliances;
    }

    protected List<Player> getSpectators() {
        return _spectators;
    }

    public Map<Integer, AionObject> getLeavers() {
        return _leavers;
    }

    public Map<Integer, Boolean> getLeaversDead() {
        return _leaversDead;
    }

    public boolean hasPlayers() {
        return (getPlayers().size() > 0 || getGroups().size() > 0 || getAlliances().size() > 0);
    }

    public Collection<Player> getPlayerList() {
        return Collections.unmodifiableCollection(getPlayers());
    }

    protected void setExpireTask(ScheduledFuture<?> expireTask) {
        this.expireTask = expireTask;
    }

    protected ScheduledFuture<?> getExpireTask() {
        return expireTask;
    }

    public ScheduledFuture<?> getBackgroundTask() {
        return backgroundTask;
    }

    public void setBackgroundTask(ScheduledFuture<?> backgroundTask) {
        this.backgroundTask = backgroundTask;
    }

    public ScheduledFuture<?> getExtraTask() {
        return extraTask;
    }

    public void setExtraTask(ScheduledFuture<?> extraTask) {
        this.extraTask = extraTask;
    }

    public static Map<String, Class<?>> getAliases() {
        return aliases;
    }

    protected VisibleObject spawn(int npcId, float x, float y, float z) {
        return spawn(npcId, x, y, z, (byte) 0, true);
    }

    protected VisibleObject spawn(int npcId, float x, float y, float z, int heading) {
        return spawn(npcId, x, y, z, 0, true);
    }

    protected VisibleObject spawn(int npcId, float x, float y, float z, boolean geo) {
        return spawn(npcId, x, y, z, 0, geo);
    }

    protected VisibleObject spawn(int npcId, double x, double y, double z, int heading, boolean geo) {
        return spawn(npcId, (float) x, (float) y, (float) z, heading, geo);
    }

    protected VisibleObject spawn(int npcId, float x, float y, float z, int heading, boolean geo) {
        SpawnTemplate st = SpawnEngine.getInstance().addNewSpawn(this.getMapId(),
            this.getInstanceId(), npcId, x, y,
            geo ? GeoEngine2.getInstance().getZ(this.getMapId(), x, y, z) : z, (byte) heading, 0,
            0, true);
        VisibleObject vo = SpawnEngine.getInstance().spawnObject(st, this.getInstanceId(), false,
            NPC_SPAWN_DELAY);

        if (vo instanceof Npc) {
            ((Npc) vo).setBattleground(this);
        }

        return vo;
    }

    protected void spawnProtection(Player pl) {
        this.spawnProtection(pl, 5000);
    }

    protected void spawnProtection(Player pl, int duration) {
        SkillTemplate template = DataManager.SKILL_DATA.getSkillTemplate(18474);
        Effect effect = new Effect(pl, pl, template, 1, duration);
        pl.getEffectController().addEffect(effect);
        effect.addAllEffectToSucess();
        effect.startEffect(true);
    }

    protected void logStatistics() {
        if (this.isPvE() || this.is1v1() || this.is2v2())
            return;

        BgLogDAO dao = DAOManager.getDAO(BgLogDAO.class);

        databaseId = dao.insertBg(this.getName());

        if (databaseId == 0) {
            log.error("Error occured logging BG statistics: bgId == 0");
            return;
        }

        for (Player pl : getAllParticipants()) {
            if (!dao.addPlayerToBg(databaseId, pl)) {
                log.error("Error occured logging BG statistics for player " + pl.getName()
                    + " (bgId " + databaseId + ")");
                return;
            }

            int premadeCounter = LadderService.getInstance().getPremadeCounter(pl);
            if (premadeCounter > 1)
                dao.addPremadeToBg(databaseId, LadderService.getInstance().getPremadeId(pl),
                    premadeCounter);
        }
    }

    protected void logWinner(int winnerId) {
        if (this.isPvE() || this.is1v1() || this.is2v2())
            return;
        else if (databaseId == 0)
            return;

        BgLogDAO dao = DAOManager.getDAO(BgLogDAO.class);

        dao.updateWinner(databaseId, winnerId);
    }

    protected Collection<Player> getAllParticipants() {
        List<Player> players = new ArrayList<Player>();

        players.addAll(this.getPlayers());

        for (PlayerGroup group : this.getGroups())
            players.addAll(group.getMembers());

        for (PlayerAlliance alliance : this.getAlliances()) {
            for (PlayerAllianceMember pla : alliance.getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null)
                    continue;

                players.add(pl);
            }
        }

        return players;
    }

    protected int computeAverageParticipantRating() {
        int rating = 0;
        int count = 0;

        for (Player pl : getPlayers()) {
            rating += LadderService.getInstance().getCachedRating(pl);
            count++;
        }

        for (PlayerGroup group : getGroups()) {
            rating += group.getAverageRating();
            count++;
        }

        for (PlayerAlliance alliance : getAlliances()) {
            rating += alliance.getAverageRating();
            count++;
        }

        rating = rating / Math.max(count, 1);

        return rating;
    }

    protected int computeAveragePlayerRating(Collection<Player> objects) {
        int rating = 0;
        int count = 0;

        for (Player pl : objects) {
            rating += LadderService.getInstance().getCachedRating(pl);
            count++;
        }

        rating = rating / Math.max(count, 1);

        return rating;
    }

    protected int computeAverageGroupRating(Collection<PlayerGroup> objects) {
        int rating = 0;
        int count = 0;

        for (PlayerGroup group : objects) {
            rating += group.getAverageRating();
            count++;
        }

        rating = rating / Math.max(count, 1);

        return rating;
    }

    protected int computeAverageAllianceRating(Collection<PlayerAlliance> objects) {
        int rating = 0;
        int count = 0;

        for (PlayerAlliance alliance : objects) {
            rating += alliance.getAverageRating();
            count++;
        }

        rating = rating / Math.max(count, 1);

        return rating;
    }

    protected boolean premadeOpponentCheck(int bgIndex) {
        if (!getGroups().isEmpty()) {
            for (PlayerGroup group : getGroups()) {
                if (group.getBgIndex() == bgIndex)
                    continue;

                for (Player pl : group.getMembers()) {
                    if (LadderService.getInstance().getPremadeId(pl) == 0)
                        return false;
                }
            }

            return true;
        }
        else if (!getAlliances().isEmpty()) {
            for (PlayerAlliance alliance : getAlliances()) {
                if (alliance.getBgIndex() == bgIndex)
                    continue;

                for (PlayerAllianceMember pla : alliance.getMembers()) {
                    Player pl = pla.getPlayer();
                    if (pl == null)
                        continue;

                    if (LadderService.getInstance().getPremadeId(pl) == 0)
                        return false;
                }
            }

            return true;
        }

        return false;
    }

    protected float getMaxRatingDiff() {
        return MAX_RATING_DIFF * (isTeamBased() ? 1f : 2f)
            * (1f + Math.max(getTeamCount() - 2, 0) / 2f);
    }

    public int getAverageParticipantRating() {
        return averageParticipantRating;
    }

    public void setAverageParticipantRating(int averageParticipantRating) {
        this.averageParticipantRating = averageParticipantRating;
    }

    public static class BattlegroundMap {
        private int mapId = 0;
        private List<SpawnPosition> spawnPoints = null;
        private List<Integer> staticDoors = null;
        private float killZ = 0f;
        private float highestZ = 4000f;
        private boolean restrictFlight = true;

        public BattlegroundMap(int mapId) {
            this.setMapId(mapId);
        }

        public void addSpawn(SpawnPosition pos) {
            if (spawnPoints == null)
                spawnPoints = new ArrayList<SpawnPosition>();

            spawnPoints.add(pos);
        }

        public void addSpawn(float x, float y, float z) {
            addSpawn(new SpawnPosition(x, y, z));
        }

        public void addStaticDoor(Integer doorId) {
            if (staticDoors == null)
                staticDoors = new ArrayList<Integer>();

            staticDoors.add(doorId);
        }

        /**
         * @param mapId
         *            the mapId to set
         */
        public void setMapId(int mapId) {
            this.mapId = mapId;
        }

        /**
         * @return the mapId
         */
        public int getMapId() {
            return mapId;
        }

        /**
         * @return the spawnPoints
         */
        public List<SpawnPosition> getSpawnPoints() {
            return spawnPoints;
        }

        /**
         * @param spawnPoints
         *            the spawnPoints to set
         */
        public void setSpawnPoints(List<SpawnPosition> spawnPoints) {
            this.spawnPoints = spawnPoints;
        }

        /**
         * @return the staticDoors
         */
        public List<Integer> getStaticDoors() {
            return staticDoors;
        }

        /**
         * @param staticDoors
         *            the staticDoors to set
         */
        public void setStaticDoors(List<Integer> staticDoors) {
            this.staticDoors = staticDoors;
        }

        /**
         * @param killZ
         *            the killZ to set
         */
        public void setKillZ(float killZ) {
            this.killZ = killZ;
        }

        /**
         * @return the killZ
         */
        public float getKillZ() {
            return killZ;
        }

        /**
         * @return the restrictFlight
         */
        public boolean isRestrictFlight() {
            return restrictFlight;
        }

        /**
         * @param restrictFlight
         *            the restrictFlight to set
         */
        public void setRestrictFlight(boolean restrictFlight) {
            this.restrictFlight = restrictFlight;
        }

        /**
         * @return the highestZ
         */
        public float getHighestZ() {
            return highestZ;
        }

        /**
         * @param highestZ
         *            the highestZ to set
         */
        public void setHighestZ(float highestZ) {
            this.highestZ = highestZ;
        }
    }

    public enum BattlegroundMode {
        NORMAL,
        EVENT,
        TOURNAMENT,
        ONEVSONE,
    }
}