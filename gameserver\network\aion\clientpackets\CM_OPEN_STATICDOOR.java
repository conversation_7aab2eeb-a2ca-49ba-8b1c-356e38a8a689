/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.network.aion.clientpackets;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.StaticDoor;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.StaticDoorTemplate.DoorState;
import gameserver.network.aion.AionClientPacket;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.services.InstanceService;
import gameserver.utils.PacketSendUtility;
import gameserver.world.WorldMapInstance;

/**
 * <AUTHOR>
 */
public class CM_OPEN_STATICDOOR extends AionClientPacket {
    private int doorId;

    /**
     * @param opcode
     */
    public CM_OPEN_STATICDOOR(int opcode) {
        super(opcode);
    }

    @Override
    protected void readImpl() {
        doorId = readD();
    }

    @Override
    protected void runImpl() {
        Player player = this.getConnection().getActivePlayer();

        if (player.getAccessLevel() >= AdminConfig.GM_LEVEL)
            PacketSendUtility.sendMessage(player, "Door id: " + doorId);

        if (player.getBattleground() != null || player.isSpectating())
            return;

        /*
         * WorldMapInstance instance = InstanceService.getRegisteredInstance(player.getWorldId(), player.getObjectId());
         * if (instance != null) instance.openDoor(doorId, player.getTarget() != null ? player.getTarget().getObjectId()
         * : 0); else PacketSendUtility.broadcastPacketAndReceive(player, new SM_EMOTION(doorId));
         */

        StaticDoor door = null;

        for (AionObject ao : player.getKnownList().getObjects()) {
            if (!(ao instanceof StaticDoor))
                continue;

            if (((StaticDoor) ao).getObjectTemplate().getId() == doorId) {
                door = (StaticDoor) ao;
                break;
            }
        }

        if (door == null) {
            if (player.isGM())
                PacketSendUtility.sendMessage(player, "[DBG] Fallback to old behaviour");

            WorldMapInstance instance = InstanceService.getRegisteredInstance(player.getWorldId(),
                player.getObjectId());
            if (instance != null)
                instance.openDoor(doorId, player.getTarget() != null ? player.getTarget()
                    .getObjectId() : 0);
            else
                PacketSendUtility.broadcastPacketAndReceive(player, new SM_EMOTION(doorId));
        }
        else {
            if (door.isOpen() && DoorState.isSet(DoorState.CLOSEABLE, door.getState()))
                door.setOpen(false);
            else
                door.setOpen(true);
        }
    }
}
