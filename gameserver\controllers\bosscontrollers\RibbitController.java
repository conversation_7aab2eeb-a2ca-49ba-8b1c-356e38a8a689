/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.PvpService;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class RibbitController extends BossController {
    private long nextMove = 0;

    public RibbitController() {
        super(219159, true);
    }

    protected void think() {
        if (nextMove == 0) {
            nextMove = System.currentTimeMillis() + Rnd.get(5000, 20000);
        }
        else if (System.currentTimeMillis() > nextMove) {
            nextMove = 0;

            randomWalk(8);
        }
    }

    @Override
    public void doReward() {
        Player killer = getOwner().getAggroList().getMostPlayerDamage();
        if (killer != null) {
            PvpService.getInstance().addMight(killer, 1);
        }
    }
}
