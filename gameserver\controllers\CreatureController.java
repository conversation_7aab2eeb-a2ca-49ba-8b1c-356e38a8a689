/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.ai.events.Event;
import gameserver.controllers.attack.AttackResult;
import gameserver.controllers.attack.AttackStatus;
import gameserver.controllers.attack.AttackUtil;
import gameserver.controllers.movement.MovementType;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.Race;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.NpcWithCreator;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.gameobjects.stats.CreatureGameStats;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.templates.stats.NpcRank;
import gameserver.network.aion.serverpackets.SM_ATTACK;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_LOOKATOBJECT;
import gameserver.network.aion.serverpackets.SM_MOVE;
import gameserver.network.aion.serverpackets.SM_SKILL_CANCEL;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.restrictions.RestrictionsManager;
import gameserver.services.AnimationCheckingService;
import gameserver.services.DuelService;
import gameserver.skillengine.SkillEngine;
import gameserver.skillengine.action.DamageType;
import gameserver.skillengine.model.ActivationAttribute;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.HealType;
import gameserver.skillengine.model.Skill;
import gameserver.skillengine.model.SkillSubType;
import gameserver.skillengine.model.SkillType;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;
import gameserver.world.World;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;

import org.apache.log4j.Logger;

import com.aionemu.commons.utils.Rnd;

/**
 * This class is for controlling Creatures [npc's, players etc]
 * 
 * <AUTHOR> ATracer(2009-09-29), Sarynth
 */
public abstract class CreatureController<T extends Creature> extends
    VisibleObjectController<Creature> {
    @SuppressWarnings("unused")
    private static final Logger log = Logger.getLogger(CreatureController.class);
    private ConcurrentHashMap<Integer, Future<?>> tasks = new ConcurrentHashMap<Integer, Future<?>>();

    private float healRate = 1.00f;
    private float healDeboostRate = 1.00f;
    private long lastAttackMilis = 0;

    private String lastChain = null;
    private boolean lastChainSuccess = false;
    private long lastChainTime = 0;
    private int selfChainCount = 0;
    private int selfChainSkillId = 0;

    private ScheduledFuture<?> attackTask = null;

    private final int TIMER_DURATION = 6000;

    /**
     * extend aura range
     */
    private float auraRangeRate = 1.00f;

    /**
     * Boostskillcastingtimeeffect -SUMMONTRAP, -SUMMON, -SUMMONHOMING, -HEAL, -ATTACK, -NONE //general, for all skills,
     * example: Boon of Quickness
     */
    private Map<SkillSubType, Integer> boostCastingRates = new HashMap<SkillSubType, Integer>();

    /**
     * {@inheritDoc}
     */
    @Override
    public void notSee(VisibleObject object, boolean isOutOfRange) {
        super.notSee(object, isOutOfRange);
        if (object == getOwner().getTarget()) {
            getOwner().setTarget(null);
            PacketSendUtility.broadcastPacket(getOwner(), new SM_LOOKATOBJECT(getOwner()));
        }
    }

    /**
     * Perform tasks on Creature starting to move
     */
    public void onStartMove() {
        getOwner().getObserveController().notifyMoveObservers();
    }

    /**
     * Perform tasks on Creature move in progress
     */
    public void onMove() {
        getOwner().getObserveController().notifyMoveObservers();
    }

    /**
     * Perform tasks on Creature stop move
     */
    public void onStopMove() {
        getOwner().getObserveController().notifyMoveObservers();
    }

    /**
     * Perform tasks on Creature death
     */
    public void onDie(Creature lastAttacker) {
        this.cancelCurrentSkill(true);
        this.getOwner().getEffectController().removeAllEffects(false);
        this.getOwner().getMoveController().stop();
        this.getOwner().setState(CreatureState.DEAD);
        this.getOwner().getObserveController().notifyDeath(this.getOwner());
    }

    /**
     * Perform tasks on Creature respawn
     */
    @Override
    public void onRespawn() {
        getOwner().unsetState(CreatureState.DEAD);
        getOwner().getAggroList().clear();
        getOwner().getFollowers().clear();
    }

    /**
     * Perform tasks when Creature was attacked //TODO may be pass only Skill object - but need to add properties in it
     */
    public void onAttack(Creature creature, int skillId, TYPE type, int damage, int logId,
        AttackStatus status, boolean notifyAttackedObservers, boolean sendPacket) {
        // if (damage > getOwner().getLifeStats().getCurrentHp())
        // damage = getOwner().getLifeStats().getCurrentHp() + 1;

        Skill skill = getOwner().getCastingSkill();
        if (skill != null && damage > 0 && notifyAttackedObservers) {
            int cancelRate = skill.getSkillTemplate().getCancelRate() / 2;

            // default cancel rate 90 for magical skills of players
            if (cancelRate == 0 && skill.getSkillTemplate().getType() == SkillType.MAGICAL
                && getOwner() instanceof Player) {
                if (skill.getSkillTemplate().getActivationAttribute() == ActivationAttribute.TOGGLE)
                    cancelRate = 10;
                else
                    cancelRate = 90;
            }
            else if (getOwner() instanceof Npc) {
                Npc npc = (Npc) getOwner();
                NpcRank rank = npc.getObjectTemplate().getRank();
                if (rank == NpcRank.ELITE || rank == NpcRank.HERO || rank == NpcRank.LEGENDARY)
                    cancelRate = 0;
            }

            if (cancelRate > 0) {
                for (Effect effect : getOwner().getEffectController().getAbnormalEffects()) {
                    if (effect.isAvatar()) {
                        cancelRate = 0;
                        break;
                    }
                }

                if (cancelRate > 0) {
                    int conc = getOwner().getGameStats().getCurrentStat(StatEnum.CONCENTRATION) / 10;
                    float maxHp = getOwner().getGameStats().getCurrentStat(StatEnum.MAXHP);
                    float cancel = (cancelRate - conc) + (((float) damage) / maxHp * 20);

                    if (Rnd.get(100) < cancel) {
                        if (creature instanceof Player)
                            PacketSendUtility.sendPacket((Player) creature,
                                SM_SYSTEM_MESSAGE.STR_SKILL_TARGET_SKILL_CANCELED());

                        cancelCurrentSkill(true);
                    }
                }
            }
        }

        if (creature != null) {
            // transfer hate from SkillAreaNpc,Homing,Trap,Totem,Servant to master
            if (creature instanceof NpcWithCreator)
                creature = creature.getMaster();

            getOwner().getAggroList().addDamage(creature, damage);
        }

        getOwner().getLifeStats().reduceHp(damage, creature);
        sendOnAttackPacket(type, damage, skillId, logId);

        // set involved creatures to combat state
        if (creature != null && notifyAttackedObservers) {
            creature.setCombatState(7); // 8s taken from client's hide combat restriction
            getOwner().setCombatState(7);

            switch (status) {
                case DODGE:
                case CRITICAL_DODGE:
                case RESIST:
                case MAGICAL_CRITICAL_RESIST:
                    break;
                default:
                    if (damage > 0)
                        getOwner().getObserveController().notifyAttackedObservers(creature);
                    break;
            }
        }

        if (damage > 0) {
            if (getOwner() instanceof Player && ((Player) getOwner()).getEquipment() != null)
                ((Player) getOwner()).getEquipment().onDefend(skillId > 0);
            if (creature instanceof Player && ((Player) creature).getEquipment() != null)
                ((Player) creature).getEquipment().onAttack(skillId > 0);
        }

        final Creature _creature = creature;

        if (getOwner() instanceof Player) {
            final Player player = (Player) getOwner();

            if (player.isEnemy(creature)
                && !DuelService.getInstance().isDueling(player.getObjectId(),
                    creature.getObjectId())) {
                player.getKnownList().doOnAllNpcs(new Executor<Npc>() {
                    @Override
                    public boolean run(Npc npc) {
                        if (npc.getObjectTemplate() != null
                            && player.getCommonData().getRace() == npc.getObjectTemplate()
                                .getRace()
                            && MathUtil.isIn3dRange(getOwner(), npc, npc.getAggroRange() + 10)) {
                            npc.getAggroList().addHate(_creature, 10);
                        }
                        return true;
                    }
                });
            }
        }
        else if (getOwner() instanceof Npc) {
            final Npc npc = (Npc) getOwner();

            if (npc.isEnemy(creature)
                && npc.getObjectTemplate() != null
                && (npc.getObjectTemplate().getRace() == Race.ELYOS
                    || npc.getObjectTemplate().getRace() == Race.ASMODIANS || npc
                    .getObjectTemplate().getRace() == Race.DRAKAN)) {
                npc.getKnownList().doOnAllNpcs(new Executor<Npc>() {
                    @Override
                    public boolean run(Npc _npc) {
                        if (npc.getObjectTemplate().getRace() == _npc.getObjectTemplate().getRace()
                            && MathUtil.isIn3dRange(npc, _npc, _npc.getAggroRange() + 5)) {
                            _npc.getAggroList().addHate(_creature, 10);
                        }
                        return true;
                    }
                });
            }
        }
    }

    /**
     * Perform tasks when Creature was attacked
     */
    public void onAttack(Creature creature, int damage, AttackStatus status,
        boolean notifyAttackedObservers) {
        this.onAttack(creature, 0, TYPE.REGULAR, damage, 0, status, notifyAttackedObservers, false);
    }

    public void onAttack(Creature creature, int skillId, TYPE type, int damage,
        AttackStatus status, boolean notifyAttackedObservers) {
        this.onAttack(creature, skillId, type, damage, 0, status, notifyAttackedObservers, false);
    }

    /**
     * send onAttackPacket
     */
    protected void sendOnAttackPacket(TYPE type, int damage, int skillId, int logId) {
        PacketSendUtility.broadcastPacketAndReceive(getOwner(), new SM_ATTACK_STATUS(getOwner(),
            type, -damage, skillId, logId));
    }

    /**
     * @param hopType
     * @param value
     */
    public void onRestore(HealType hopType, int value) {
        switch (hopType) {
            case HP:
                getOwner().getLifeStats().increaseHp(TYPE.HP, value);
                break;
            case MP:
                getOwner().getLifeStats().increaseMp(TYPE.MP, value);
                break;
            case FP:
                getOwner().getLifeStats().increaseFp(TYPE.FP, value);
                break;
        }
    }

    /**
     * Perform reward operation
     */
    public void doReward() {

    }

    /**
     * This method should be overriden in more specific controllers
     */
    public void onDialogRequest(Player player) {

    }

    /**
     * 
     * @param target
     */
    public void attackTarget(Creature target) {
        double angle = MathUtil.calculateAngleFrom(getOwner(), target) / 3;

        World.getInstance().updateHeading(getOwner(), (byte) angle);

        this.attackTarget(target, 0, 1000, 1);
    }

    /**
     * @param target
     */
    public void attackTarget(final Creature target, int atknumber, int time, int attackType) {
        final Creature attacker = getOwner();

        /**
         * Check all prerequisites
         */
        // npc specific checks
        if (attacker instanceof Npc) {
            if (!attacker.isSpawned())
                return;

            if (target == null || target.getLifeStats().isAlreadyDead()) {
                ((Npc) attacker).getAi().handleEvent(Event.MOST_HATED_CHANGED);
                return;
            }
        }

        if (target == null || target.getLifeStats().isAlreadyDead())
            return;

        if (!attacker.isEnemy(target) || attacker.getLifeStats().isAlreadyDead()
            || !attacker.canAttack())
            return;

        long milis = System.currentTimeMillis();

        // player specific checks
        if (attacker instanceof Player) {
            Player player = (Player) attacker;

            // if (target != null && !player.canSee(target)) {
            // Logger.getLogger(getClass()).info(
            // "[AUDIT] Possible anti-hide hack: " + player.getName() + " trying to attack "
            // + target.getName() + " with seeState < hideState!");
            // }

            if (!RestrictionsManager.canAttack(player, target))
                return;

            // anti-lag stuff
            int attackSpeed = attacker.getGameStats().getCurrentStat(StatEnum.ATTACK_SPEED);

            if (milis - lastAttackMilis < attackSpeed - 30) {
                // PacketSendUtility.sendMessage(player, "Blocked attack ("
                // + (milis - lastAttackMilis) + " < " + attackSpeed + ")");
                return;
            }

            float range = player.getGameStats().getCurrentStat(StatEnum.ATTACK_RANGE) / 1000f;

            range += 1.25f;

            if (player.getMoveController().isMoving())
                range += 1.5f;
            if (target.getMoveController().isMoving())
                range += 1.5f;
            if (target.getEffectController().hasCantMoveAbnormal())
                range += 2f;

            // if (target instanceof Npc)
            range += target.getBoundRadius().getFront();

            // range check
            if (!MathUtil.isIn3dRange(player, target, range)) {
                AnimationCheckingService.outOfAttackRange(player, false);
                AnimationCheckingService.outOfRange(player, true, false);
                return;
            }

            AnimationCheckingService.outOfRange(player, false, false);
        }

        if (!GeoEngine2.getInstance().canSee(attacker, target)) {
            if (attacker instanceof Player)
                PacketSendUtility.sendPacket((Player) attacker,
                    SM_SYSTEM_MESSAGE.STR_ATTACK_OBSTACLE_EXIST);
            return;
        }

        // Don't set last attack timestamp before it's actually certain
        lastAttackMilis = milis;

        CreatureGameStats<?> gameStats = attacker.getGameStats();

        /**
         * Calculate and apply damage
         */
        List<AttackResult> attackResult = new ArrayList<AttackResult>();

        final DamageType dmgType = attacker.getAttackType().isMagical() ? DamageType.MAGICAL
            : DamageType.PHYSICAL;

        if (dmgType == DamageType.MAGICAL)
            attackResult = AttackUtil.calculateMagicalAttackResult(attacker, target);
        else
            attackResult = AttackUtil.calculatePhysicalAttackResult(attacker, target, time);

        int damage = 0;
        AttackStatus status = AttackStatus.NORMALHIT;
        for (AttackResult result : attackResult) {
            if (result.getAttackStatus() == AttackStatus.STUMBLE_OR_STAGGER)
                continue;

            damage += result.getDamage();
            status = result.getAttackStatus();
        }

        PacketSendUtility.broadcastPacketAndReceive(attacker, new SM_ATTACK(attacker, target,
            gameStats.getAttackCounter(), time, attackType, attackResult));

        // TODO synchronize attackcounter with packet atknumber
        gameStats.increaseAttackCounter();

        attacker.getObserveController().notifyAttackUseObservers(target);

        final int finalDamage = damage;
        final AttackStatus finalStatus = status;
        if (time == 0) {
            target.getController().onAttack(attacker, finalDamage, finalStatus, true);

            attacker.getObserveController().notifyAttackObservers(target);

            switch (finalStatus) {
                case DODGE:
                case CRITICAL_DODGE:
                case RESIST:
                case MAGICAL_CRITICAL_RESIST:
                    break;
                default:
                    target.getObserveController().notifyHittedObservers(getOwner(), dmgType);
                    break;
            }
        }
        else {
            attackTask = ThreadPoolManager.getInstance().schedule(new Runnable() {
                public void run() {
                    if (attacker.getLifeStats().isAlreadyDead())
                        return;

                    target.getController().onAttack(attacker, finalDamage, finalStatus, true);

                    attacker.getObserveController().notifyAttackObservers(target);

                    switch (finalStatus) {
                        case DODGE:
                        case CRITICAL_DODGE:
                        case RESIST:
                        case MAGICAL_CRITICAL_RESIST:
                            break;
                        default:
                            target.getObserveController()
                                .notifyHittedObservers(getOwner(), dmgType);
                            break;
                    }
                }
            }, time);
        }
    }

    /**
     * Stops movements
     */
    public void stopMoving() {
        Creature owner = getOwner();

        if (owner.isSpawned() && owner.getWorldId() != 0) {
            float ownerX = owner.getX();
            float ownerY = owner.getY();
            float ownerZ = GeoEngine2.getInstance().getZ(owner);
            byte ownerH = owner.getHeading();

            World.getInstance().updatePosition(owner, ownerX, ownerY, ownerZ, ownerH);
            PacketSendUtility.broadcastPacket(owner, new SM_MOVE(owner, ownerX, ownerY, ownerZ,
                ownerH, MovementType.MOVEMENT_STOP));
        }
    }

    /**
     * Handle Dialog_Select
     * 
     * @param dialogId
     * @param player
     * @param questId
     */
    public void onDialogSelect(int dialogId, Player player, int questId) {
        // TODO Auto-generated method stub
    }

    /**
     * @param taskId
     * @return
     */
    public Future<?> getTask(TaskId taskId) {
        return tasks.get(taskId.ordinal());
    }

    /**
     * @param taskId
     * @return
     */
    public boolean hasTask(TaskId taskId) {
        return tasks.containsKey(taskId.ordinal());
    }

    public boolean hasActiveTask(TaskId taskId) {
        return tasks.containsKey(taskId.ordinal()) && !tasks.get(taskId.ordinal()).isDone();
    }

    /**
     * @param taskId
     */
    public void cancelTask(TaskId taskId) {
        Future<?> task = tasks.remove(taskId.ordinal());
        if (task != null) {
            task.cancel(false);
        }
    }

    /**
     * If task already exist - it will be canceled
     * 
     * @param taskId
     * @param task
     */
    public void addTask(TaskId taskId, Future<?> task) {
        cancelTask(taskId);
        tasks.put(taskId.ordinal(), task);
    }

    /**
     * If task already exist - it will not be replaced
     * 
     * @param taskId
     * @param task
     */
    public void addNewTask(TaskId taskId, Future<?> task) {
        tasks.putIfAbsent(taskId.ordinal(), task);
    }

    /**
     * Cancel all tasks associated with this controller (when deleting object)
     */
    public void cancelAllTasks() {
        for (Future<?> task : tasks.values()) {
            if (task != null) {
                task.cancel(true);
            }
        }
        // FIXME: This can fill error logs with NPE if left null. Should never happen...
        tasks = new ConcurrentHashMap<Integer, Future<?>>();
    }

    @Override
    public void delete() {
        cancelAllTasks();
        cancelCurrentSkill(true);
        super.delete();
    }

    /**
     * Die by reducing HP to 0
     */
    public void die() {
        getOwner().getLifeStats().reduceHp(getOwner().getLifeStats().getCurrentHp() + 1, null);
    }

    /**
     * @param skillId
     */
    public void useSkill(int skillId) {
        Creature creature = getOwner();

        Skill skill = SkillEngine.getInstance()
            .getSkill(creature, skillId, 1, creature.getTarget());
        // Logger.getLogger(getClass()).debug(creature.getName() + "using skill #" + skillId + ":" + skill);
        if (skill != null) {
            skill.useSkill();
        }
    }

    /**
     * Notify hate value to all visible creatures
     * 
     * @param value
     */
    public void broadcastHate(final int value) {
        if (getOwner() == null || getOwner().getKnownList() == null)
            return;

        getOwner().getKnownList().doOnAllObjects(new Executor<AionObject>() {
            @Override
            public boolean run(AionObject visibleObject) {
                if (visibleObject instanceof Creature) {
                    ((Creature) visibleObject).getAggroList().notifyHate(getOwner(), value);
                }
                return true;
            }
        });
    }

    /**
     * Cancel current skill and remove cooldown
     */
    public void cancelCurrentSkill(boolean forced) {
        Creature creature = getOwner();
        Skill castingSkill = creature.getCastingSkill();

        if (castingSkill != null) {
            castingSkill.cancelCast();
            creature.removeSkillCoolDown(castingSkill.getSkillTemplate().getDelayId());
            this.setSelfChainCount(0);
            creature.setCasting(null);

            if (creature instanceof Player) {
                PacketSendUtility.broadcastPacket((Player) creature, new SM_SKILL_CANCEL(creature,
                    castingSkill.getSkillTemplate().getSkillId()), forced);
                PacketSendUtility.sendPacket((Player) creature,
                    SM_SYSTEM_MESSAGE.STR_SKILL_CANCELED());
            }
            else {
                PacketSendUtility.broadcastPacket(creature, new SM_SKILL_CANCEL(creature,
                    castingSkill.getSkillTemplate().getSkillId()));
            }
        }
    }

    /**
     * @param npcId
     * @param skillId TODO
     */
    public void createSummon(int npcId, int skillId, int skillLvl) {
        // TODO Auto-generated method stub

    }

    /**
     * BoostCastingTimeEffects rates
     * 
     */
    public void addBoostCastingRate(SkillSubType type, int value) {
        if (boostCastingRates.containsKey(type)) {
            int temp = boostCastingRates.get(type);
            boostCastingRates.put(type, temp + value);
        }
        else
            boostCastingRates.put(type, value);
    }

    public void removeBoostCastingRate(SkillSubType type, int value) {
        if (boostCastingRates.containsKey(type)) {
            int temp = boostCastingRates.get(type);
            boostCastingRates.put(type, temp - value);
        }
    }

    public int getBoostCastingRate(SkillSubType type) {
        if (boostCastingRates.containsKey(type))
            return boostCastingRates.get(type);
        else
            return 0;
    }

    // extend aura range
    public float getAuraRangeRate() {
        return auraRangeRate;
    }

    public void setAuraRangeRate(float auraRangeRate) {
        this.auraRangeRate = auraRangeRate;
    }

    public float getHealRate() {
        return healRate;
    }

    public void changeHealRate(float healRateChange) {
        this.healRate += healRateChange;
    }

    public float getHealDeboostRate() {
        return healDeboostRate;
    }

    public void changeHealDeboostRate(float healDeboostRateChange) {
        this.healDeboostRate += healDeboostRateChange;
    }

    public String getLastChain() {
        return lastChain;
    }

    public void setLastChain(String lastChain) {
        this.lastChain = lastChain;
    }

    public void setLastChainSuccess(boolean lastChainSuccess) {
        this.lastChainSuccess = lastChainSuccess;
    }

    public boolean getLastChainSuccess() {
        return lastChainSuccess;
    }

    public void setLastChainTime() {
        this.lastChainTime = System.currentTimeMillis() + TIMER_DURATION;
    }

    public boolean hasActiveChain() {
        return lastChainTime > System.currentTimeMillis();
    }

    public int getSelfChainCount() {
        return selfChainCount;
    }

    public void setSelfChainCount(int selfChainCount) {
        this.selfChainCount = selfChainCount;
    }

    public int getSelfChainSkillId() {
        return selfChainSkillId;
    }

    public void setSelfChainSkillId(int selfChainSkillId) {
        this.selfChainSkillId = selfChainSkillId;
    }

    public void cancelAttackTask() {
        if (attackTask != null && !attackTask.isDone() && !attackTask.isCancelled()) {
            attackTask.cancel(false);
            attackTask = null;
        }
    }
}
