/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.geoEngine2.scene;

import gameserver.geoEngine2.bounding.BoundingBox;
import gameserver.geoEngine2.bounding.BoundingVolume;
import gameserver.geoEngine2.collision.Collidable;
import gameserver.geoEngine2.collision.CollisionResults;
import gameserver.geoEngine2.collision.UnsupportedCollisionException;
import gameserver.geoEngine2.math.Matrix3f;
import gameserver.geoEngine2.math.Quaternion;
import gameserver.geoEngine2.math.Ray;
import gameserver.geoEngine2.math.Transform;
import gameserver.geoEngine2.math.Vector3f;

import java.util.BitSet;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 * 
 */
public class DoorGeometry extends Spatial {
    private BitSet instances = new BitSet();
    
    public DoorGeometry(BoundingBox box) {
        this.worldBound = box;
    }
    
    public void setDoorOpen(int instanceId, boolean open) {
        instances.set(instanceId, open);
    }

    @Override
    public int collideWith(Collidable other, CollisionResults results)
        throws UnsupportedCollisionException {
        if (instances.get(results.getInstanceId()))
            return 0;
        
        int hits = worldBound.collideWith(other, results);
        
        if (hits > 0 && other instanceof Ray) {
            for (int i = results.size() - hits; i < results.size(); i++) {
                if (results.getCollisionDirect(i).getDistance() > ((Ray) other).getLimit())
                    results.remove(i);
            }
        }
        
        if (hits > 0) {
            Logger.getLogger(getClass()).info("Door collision with " + other.toString());
        }
        
        return hits;
    }

    @Override
    public void updateModelBound() {
    }

    @Override
    public void setModelBound(BoundingVolume modelBound) {
        worldBound = modelBound;
    }

    @Override
    public int getVertexCount() {
        return 0;
    }

    @Override
    public int getTriangleCount() {
        return 0;
    }

    @Override
    public DoorGeometry clone() throws CloneNotSupportedException {
        return (DoorGeometry) super.clone();
    }

    @Override
    public Spatial deepClone() throws CloneNotSupportedException {
        DoorGeometry clone = clone();
        clone.worldBound = this.worldBound;
        return null;
    }

    @Override
    public void setTransform(Matrix3f rotation, Vector3f loc, float scale) {
        Quaternion quat = new Quaternion();
        quat.fromRotationMatrix(rotation);

        Vector3f sc = new Vector3f(scale, scale, scale);

        worldBound.transform(new Transform(loc, quat, sc));
    }
}
