/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects;

import gameserver.ai.AI;
import gameserver.ai.desires.impl.AggressionDesire;
import gameserver.ai.npcai.AggressiveAi;
import gameserver.ai.npcai.NpcAi;
import gameserver.ai.state.AIState;
import gameserver.configs.main.CustomConfig;
import gameserver.configs.main.NpcMovementConfig;
import gameserver.controllers.NpcController;
import gameserver.dataholders.DataManager;
import gameserver.model.ChatType;
import gameserver.model.EmotionType;
import gameserver.model.NpcType;
import gameserver.model.Race;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.gameobjects.stats.NpcGameStats;
import gameserver.model.gameobjects.stats.NpcLifeStats;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.pvpevents.Battleground;
import gameserver.model.templates.NpcTemplate;
import gameserver.model.templates.VisibleObjectTemplate;
import gameserver.model.templates.npcskill.NpcSkillList;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.model.templates.stats.NpcRank;
import gameserver.network.aion.serverpackets.SM_CUSTOM_SETTINGS;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_UPDATE_HEADING;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;
import gameserver.world.KnownList;
import gameserver.world.NpcKnownList;
import gameserver.world.World;
import gameserver.world.WorldPosition;

/**
 * This class is a base class for all in-game NPCs, what includes: monsters and npcs that player can talk to (aka
 * Citizens)
 * 
 * <AUTHOR>
 */
public class Npc extends Creature {

    private NpcSkillList npcSkillList;
    private boolean noHome = false;
    private Battleground battleground = null;
    private int bgIndex = -1;

    /**
     * Constructor creating instance of Npc.
     * 
     * @param spawn
     *            SpawnTemplate which is used to spawn this npc
     * @param objId
     *            unique objId
     */
    public Npc(int objId, NpcController controller, SpawnTemplate spawnTemplate,
        VisibleObjectTemplate objectTemplate) {
        super(objId, controller, spawnTemplate, objectTemplate, new WorldPosition());
        controller.setOwner(this);

        if (this.getObjectTemplate() != null && this.getTribe().equals("DUMMY"))
            this.setDummy(true);

        super.setGameStats(new NpcGameStats(this));
        super.setLifeStats(new NpcLifeStats(this));
    }

    @Override
    public NpcTemplate getObjectTemplate() {
        return (NpcTemplate) objectTemplate;
    }

    @Override
    public String getName() {
        return getObjectTemplate().getName();
    }

    public int getNpcId() {
        return getObjectTemplate().getTemplateId();
    }

    @Override
    public byte getLevel() {
        return getObjectTemplate().getLevel();
    }

    /**
     * @return the lifeStats
     */
    @Override
    public NpcLifeStats getLifeStats() {
        return (NpcLifeStats) super.getLifeStats();
    }

    /**
     * @return the gameStats
     */
    @Override
    public NpcGameStats getGameStats() {
        return (NpcGameStats) super.getGameStats();
    }

    @Override
    public NpcController getController() {
        return (NpcController) super.getController();
    }

    public boolean hasWalkRoutes() {
        return getSpawn().getWalkerId() > 0
            || (getSpawn().hasRandomWalk() && NpcMovementConfig.ACTIVE_NPC_MOVEMENT);
    }

    /**
     * @return
     */
    public boolean isAggressive() {
        String currentTribe = getObjectTemplate().getTribe();
        return DataManager.TRIBE_RELATIONS_DATA.hasAggressiveRelations(currentTribe) || isGuard()
            || isHostile();
    }

    public boolean isHostile() {
        String currentTribe = getObjectTemplate().getTribe();
        return DataManager.TRIBE_RELATIONS_DATA.hasHostileRelations(currentTribe);
    }

    @Override
    public boolean isAggressiveTo(Creature creature) {
        if (this.isGuard() && creature instanceof Npc)
            return !this.isFriendly((Npc) creature);
        return creature.isAggroFrom(this);
    }

    @Override
    public boolean isAggroFrom(Npc npc) {
        return !isDummy()
            && DataManager.TRIBE_RELATIONS_DATA.isAggressiveRelation(npc.getTribe(), getTribe());
    }

    @Override
    public boolean isHostileFrom(Npc npc) {
        return DataManager.TRIBE_RELATIONS_DATA.isHostileRelation(npc.getTribe(), getTribe());
    }

    @Override
    public boolean isSupportFrom(Npc npc) {
        return DataManager.TRIBE_RELATIONS_DATA.isSupportRelation(npc.getTribe(), getTribe());
    }

    public boolean isFriendly(Npc npc) {
        if (this.getObjectTemplate() == null || npc.getObjectTemplate() == null)
            return false;
        if (this.getObjectTemplate().getRace() == npc.getObjectTemplate().getRace())
            return true;
        else if (this.getTribe().equals(npc.getTribe()))
            return true;
        else if (this.isSupportFrom(npc))
            return true;
        else if (npc.getTribe() != null
            && (npc.getTribe().equalsIgnoreCase("USEALL")
                || npc.getTribe().equalsIgnoreCase("FIELD_OBJECT_ALL")
                || npc.getTribe().equalsIgnoreCase("TELEPORTOR_DA") || npc.getTribe()
                .equalsIgnoreCase("DUMMY")))
            return true;
        else if (npc.getObjectTemplate().getRace() == Race.BROWNIE)
            return true;
        else if (npc.getObjectTemplate().getNpcType() != null
            && (npc.getObjectTemplate().getNpcType() == NpcType.POSTBOX
                || npc.getObjectTemplate().getNpcType() == NpcType.PORTAL
                || npc.getObjectTemplate().getNpcType() == NpcType.USEITEM
                || npc.getObjectTemplate().getNpcType() == NpcType.RESURRECT
                || npc.getObjectTemplate().getNpcType() == NpcType.CHEST || npc.getObjectTemplate()
                .getNpcType() == NpcType.ARTIFACT))
            return true;

        if (getObjectTemplate().getRace() != null) {
            switch (getObjectTemplate().getRace()) {
                case PC_LIGHT_CASTLE_DOOR:
                case GCHIEF_LIGHT:
                case ELYOS:
                    if (npc.getObjectTemplate().getRace() == Race.ELYOS
                        || npc.getObjectTemplate().getRace() == Race.PC_LIGHT_CASTLE_DOOR
                        || npc.getObjectTemplate().getRace() == Race.GCHIEF_LIGHT)
                        return true;
                    break;
                case PC_DARK_CASTLE_DOOR:
                case GCHIEF_DARK:
                case ASMODIANS:
                    if (npc.getObjectTemplate().getRace() == Race.ASMODIANS
                        || npc.getObjectTemplate().getRace() == Race.PC_DARK_CASTLE_DOOR
                        || npc.getObjectTemplate().getRace() == Race.GCHIEF_DARK)
                        return true;
                    break;
            }
        }

        if (getBattleground() != null && npc.getBattleground() != null) {
            if (getBgIndex() == npc.getBgIndex())
                return true;
        }

        return false;
    }

    public boolean isFriendly(Player player) {
        if (player.getBattleground() != null) {
            if (this.getBgIndex() == -1)
                return false;

            if (player.isInGroup())
                return player.getPlayerGroup().getBgIndex() == this.getBgIndex();
            else if (player.isInAlliance())
                return player.getPlayerAlliance().getBgIndex() == this.getBgIndex();

            return player.getBgIndex() == this.getBgIndex();
        }

        if (this.getObjectTemplate().getRace() == player.getCommonData().getRace())
            return true;
        else if (this.getTribe().equals(player.getTribe()))
            return true;
        else if ((this.getObjectTemplate().getRace() == Race.GCHIEF_LIGHT && player.getCommonData()
            .getRace() == Race.ELYOS)
            || (this.getObjectTemplate().getRace() == Race.GCHIEF_DARK && player.getCommonData()
                .getRace() == Race.ASMODIANS))
            return true;

        return false;
    }

    /**
     * @return
     */
    public boolean isGuard() {
        String currentTribe = getObjectTemplate().getTribe();
        return DataManager.TRIBE_RELATIONS_DATA.isGuardDark(currentTribe)
            || DataManager.TRIBE_RELATIONS_DATA.isGuardLight(currentTribe);
    }

    @Override
    public String getTribe() {
        return this.getObjectTemplate() != null ? this.getObjectTemplate().getTribe() : "";
    }

    public int getAggroRange() {
        return getObjectTemplate().getAggroRange() + 1;
    }

    @Override
    public void initializeAi() {
        if (isAggressive() && !CustomConfig.DISABLE_MOB_AGGRO)
            this.ai = new AggressiveAi();
        else
            this.ai = new NpcAi();
        ai.setOwner(this);
    }

    /**
     * Check whether npc located at initial spawn location
     * 
     * @return true or false
     */
    public boolean isAtSpawnLocation() {
        return isNoHome()
            || MathUtil.getDistance(getSpawn().getX(), getSpawn().getY(), getSpawn().getZ(),
                getX(), getY(), getZ()) < .5F;
    }

    /**
     * @return the npcSkillList
     */
    public NpcSkillList getNpcSkillList() {
        return npcSkillList;
    }

    /**
     * @param npcSkillList
     *            the npcSkillList to set
     */
    public void setNpcSkillList(NpcSkillList npcSkillList) {
        this.npcSkillList = npcSkillList;
    }

    @Override
    protected boolean isEnemyNpc(Npc visibleObject) {
        if (visibleObject.getMaster() instanceof Player)
            return isEnemyPlayer((Player) visibleObject.getMaster());

        if (this.getObjectTemplate() == null || visibleObject.getObjectTemplate() == null)
            return true;

        return this.getObjectTemplate().getRace() != visibleObject.getObjectTemplate().getRace()
            && (!((DataManager.TRIBE_RELATIONS_DATA.isSupportRelation(getTribe(),
                visibleObject.getTribe())) || (DataManager.TRIBE_RELATIONS_DATA.isFriendlyRelation(
                getTribe(), visibleObject.getTribe())))) && !isFriendly(visibleObject);
    }

    @Override
    protected boolean isEnemyPlayer(Player visibleObject) {
        if (getMaster() instanceof Player)
            return getMaster().isEnemy(visibleObject);
        if (getObjectTemplate() == null || getObjectTemplate().getRace() == null)
            return true;
        else if (visibleObject.isOutlaw() || visibleObject.isLawless() || visibleObject.isBandit())
            return true;
        else if (visibleObject.getBattleground() != null) {
            if (this.getBgIndex() == -1)
                return true;

            if (visibleObject.isInGroup())
                return visibleObject.getPlayerGroup().getBgIndex() != this.getBgIndex();
            else if (visibleObject.isInAlliance())
                return visibleObject.getPlayerAlliance().getBgIndex() != this.getBgIndex();

            return visibleObject.getBgIndex() != this.getBgIndex();
        }

        switch (getObjectTemplate().getRace()) {
            case PC_LIGHT_CASTLE_DOOR:
            case GCHIEF_LIGHT:
            case ELYOS:
                if (visibleObject.getCommonData().getRace() == Race.ELYOS)
                    return false;
                break;

            case PC_DARK_CASTLE_DOOR:
            case GCHIEF_DARK:
            case ASMODIANS:
                if (visibleObject.getCommonData().getRace() == Race.ASMODIANS)
                    return false;
                break;
        }

        return (!((DataManager.TRIBE_RELATIONS_DATA.isSupportRelation(getTribe(),
            visibleObject.getTribe())) || (DataManager.TRIBE_RELATIONS_DATA.isFriendlyRelation(
            getTribe(), visibleObject.getTribe()))));
    }

    @Override
    protected boolean isEnemySummon(Summon visibleObject) {
        Player player = visibleObject.getMaster();
        if (player != null)
            return isEnemyPlayer(player);

        return true;
    }

    @Override
    protected boolean canSeeNpc(Npc npc) {
        return true; // TODO
    }

    @Override
    protected boolean canSeePlayer(Player player) {
        if (player.isSpectating())
            return false;

        if (!player.isInState(CreatureState.ACTIVE))
            return false;

        if (player.getVisualState() == 1 && getObjectTemplate().getRank() == NpcRank.NORMAL)
            return false;

        if (player.getVisualState() == 2
            && (getObjectTemplate().getRank() == NpcRank.ELITE || getObjectTemplate().getRank() == NpcRank.NORMAL))
            return false;

        if (player.getVisualState() >= 3)
            return false;

        return true;
    }

    @Override
    public void setKnownlist(KnownList knownList) {
        if (!(knownList instanceof NpcKnownList)) {
            throw new RuntimeException("Invalid knownlist " + knownList.getClass().getSimpleName()
                + " for " + getClass().getSimpleName());
        }
        super.setKnownlist(knownList);
    }

    @Override
    public NpcKnownList getKnownList() {
        return (NpcKnownList) super.getKnownList();
    }

    public boolean isNoHome() {
        return noHome;
    }

    public void setNoHome(boolean noHome) {
        this.noHome = noHome;
    }

    public void checkAggro() {
        if (ai == null)
            return;

        final Npc owner = this;

        owner.getKnownList().doOnAllObjects(new Executor<AionObject>() {
            @Override
            public boolean run(AionObject object) {
                if (object instanceof Creature) {
                    Creature creature = (Creature) object;

                    if (creature.getLifeStats() != null && !creature.getLifeStats().isAlreadyDead()
                        && owner.isAggressiveTo(creature) && owner.canSee(creature)
                        && MathUtil.isInRange(owner, creature, owner.getAggroRange())) {
                        if (ai == null)
                            return false;

                        ai.addDesire(new AggressionDesire(owner, AIState.ACTIVE.getPriority()));
                        return false;
                    }
                }

                return true;
            }
        }, true);
    }

    public int getBgIndex() {
        return bgIndex;
    }

    public void setBgIndex(int bgIndex) {
        this.bgIndex = bgIndex;
    }

    public void broadcastNpcType() {
        PacketSendUtility.broadcastPacket(this, new SM_CUSTOM_SETTINGS(this));
    }

    public Battleground getBattleground() {
        return battleground;
    }

    public void setBattleground(Battleground battleground) {
        this.battleground = battleground;
    }

    public Npc walkTo(float x, float y, float z) {
        return walkTo(x, y, z, 0);
    }

    public Npc walkTo(final float x, final float y, final float z, int delay) {
        final Npc owner = this;

        if (delay > 0) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    if (!owner.isSpawned())
                        return;

                    boolean wasWalking = owner.getMoveController().isWalking();

                    owner.getMoveController().setSpeed(
                        owner.getGameStats().getCurrentStat(StatEnum.WALK) * 0.001f);
                    owner.getMoveController().setWalking(true);

                    if (owner.isInState(CreatureState.WEAPON_EQUIPPED)) {
                        owner.setState(CreatureState.NPC_IDLE);
                        owner.unsetState(CreatureState.WEAPON_EQUIPPED);

                        PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                            EmotionType.NEUTRALMODE));
                    }

                    if (!wasWalking)
                        PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                            EmotionType.WALK));

                    owner.getMoveController().setDistance(0);
                    owner.getMoveController().setNewDirection(x, y, z);
                    owner.getMoveController().schedule();
                }
            }, delay);
        }
        else {
            boolean wasWalking = owner.getMoveController().isWalking();

            owner.getMoveController().setSpeed(
                owner.getGameStats().getCurrentStat(StatEnum.WALK) * 0.001f);
            owner.getMoveController().setWalking(true);

            if (owner.isInState(CreatureState.WEAPON_EQUIPPED)) {
                owner.setState(CreatureState.NPC_IDLE);
                owner.unsetState(CreatureState.WEAPON_EQUIPPED);

                PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                    EmotionType.NEUTRALMODE));
            }

            if (!wasWalking)
                PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner, EmotionType.WALK));

            owner.getMoveController().setDistance(0);
            owner.getMoveController().setNewDirection(x, y, z);
            owner.getMoveController().schedule();
        }

        return this;
    }

    public Npc runTo(float x, float y, float z) {
        return runTo(x, y, z, 0);
    }

    public Npc runTo(final float x, final float y, final float z, int delay) {
        final Npc owner = this;

        if (delay > 0) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    if (owner.getAi().desireQueueSize() > 0 || !owner.isSpawned())
                        return;

                    owner.getMoveController().setSpeed(
                        owner.getGameStats().getCurrentStat(StatEnum.SPEED) * 0.001f);
                    owner.getMoveController().setWalking(false);

                    if (!owner.isInState(CreatureState.WEAPON_EQUIPPED)) {
                        owner.unsetState(CreatureState.NPC_IDLE);
                        owner.setState(CreatureState.WEAPON_EQUIPPED);
                        PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                            EmotionType.ATTACKMODE));
                    }

                    owner.getMoveController().setDistance(0);
                    owner.getMoveController().setNewDirection(x, y, z);
                    owner.getMoveController().schedule();
                }
            }, delay);
        }
        else {
            if (owner.getAi().desireQueueSize() > 0)
                return this;

            owner.getMoveController().setSpeed(
                owner.getGameStats().getCurrentStat(StatEnum.SPEED) * 0.001f);
            owner.getMoveController().setWalking(false);

            if (!owner.isInState(CreatureState.WEAPON_EQUIPPED)) {
                owner.unsetState(CreatureState.NPC_IDLE);
                owner.setState(CreatureState.WEAPON_EQUIPPED);
                PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                    EmotionType.ATTACKMODE));
            }

            owner.getMoveController().setDistance(0);
            owner.getMoveController().setNewDirection(x, y, z);
            owner.getMoveController().schedule();
        }

        return this;
    }

    public Npc shout(String message) {
        return shout(message, 0);
    }

    public Npc shout(final String message, int delay) {
        final Npc owner = this;

        if (delay > 0) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    if (!owner.isSpawned())
                        return;

                    PacketSendUtility.broadcastPacket(owner, new SM_MESSAGE(owner.getObjectId(),
                        owner.getName(), message, ChatType.ALLIANCE));
                }
            }, delay);
        }
        else {
            PacketSendUtility.broadcastPacket(owner,
                new SM_MESSAGE(owner.getObjectId(), owner.getName(), message, ChatType.ALLIANCE));
        }

        return this;
    }

    public Npc delete(int delay) {
        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                getController().onDelete();
            }
        }, delay);

        return this;
    }

    public Npc scheduleHeadingReset() {
        final Npc owner = this;

        if (!owner.getController().hasActiveTask(TaskId.HEADING_RESET)) {
            owner.getController().addTask(TaskId.HEADING_RESET,
                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        World.getInstance().updateHeading(owner, owner.getSpawn().getHeading());
                        PacketSendUtility.broadcastPacket(owner, new SM_UPDATE_HEADING(owner));
                    }
                }, 2000));
        }

        return this;
    }
}
