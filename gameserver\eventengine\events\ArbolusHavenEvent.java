/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 *
 */
public class ArbolusHavenEvent extends MobEvent {
    public ArbolusHavenEvent() {
        super.mapId = 210040000 ;
        super.center = new SpawnPosition(206, 1577, 111);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("An event at Arbolu's Haven in Heiron will commence in 3 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at Arbolu's Haven starts in 1 minute", 2 * 60 * 1000);
        announceAll("The event at Arbolu's Haven starts in 30 seconds", 30 * 1000 + 2
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 3 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters at Arbolu's Haven in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217079, 213, 1553, 111);
        spawnMob(217079, 173, 1561, 113);
        spawnMob(217079, 149, 1557, 117);
        spawnMob(217079, 137, 1584, 120);
        spawnMob(217079, 163, 1594, 120);
        spawnMob(217079, 157, 1618, 121);
        spawnMob(217079, 160, 1650, 120);
        spawnMob(217079, 181, 1603, 111);
        spawnMob(217079, 231, 1588, 112);
        spawnMob(217079, 231, 1636, 114);
        spawnMob(217079, 206, 1672, 111);
        spawnMob(217079, 177, 1632, 120);
        spawnMob(217079, 201, 1622, 124);
        spawnMob(217079, 227, 1543, 111);
    }
}
