/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.model.templates.item;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ItemEnhance")
public class ItemEnhance {
    
    @XmlAttribute(name = "stone_slots")
    private int stoneSlots = 0;
    
    @XmlAttribute(name = "special_slots")
    private int specialSlots = 0;
    
    @XmlAttribute(name = "max_enchant")
    private int maxEnchant = 0;
    
    @XmlAttribute(name = "max_enchant_bonus")
    private int maxEnchantBonus = 0;
    
    @XmlAttribute(name = "max_temperance")
    private int maxTemperance = 0;
    
    @XmlAttribute(name = "random_option")
    private int randomOption = 0;
    
    @XmlAttribute(name = "retune")
    private int retune = 0;
    
    @XmlAttribute(name = "can_idian")
    private boolean canIdian = false;
    
    @XmlAttribute(name = "can_amplify")
    private boolean canAmplify = false;
    
    @XmlAttribute(name = "pre_amplified")
    private boolean preAmplified = false;

    public int getStoneSlots() {
        return stoneSlots;
    }

    public int getSpecialSlots() {
        return specialSlots;
    }

    public int getMaxEnchant() {
        return maxEnchant;
    }
    
    public int getMaxEnchantBonus() {
        return maxEnchantBonus;
    }
    
    public int getMaxTemperance() {
        return maxTemperance;
    }
    
    public int getRandomOption() {
        return randomOption;
    }
    
    public int getRetune() {
        return retune;
    }

    public boolean canIdian() {
        return canIdian;
    }
    
    public boolean canAmplify() {
        return canAmplify;
    }
    
    public boolean isPreAmplified() {
        return preAmplified;
    }
}
