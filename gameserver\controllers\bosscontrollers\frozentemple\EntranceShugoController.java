/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.frozentemple;

import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.ChatType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.RequestResponseHandler;
import gameserver.model.pvpevents.Battleground;
import gameserver.model.pvpevents.DefenseOfTheDrana;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_QUESTION_WINDOW;
import gameserver.services.EventService;
import gameserver.services.GloryService;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 
 */
public class EntranceShugoController extends BossController {
    private long nextShout = 0;

    public EntranceShugoController() {
        super(205512, true);
    }

    protected void think() {
        Npc owner = getOwner();

        // if (owner.getCustomTag() == null || owner.getCustomTag().equals(""))
        // owner.setCustomTag("Enter Instance");

        if (System.currentTimeMillis() < nextShout)
            return;

        shout();
    }

    @Override
    public void onCreation() {
        getOwner().setCustomTag("Enter Instance");
    }

    private void shout() {
        nextShout = System.currentTimeMillis() + 90000;

        PacketSendUtility.broadcastPacket(getOwner(), new SM_MESSAGE(getOwner().getObjectId(),
            "Assistant Recordkeeper",
            "Welcome! Talk to me when you are ready to enter the instance.", ChatType.ALLIANCE));
    }

    @Override
    public void onDialogRequest(Player player) {
        if (!EventService.getInstance().areInstancesEnabled()) {
            message(player, "The Drana Facility is currently closed.");
            return;
        }

        if (player.isInAlliance()) {
            message(player, "You cannot enter the instance with an Alliance.");
            return;
        }
        else if (player.isInGroup()) {
            if (!player.getPlayerGroup().getGroupLeader().getObjectId()
                .equals(player.getObjectId())) {
                message(player, "Only the group leader can request entry to the instance.");
                return;
            }
        }
        else {
            message(player, "You cannot enter the instance by yourself. You must be in a group.");
            return;
        }

        List<Player> participants = new ArrayList<Player>();

        if (player.isInGroup()) {
            for (Player pl : player.getPlayerGroup().getMembers()) {
                if (!GloryService.getInstance().checkDefense(pl)) {
                    message(player, pl.getName()
                        + " has already reached his daily entry limit for this instance.");
                }
                else {
                    participants.add(pl);
                }
            }

            if (participants.size() != player.getPlayerGroup().size())
                return;
        }
        else {
            if (!GloryService.getInstance().checkDefense(player)) {
                message(player,
                    "You have already reached your daily entry limit for this instance.");
                return;
            }

            participants.add(player);
        }

        message(player, "Click Yes to enter the instance.");
        request(player, participants);
    }

    private void request(Player player, final List<Player> participants) {
        RequestResponseHandler responseHandler = new RequestResponseHandler(getOwner()) {
            @Override
            public void acceptRequest(Creature requester, Player responder) {
                for (Player pl : participants)
                    if (!GloryService.getInstance().checkDefense(pl))
                        return;

                for (Player pl : participants)
                    GloryService.getInstance().enterDefense(pl);

                Battleground bg = new DefenseOfTheDrana();
                bg.setIsPvE(true);
                bg.createMatch(participants);

                if (bg.hasPlayers())
                    LadderService.getInstance().registerBg(bg);
            }

            @Override
            public void denyRequest(Creature requester, Player responder) {
            }
        };

        boolean requested = player.getResponseRequester().putRequest(SM_QUESTION_WINDOW.STR_CUSTOM,
            responseHandler);
        if (requested) {
            PacketSendUtility.sendPacket(player, new SM_QUESTION_WINDOW(
                SM_QUESTION_WINDOW.STR_CUSTOM, 0, "Do you wish to enter the Drana Facility?", ""));
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendPacket(player, new SM_MESSAGE(getOwner().getObjectId(),
            "Assistant Recordkeeper", msg, ChatType.ALLIANCE));
    }
}