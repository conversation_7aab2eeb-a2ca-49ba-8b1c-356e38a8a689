/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class AngriefRuinsEvent extends MobEvent {
    public AngriefRuinsEvent() {
        super.mapId = 210050000;
        super.center = new SpawnPosition(1496, 1737, 326);
        super.apBasePlayer = 500;
        super.apPoolPerPlayer = 500;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("The Angrief Ruins in Inggison will be assaulted in 3 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at the Angrief Ruins in Inggison starts in 2 minutes", 1 * 60 * 1000);
        announceAll("The event at the Angrief Ruins in Inggison starts in 1 minute", 2 * 60 * 1000);
        announceAll("The event at the Angrief Ruins in Inggison starts in 30 seconds", 30 * 1000
            + 2 * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 3 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters in the Angrief Ruins in the next 3 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 3 * 60));
        }

        super.scheduleEnd(3 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217937, 1494, 1748, 328);
        spawnMob(217937, 1497, 1756, 326);
        spawnMob(217937, 1502, 1764, 323);
        spawnMob(217937, 1510, 1759, 322);
        spawnMob(217937, 1514, 1751, 323);
        spawnMob(217937, 1510, 1743, 325);
        spawnMob(217937, 1513, 1734, 326);
        spawnMob(217937, 1505, 1728, 326);
        spawnMob(217937, 1497, 1725, 327);
        spawnMob(217937, 1489, 1736, 329);
        spawnMob(217937, 1483, 1734, 332);
        spawnMob(217937, 1488, 1722, 323);
        spawnMob(217937, 1481, 1719, 337);
        spawnMob(217937, 1474, 1731, 337);
        spawnMob(217937, 1499, 1736, 327);
    }
}
