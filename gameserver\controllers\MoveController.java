/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.ai.desires.impl.MoveToHomeDesire;
import gameserver.ai.events.Event;
import gameserver.configs.main.FallDamageConfig;
import gameserver.controllers.movement.MovementType;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.geoEngine2.math.Vector3f;
import gameserver.model.EmotionType;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.DecorationObject;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.siege.FortressGeneral;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_LOOKATOBJECT;
import gameserver.network.aion.serverpackets.SM_MOVE;
import gameserver.network.aion.serverpackets.SM_POSITION_CORRECTION;
import gameserver.services.MountService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.stats.StatFunctions;
import gameserver.world.World;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 */
public class MoveController {
    private static final Logger log = Logger.getLogger(MoveController.class);

    private Future<?> moveTask;
    private Creature owner;
    private boolean directionChanged = true;

    private VisibleObject target;

    private float targetX;
    private float targetY;
    private float targetZ;

    private float ownerX;
    private float ownerY;
    private float ownerZ;

    private float originX;
    private float originY;
    private float originZ;

    private float originX2;
    private float originY2;
    private float originZ2;

    private float x2;
    private float y2;
    private float z2;
    private byte h2;

    private static final int MOVE_DIVIDER = 10;

    private MovementType lastMoveType = MovementType.MOVEMENT_STOP;

    private boolean isFollowTarget;
    private boolean isStopped = false;

    private float speed = 0;
    private float distance = 2;

    private double distanceToTarget;
    private boolean walking;
    private boolean canWalk = true;

    private long nextPacket = 0;

    private MovementType moveType = MovementType.MOVEMENT_START_MOUSE;
    private int moveDirection = MoveDirection.NONE.getId();
    private MoveDirection currentMoveDirection = MoveDirection.NONE;

    private Map<MoveDirection, ScheduledFuture<?>> scheduledMoveDirections = new ConcurrentHashMap<MoveDirection, ScheduledFuture<?>>();
    private Map<MoveDirection, ScheduledFuture<?>> scheduledDirectionUnsets = new ConcurrentHashMap<MoveDirection, ScheduledFuture<?>>();

    private Queue<Boolean> noGravityTicks = new ConcurrentLinkedQueue<Boolean>();
    private Queue<LoggedMove> loggedMoves = new ConcurrentLinkedQueue<LoggedMove>();
    private Queue<Float> loggedSpeeds = new ConcurrentLinkedQueue<Float>();

    private float fallDistance = 0;
    private float lastFallZ = 0;

    private float loggedSpeed = 0;

    private long leewayTime = 0;

    private Creature followLeader = null;
    private float followX;
    private float followY;
    private float followZ;

    private boolean mountSprinting = false;
    private boolean inGeyser = false;

    private long lastForceMove = 0;

    private float effectTargetX = 0;
    private float effectTargetY = 0;
    private float effectTargetZ = 0;

    private AtomicInteger moveTicker = new AtomicInteger(0);

    private ScheduledFuture<?> fixerTask = null;
    private ReentrantLock fixerLock = new ReentrantLock();

    private AtomicInteger glideTicker = new AtomicInteger(0);
    private long glideTimer = 0;

    private long lastSpeedChange = 0;

    private long lastSpeedBlock = 0;

    /**
     * @param owner
     */
    public MoveController(Creature owner) {
        this.owner = owner;
    }

    /**
     * @param isFollowTarget
     *            the isFollowTarget to set
     */
    public void setFollowTarget(boolean isFollowTarget) {
        this.isFollowTarget = isFollowTarget;
    }

    /**
     * @param directionChanged
     *            the directionChanged to set
     */
    public void setDirectionChanged(boolean directionChanged) {
        this.directionChanged = directionChanged;
    }

    /**
     * @param speed
     *            the speed to set
     */
    public void setSpeed(float speed) {
        this.speed = speed;
    }

    /**
     * @return The speed.
     */
    public float getSpeed() {
        return speed;
    }

    /**
     * @param distance
     *            the distance to set
     */
    public void setDistance(float distance) {
        this.distance = distance;
    }

    /**
     * @return the walking
     */
    public boolean isWalking() {
        return walking;
    }

    /**
     * @param walking
     *            the walking to set
     */
    public void setWalking(boolean walking) {
        this.walking = walking;
    }

    /**
     * @return creature is able to walk
     */
    public boolean canWalk() {
        return canWalk;
    }

    /**
     * @param canWalk
     *            if creature is able to walk
     */
    public void setCanWalk(boolean canWalk) {
        this.canWalk = canWalk;
    }

    public void setOwner(Creature owner) {
        this.owner = owner;
    }

    public void setPosition(float x, float y, float z) {
        setPosition(x, y, z, originX2, originY2, originZ2);
    }

    public void setPosition(float x, float y, float z, float x2, float y2, float z2) {
        if (!(owner instanceof Player))
            return;

        this.ownerX = x;
        this.ownerY = y;
        this.ownerZ = z;
        this.originX = x;
        this.originY = y;
        this.originZ = z;
        this.originX2 = x2;
        this.originY2 = y2;
        this.originZ2 = z2;
        this.speed = MountService.getSpeed((Player) owner);

        this.loggedMoves.add(new LoggedMove(x, y, z, System.currentTimeMillis()));

        this.moveTicker.set(0);
    }

    public void startMovement(MovementType moveType, float x, float y, float z, float x2, float y2,
        float z2, byte heading) {
        if (moveTask != null)
            moveTask.cancel(true);

        this.ownerX = x;
        this.ownerY = y;
        this.ownerZ = z;
        this.originX = x;
        this.originY = y;
        this.originZ = z;
        this.originX2 = x2;
        this.originY2 = y2;
        this.originZ2 = z2;
        this.speed = MountService.getSpeed((Player) owner);

        this.loggedMoves.add(new LoggedMove(x, y, z, System.currentTimeMillis()));

        if (!GeoEngine2.getInstance().isInBounds(owner))
            return;

        if (speed == 0)
            return;

        switch (moveType) {
            case MOVEMENT_START_KEYBOARD:
                this.speed *= getRelativeDirectionSpeed(x2, y2, heading);

                double speedLen = speed / (MOVE_DIVIDER * Math.sqrt(x2 * x2 + y2 * y2 + z2 * z2));
                this.x2 = (float) (speedLen * x2);
                this.y2 = (float) (speedLen * y2);

                if (z2 != 0 && ((Player) owner).getFlyState() > 0)
                    this.z2 = (float) (speedLen * z2);
                else
                    this.z2 = 0;
                break;

            case MOVEMENT_START_MOUSE:
                double dist = MathUtil.getDistance(x, y, z, x2, y2, z2);
                double speedDist = speed / MOVE_DIVIDER;

                this.x2 = (float) (speedDist * (x2 - x) / dist);
                this.y2 = (float) (speedDist * (y2 - y) / dist);

                if (z2 != 0 && ((Player) owner).getFlyState() > 0)
                    this.z2 = (float) (speedDist * (z2 - z) / dist);
                else
                    this.z2 = 0;
                break;
        }

        moveTicker.set(0);

        moveTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                if (moveTicker.incrementAndGet() > 15) {
                    if (moveTask != null)
                        moveTask.cancel(false);
                }
                else {
                    playerMove();
                }
            }
        }, (moveType == MovementType.MOVEMENT_START_MOUSE ? 100 : 0) + (int) (1000 / MOVE_DIVIDER),
            (int) (1000 / MOVE_DIVIDER));
    }

    private MoveDirection computeMoveDirection(MovementType moveType, float x2, float y2,
        byte heading) {
        switch (moveType) {
            case MOVEMENT_START_KEYBOARD:
                if (x2 == 0f && y2 == 0f)
                    return MoveDirection.NONE;

                float angle = MathUtil.convertHeadingToDegree(heading);
                float angleTarget = (float) Math.toDegrees(Math.atan2(y2, x2));
                if (angleTarget < 0)
                    angleTarget = 360 + angleTarget;

                float angleDiff = Math.abs(angleTarget - angle) % 360;
                if (angleDiff > 180)
                    angleDiff = 360 - angleDiff;

                if (angleDiff <= 60)
                    return MoveDirection.FORWARD; // forward

                if (angleDiff <= 150)
                    return MoveDirection.STRAFE; // strafe

                return MoveDirection.BACKWARD; // backward

            case MOVEMENT_START_MOUSE:
                return MoveDirection.FORWARD;

            case MOVEMENT_STOP:
                return MoveDirection.NONE;

            default:
                return currentMoveDirection;
        }
    }

    public float getRelativeDirectionSpeed(float x2, float y2, byte heading) {
        float angle = MathUtil.convertHeadingToDegree(heading);
        float angleTarget = (float) Math.toDegrees(Math.atan2(y2, x2));
        if (angleTarget < 0)
            angleTarget = 360 + angleTarget;

        float angleDiff = Math.abs(angleTarget - angle) % 360;
        if (angleDiff > 180)
            angleDiff = 360 - angleDiff;

        if (angleDiff <= 20)
            return 1f; // forward

        if (angleDiff <= 60)
            return 0.9f; // forward side-strafe

        if (angleDiff <= 100)
            return 0.8f; // full strafe

        if (angleDiff <= 150)
            return 0.7f; // backwards side-strafe

        return 0.6f;
    }

    private void playerMove() {
        if (!owner.canPerformMove()) {
            if (moveTask != null)
                moveTask.cancel(false);
            return;
        }

        if (lastMoveType == MovementType.MOVEMENT_START_MOUSE
            && MathUtil.getDistance(ownerX, ownerY, ownerZ, targetX, targetY, targetZ) <= speed
                / MOVE_DIVIDER) {
            if (moveTask != null)
                moveTask.cancel(false);
            return;
        }

        Vector3f coll = GeoEngine2.getInstance().getClosestCollision(owner, ownerX + x2,
            ownerY + y2, ownerZ + z2);
        ownerX = coll.getX();
        ownerY = coll.getY();

        if (((Player) owner).getFlyState() > 0 || fallDistance > 0)
            ownerZ += z2;
        else
            ownerZ = GeoEngine2.getInstance().getZ(owner.getWorldId(), coll.getX(), coll.getY(),
                coll.getZ());

        // PacketSendUtility.sendMessage((Player) owner, String.format(
        // "[C2M] ownerX %.02f, ownerY %.02f, ownerZ %.02f (%.02f, %.02f, %.02f)", ownerX, ownerY,
        // ownerZ, x2, y2, z2));

        if (!GeoEngine2.getInstance().isInBounds(owner.getWorldId(), ownerX, ownerY, ownerZ)) {
            ownerX = owner.getX();
            ownerY = owner.getY();
            ownerZ = owner.getZ();

            if (moveTask != null)
                moveTask.cancel(false);
        }
        else {
            World.getInstance().updatePosition(owner, ownerX, ownerY, ownerZ, owner.getHeading(),
                false);
        }
    }

    public void setNewDirection(float x, float y, float z) {
        if (x != targetX || y != targetY || z != targetZ)
            directionChanged = true;
        this.targetX = x;
        this.targetY = y;
        this.targetZ = z;
    }

    public float getTargetX() {
        return targetX;
    }

    public float getTargetY() {
        return targetY;
    }

    public float getTargetZ() {
        return targetZ;
    }

    public boolean isScheduled() {
        return moveTask != null && !moveTask.isCancelled();
    }

    public void schedule() {
        if (isScheduled())
            return;

        if (speed == 0)
            speed = owner.getGameStats().getCurrentStat(StatEnum.SPEED) / 1000;

        moveTask = ThreadPoolManager.getInstance().scheduleAiAtFixedRate(new Runnable() {
            @Override
            public void run() {
                move();
            }
        }, 0, 500);
    }

    private void move() {
        /**
         * Demo npc skills - prevent movement while casting
         */
        if (!owner.canPerformMove() || owner.isCasting() || !canWalk) {
            if (!isStopped) {
                isStopped = true;
                owner.getController().stopMoving();
            }
            return;
        }

        target = owner.getTarget();
        ownerX = owner.getX();
        ownerY = owner.getY();
        ownerZ = owner.getZ();

        if (isFollowTarget && target instanceof Creature
            && ((Creature) target).getLifeStats().isAlreadyDead()) {
            target = null;
            isFollowTarget = false;
            owner.setTarget(target);
            PacketSendUtility.broadcastPacket(owner, new SM_LOOKATOBJECT(owner));
        }

        if (isFollowTarget && target != null
            && MathUtil.getDistance(owner, target) > (distance - 1)) {
            double radian = Math.atan2(target.getY() - owner.getY(), target.getX() - owner.getX());

            if (radian < 0)
                radian += (2 * Math.PI);

            float dist = distance > 8 ? distance / 2 : distance - 1;

            if (target instanceof Npc)
                dist += target.getBoundRadius().getFront() / 3;

            float xx = (float) (target.getX() - (dist * Math.cos(radian)));
            float yy = (float) (target.getY() - (dist * Math.sin(radian)));
            float zz = GeoEngine2.getInstance().getZ(owner.getWorldId(), xx, yy,
                (target.getZ() + owner.getZ()) / 2F, 2F);

            if (Math.abs(zz - owner.getZ()) > 50f)
                zz = owner.getZ();

            setNewDirection(xx, yy, zz);
        }

        if (!isFollowTarget && hasFollowLeader()) {
            setNewDirection(followX, followY, followZ);
        }

        distanceToTarget = MathUtil.getDistance(ownerX, ownerY, ownerZ, targetX, targetY, targetZ);

        // 2 hacks for FortressGenerals
        if (owner instanceof FortressGeneral) {
            this.distance = 3.5f;

            if (MathUtil.getDistance(owner, owner.getSpawn().getX(), owner.getSpawn().getY(), owner
                .getSpawn().getZ()) > 30) {
                owner.getAi().clearDesires();
                owner.getAi().addDesire(new MoveToHomeDesire((Npc) owner, 100));
                owner.getAi().schedule();
                return;
            }
        }

        if (isFollowTarget && target != null && Math.abs(ownerZ - target.getZ()) > 20) {
            owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);
            return;
        }
        else if (!isFollowTarget && hasFollowLeader() && distanceToTarget < 0.1) {
            if (!isStopped) {
                isStopped = true;
                // owner.getController().stopMoving();
            }

            stop();
        }
        else if (distanceToTarget < speed * 0.5f) {
            /*
             * if (target != null && target instanceof Player) { ownerZ = GeoEngine2.getInstance().getZ(owner); Player
             * player = (Player) target; if (player.isInState(CreatureState.FLYING) ||
             * player.isInState(CreatureState.GLIDING)) targetZ = GeoEngine2.getInstance().getZ(owner.getWorldId(),
             * targetX, targetY, targetZ); }
             */

            double angle = Math.toDegrees(Math.atan2((targetY - ownerY), (targetX - ownerX))) / 3;
            if (angle < 0)
                angle += 120;

            h2 = (byte) angle;

            if (directionChanged || System.currentTimeMillis() > nextPacket) {
                PacketSendUtility.broadcastPacket(owner, new SM_MOVE(owner, ownerX, ownerY, ownerZ,
                    targetX, targetY, targetZ, h2, MovementType.MOVEMENT_START_MOUSE));
                nextPacket = System.currentTimeMillis() + 10000;
            }

            World.getInstance().updatePosition(owner, targetX, targetY, targetZ, h2, true);

            owner.getController().onMove();

            /*
             * ThreadPoolManager.getInstance().schedule(new Runnable() {
             * @Override public void run() { if (owner.getTarget() != null && System.currentTimeMillis() >
             * owner.getNextAttack()) { owner.getController().attackTarget((Creature) owner.getTarget());
             * owner.setNextAttack(System.currentTimeMillis() + ((Npc) owner).getObjectTemplate().getAttackRate()); } }
             * }, Rnd.get(400, 500));
             */

            directionChanged = false;
            isStopped = true;

            boolean oldIsFollowTarget = isFollowTarget;

            stop();

            isFollowTarget = oldIsFollowTarget;
        }
        else {
            // Hot fix for the running off into nothing-ness bug
            if (MathUtil.getDistance(targetX, targetY, targetZ, 0f, 0f, 0f) < 1f) {
                if (!isStopped) {
                    isStopped = true;
                    owner.getController().stopMoving();
                }

                stop();
                return;
            }

            x2 = (float) (((targetX - ownerX) / distanceToTarget) * speed * 0.5f);
            y2 = (float) (((targetY - ownerY) / distanceToTarget) * speed * 0.5f);

            double angle = Math.toDegrees(Math.atan2(y2, x2)) / 3;
            if (angle < 0)
                angle += 120;

            h2 = (byte) angle;

            isStopped = false;

            float oldZ = ownerZ;

            ownerZ = GeoEngine2.getInstance().getZ(owner.getWorldId(), ownerX + x2, ownerY + y2,
                ownerZ);

            // Stop if too large Z-difference
            if (Math.abs(ownerZ - oldZ) > 8) {
                ownerZ = oldZ;

                if (!isStopped) {
                    isStopped = true;
                    owner.getController().stopMoving();
                }

                return;
            }

            if (directionChanged || System.currentTimeMillis() > nextPacket) {
                PacketSendUtility.broadcastPacket(owner, new SM_MOVE(owner, ownerX, ownerY, oldZ,
                    targetX, targetY, targetZ, h2, MovementType.MOVEMENT_START_MOUSE));
                nextPacket = System.currentTimeMillis() + 10000;

                directionChanged = false;
                World.getInstance().updatePosition(owner, ownerX + x2, ownerY + y2, ownerZ, h2,
                    true);
            }
            else {
                World.getInstance().updatePosition(owner, ownerX + x2, ownerY + y2, ownerZ, h2,
                    true);
            }

            owner.getController().onMove();
        }
    }

    public double getDistanceToTarget() {
        if (isFollowTarget) {
            VisibleObject target = owner.getTarget();
            if (target != null)
                return MathUtil.getDistance(owner.getX(), owner.getY(), owner.getZ(),
                    target.getX(), target.getY(), target.getZ());

        }
        return MathUtil.getDistance(owner.getX(), owner.getY(), owner.getZ(), targetX, targetY,
            targetZ);
    }

    public synchronized void stop() {
        // this.walking = false;
        this.isFollowTarget = false;
        // this.moveDirection = MoveDirection.NONE;

        if (moveTask != null) {
            if (!moveTask.isCancelled())
                moveTask.cancel(true);
            moveTask = null;
        }
    }

    public void updateFormation() {
        if (owner == null || owner.getFollowers() == null || owner.getFollowers().size() <= 0)
            return;

        synchronized (owner.getFollowers()) {
            for (Iterator<Creature> it = owner.getFollowers().iterator(); it.hasNext();) {
                Creature follower = it.next();

                if (!follower.isSpawned() || follower.getLifeStats().isAlreadyDead()
                    || follower.getWorldId() != owner.getWorldId()
                    || follower.getInstanceId() != owner.getInstanceId())
                    it.remove();
            }
        }

        float x = owner.getX(), y = owner.getY(), z = owner.getZ(), d = owner
            .getFormationDistance();
        double r = Math.toRadians(MathUtil.convertHeadingToDegree(owner.getHeading()));

        try {
            double back = Math.PI + r;

            float dx = (float) Math.cos(back) * d;
            float dy = (float) Math.sin(back) * d;

            float xx = x, yy = y;

            int row = 1;

            switch (owner.getFormationType()) {
                case COLUMN:
                    synchronized (owner.getFollowers()) {
                        int size = owner.getFollowers().size();

                        int rowSize = (int) (2 + Math.floor(size / 7));

                        for (int i = 0; i < size;) {
                            for (int j = 0; j < rowSize; j++) {
                                if (i + j >= size)
                                    break;

                                MoveController mc = owner.getFollowers().get(i + j)
                                    .getMoveController();

                                xx = x + dx * row;
                                yy = y + dy * row;

                                if (rowSize % 2 == 0) { // Even row size - no center
                                    int floor = j / 2 + 1;

                                    xx += (j % 2 == 0) ? -dy * (floor - 0.5) : dy * (floor - 0.5);
                                    yy += (j % 2 == 0) ? dx * (floor - 0.5) : -dx * (floor - 0.5);
                                }
                                else { // Uneven row size - center
                                    int ceil = (j - 1) / 2 + 1;

                                    if (j != 0) {
                                        xx += (j % 2 == 0) ? -dy * ceil : dy * ceil;
                                        yy += (j % 2 == 0) ? dx * ceil : -dx * ceil;
                                    }
                                }

                                mc.followHere(
                                    owner,
                                    xx,
                                    yy,
                                    GeoEngine2.getInstance().getZ(owner.getWorldId(), xx, yy, z,
                                        2.5F));
                            }

                            row++;
                            i += rowSize;
                        }
                    }

                    break;
                case TRIANGLE:
                    synchronized (owner.getFollowers()) {
                        int size = owner.getFollowers().size();

                        for (int i = 0; i < size;) {
                            for (int j = 0; j < (row + 1); j++) {
                                if (i + j >= size)
                                    break;

                                MoveController mc = owner.getFollowers().get(i + j)
                                    .getMoveController();

                                xx = x + dx * row;
                                yy = y + dy * row;

                                if (row % 2 == 0) { // Even row - has center follower
                                    int ceil = (j - 1) / 2 + 1;

                                    if (j != 0) {
                                        xx += (j % 2 == 0) ? -dy * ceil : dy * ceil;
                                        yy += (j % 2 == 0) ? dx * ceil : -dx * ceil;
                                    }
                                }
                                else { // Uneven row - no center
                                    int floor = j / 2 + 1;

                                    xx += (j % 2 == 0) ? -dy * (floor - 0.5) : dy * (floor - 0.5);
                                    yy += (j % 2 == 0) ? dx * (floor - 0.5) : -dx * (floor - 0.5);
                                }

                                mc.followHere(
                                    owner,
                                    xx,
                                    yy,
                                    GeoEngine2.getInstance().getZ(owner.getWorldId(), xx, yy, z,
                                        2.5F));
                            }

                            i += ++row;
                        }
                    }

                    break;
            }
        }
        catch (Exception e) {
            log.error(e);
        }
    }

    @SuppressWarnings("unchecked")
    public void followHere(Creature followLeader, float x, float y, float z) {
        this.followLeader = followLeader;
        this.followX = x;
        this.followY = y;
        this.followZ = z;

        if (!isScheduled() && followLeader != null) {
            final boolean walk = followLeader.getMoveController().isWalking();

            if (!owner.getController().hasActiveTask(TaskId.FOLLOW)) {
                owner.getController().addTask(TaskId.FOLLOW,
                    ThreadPoolManager.getInstance().schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (walk) {
                                boolean wasWalking = walking;

                                setSpeed(owner.getGameStats().getCurrentStat(StatEnum.WALK) * 0.001f);
                                setWalking(true);

                                if (owner.isInState(CreatureState.WEAPON_EQUIPPED)) {
                                    owner.setState(CreatureState.NPC_IDLE);
                                    owner.unsetState(CreatureState.WEAPON_EQUIPPED);

                                    PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                                        EmotionType.NEUTRALMODE));
                                }

                                if (!wasWalking)
                                    PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                                        EmotionType.WALK));
                            }
                            else {
                                boolean wasWalking = walking;

                                setSpeed(owner.getGameStats().getCurrentStat(StatEnum.SPEED) * 0.001f);
                                setWalking(false);

                                if (!owner.isInState(CreatureState.WEAPON_EQUIPPED)) {
                                    owner.unsetState(CreatureState.NPC_IDLE);
                                    owner.setState(CreatureState.WEAPON_EQUIPPED);

                                    PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                                        EmotionType.ATTACKMODE));
                                }

                                if (wasWalking)
                                    PacketSendUtility.broadcastPacketAndReceive(owner,
                                        new SM_EMOTION(owner, EmotionType.RUN));
                            }

                            schedule();
                        }
                    }, 250));
            }
        }
    }

    public boolean hasFollowLeader() {
        if (followLeader != null
            && (followLeader.getLifeStats().isAlreadyDead()
                || !MathUtil.isIn3dRange(followLeader, owner, 100) || !followLeader.isSpawned())) {
            followLeader.removeFollower(owner);
            followLeader = null;
        }

        return followLeader != null;
    }

    public Creature getFollowLeader() {
        return hasFollowLeader() ? followLeader : null;
    }

    public void setMoveType(MovementType moveType) {
        this.moveType = moveType;
    }

    public MovementType getMoveType() {
        return moveType;
    }

    public float getOriginX() {
        return originX;
    }

    public float getOriginY() {
        return originY;
    }

    public float getOriginZ() {
        return originZ;
    }

    public void logLastMove(final MovementType moveType, final float x, final float y, final float z) {
        if (!(owner instanceof Player))
            return;

        final Player player = (Player) owner;

        fixerLock.lock();

        try {
            switch (moveType) {
                case MOVEMENT_STOP:
                    if (fixerTask != null) {
                        fixerTask.cancel(true);
                        fixerTask = null;
                    }

                    if (isBeingForcedMove())
                        break;

                    /*fixerTask = ThreadPoolManager.getInstance().schedule(new Runnable() {
                        @Override
                        public void run() {
                            if (isBeingForcedMove())
                                return;

                            PacketSendUtility.broadcastPacket(player, new SM_MOVE(player, x, y, z,
                                player.getHeading(), moveType), false);
                        }
                    }, 1000);*/
                    break;
                default:
                    if (fixerTask != null) {
                        fixerTask.cancel(true);
                        fixerTask = null;
                    }
                    break;
            }
        }
        finally {
            fixerLock.unlock();
        }

        return;

        /*
         * switch (moveType) { case VALIDATE_JUMP: case VALIDATE_JUMP_WHILE_MOVING: while (noGravityTicks.size() > 12)
         * noGravityTicks.poll(); if (z == originZ && moveType == getMoveType() &&
         * !owner.getLifeStats().isAlreadyDead()) noGravityTicks.add(true); else noGravityTicks.add(false); int count =
         * 0; for (Iterator<Boolean> it = noGravityTicks.iterator(); it.hasNext();) if (it.next()) count++; if (count >
         * 6 && ((Player) owner).getAccessLevel() < 1) { TeleportService.moveToBindLocation((Player) owner, true);
         * ThreadPoolManager.getInstance().schedule(new Runnable() {
         * @Override public void run() { if (((Player) owner).getClientConnection() != null) ((Player)
         * owner).getClientConnection().close( new SM_QUIT_RESPONSE(), true); else owner.getController().delete(); } },
         * 2000); } break; } setMoveType(moveType);
         */
    }

    public void cancelFixer() {
        fixerLock.lock();

        try {
            if (fixerTask != null) {
                fixerTask.cancel(true);
                fixerTask = null;
            }
        }
        finally {
            fixerLock.unlock();
        }
    }

    public boolean hasMoveDirection(MoveDirection direction) {
        return (moveDirection & direction.getId()) == direction.getId();
    }

    public void setMoveDirection(MoveDirection direction) {
        this.moveDirection |= direction.getId();
    }

    public void unsetMoveDirection(MoveDirection direction) {
        this.moveDirection &= ~direction.getId();
    }

    public void clearMoveDirection() {
        this.currentMoveDirection = MoveDirection.NONE;
        this.moveDirection = MoveDirection.NONE.getId();
    }

    public int getMoveDirection() {
        return this.moveDirection;
    }

    public MoveDirection updateMoveDirection(MovementType moveType, float x2, float y2, byte heading) {
        this.currentMoveDirection = computeMoveDirection(moveType, x2, y2, heading);

        if (currentMoveDirection != MoveDirection.NONE
            && !hasMoveDirection(currentMoveDirection)
            && !(scheduledMoveDirections.containsKey(currentMoveDirection) && !scheduledMoveDirections
                .get(currentMoveDirection).isDone())) {
            scheduledMoveDirections.put(currentMoveDirection,
                scheduleSetMoveDirection(this.currentMoveDirection));
        }

        for (MoveDirection direction : MoveDirection.getMoveDirections(moveDirection)) {
            if (direction == MoveDirection.NONE)
                continue;

            if (direction == currentMoveDirection) {
                if (scheduledDirectionUnsets.containsKey(direction)) {
                    ScheduledFuture<?> future = scheduledDirectionUnsets.remove(direction);
                    future.cancel(true);
                }

                continue;
            }

            if (!scheduledDirectionUnsets.containsKey(direction)
                || scheduledDirectionUnsets.get(direction).isDone()) {
                scheduledDirectionUnsets.put(direction, scheduleUnsetMoveDirection(direction));
            }
        }

        return this.currentMoveDirection;
    }

    public ScheduledFuture<?> scheduleSetMoveDirection(final MoveDirection direction) {
        return ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (direction == currentMoveDirection) {
                    setMoveDirection(direction);
                    // scheduleUnsetMoveDirection(direction);
                }
            }
        }, 500);
    }

    public ScheduledFuture<?> scheduleUnsetMoveDirection(final MoveDirection direction) {
        return ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (direction != currentMoveDirection)
                    unsetMoveDirection(direction);
            }
        }, 1500);
    }

    public boolean validateNewPosition(MovementType type, float x, float y, float z, float x2,
        float y2, float z2, byte heading, byte glideFlag, byte glideFlag2) {
        if (!(owner instanceof Player))
            return true;

        switch (type) {
            case MOVEMENT_GLIDE_DOWN:
            case MOVEMENT_GLIDE_UP:
            case MOVEMENT_GLIDE_START_MOUSE:
            case VALIDATE_GLIDE_MOUSE:
                if (z2 <= 0 && originZ2 <= 0 && z - getOriginZ() > .2F && glideFlag2 == 0) {
                    if (System.currentTimeMillis() - glideTimer > 5000)
                        glideTicker.set(0);

                    glideTimer = System.currentTimeMillis();

                    if (glideTicker.incrementAndGet() > 6) {
                        // log.info("[AUDIT] Player " + owner.getName()
                        // + " seems to be glide-hacking.");
                        return false;
                    }
                    else if (z - getOriginZ() > 4F) {
                        return false;
                    }
                }
                break;
            case MOVEMENT_START_KEYBOARD:
            case MOVEMENT_START_MOUSE:
            case VALIDATE_KEYBOARD:
            case VALIDATE_MOUSE:
                if (((Player) owner).getFlyState() == 1 || isInGeyser())
                    break;

                if (z - getOriginZ() > 3F) {
                    float geoZ = GeoEngine2.getInstance().getZ(owner.getWorldId(), x, y, z);

                    if (z - geoZ > 2F)
                        return false;
                }
                break;
            case VALIDATE_JUMP:
            case VALIDATE_JUMP_WHILE_MOVING:
                if (!isInGeyser() && z - getOriginZ() > 3F)
                    return false;
                break;
            case MOVEMENT_STOP:
                if (((Player) owner).getFlyState() != 0 || isInGeyser())
                    break;

                float geoZ = GeoEngine2.getInstance().getZ(owner.getWorldId(), x, y, z);

                if (z - geoZ > 8f) {
                    boolean decorationNear = false;

                    for (Iterator<AionObject> it = owner.getKnownList().getObjects().iterator(); it
                        .hasNext();) {
                        AionObject obj = it.next();

                        if (!(obj instanceof DecorationObject))
                            continue;

                        DecorationObject decor = (DecorationObject) obj;

                        if (MathUtil.getDistance(decor, x, y, z) <= 3.5d) {
                            decorationNear = true;
                            break;
                        }
                        else if (MathUtil.getDistance(decor.getX(), decor.getY(), x, y) <= 2.5d
                            && z - decor.getZ() <= 5f) {
                            decorationNear = true;
                            break;
                        }
                    }

                    if (!decorationNear) {
                        originZ = geoZ;
                        return false;
                    }
                }
                break;
        }

        float speed = MountService.getSpeed((Player) owner);

        if (speed != this.loggedSpeed) {
            loggedMoves.clear();
            loggedSpeeds.clear();
            lastSpeedChange = System.currentTimeMillis();
        }

        this.loggedSpeed = speed;

        if ((type.getMovementTypeId() & 0x08) == 0x08) // fall/jump
            speed *= 1.5F;

        if (System.currentTimeMillis() - leewayTime < 4000) {
            if (((Player) owner).isInState(CreatureState.GLIDING))
                speed = owner.getGameStats().getCurrentStat(StatEnum.SPEED) * 3F;
            else
                speed *= 3F;
        }

        // Keyboard speed tracking
        if ((type.getMovementTypeId() & 0xC0) == 0xC0 && (type.getMovementTypeId() & 0x20) == 0) {
            if (System.currentTimeMillis() - lastSpeedChange < 500) {
                /*
                 * if (((Player) owner).getAccessLevel() >= 5) PacketSendUtility.sendMessage( (Player) owner,
                 * String.format("[DBG] Skipping log of speed %.01f (stat %.01f)", Math.sqrt(x2 * x2 + y2 * y2) /
                 * relative, this.loggedSpeed));
                 */
            }
            else {
                /*
                 * if (((Player) owner).getAccessLevel() >= 5) PacketSendUtility.sendMessage( (Player) owner,
                 * String.format("[DBG] Logging speed %.01f (stat %.01f)", Math.sqrt(x2 * x2 + y2 * y2) / relative,
                 * this.loggedSpeed));
                 */
                float relative = getRelativeDirectionSpeed(x2, y2, heading);

                loggedSpeeds.add((float) Math.sqrt(x2 * x2 + y2 * y2) / relative);

                if (loggedSpeeds.size() >= 7) {
                    while (loggedSpeeds.size() > 10)
                        loggedSpeeds.poll();

                    int counter = 0;
                    float highestSpeed = 0f;
                    for (float logSpeed : loggedSpeeds) {
                        if (logSpeed - .1F > this.loggedSpeed) {
                            counter++;

                            if (logSpeed > highestSpeed)
                                highestSpeed = logSpeed;
                        }
                    }

                    if (counter * 100 / loggedSpeeds.size() >= 60) {
                        if (System.currentTimeMillis() - lastSpeedBlock < 60 * 1000) {
                            log.info("[AUDIT] Player " + owner.getName()
                                + " possible speed hack (peak "
                                + String.format("%.01f", highestSpeed) + " should be "
                                + String.format("%.01f", this.loggedSpeed) + " at " + counter + "/"
                                + loggedSpeeds.size() + ")");
                        }

                        loggedSpeeds.clear();

                        lastSpeedBlock = System.currentTimeMillis();
                    }
                }
            }
        }

        if (loggedMoves.size() >= 2) {
            while (loggedMoves.size() > 2)
                loggedMoves.poll();

            LoggedMove earliestMove = loggedMoves.poll();

            long timeDelta = 200 + System.currentTimeMillis() - earliestMove.getTime();
            timeDelta *= 1.20f;

            float timeSpeed = speed * timeDelta / 1000f;

            // PacketSendUtility.sendMessage((Player) owner, String.format("Move: %.02f - %.02f [%d]",
            // MathUtil.getDistance(earliestMove.getX(), earliestMove.getY(), x, y), timeSpeed,
            // timeDelta));
            if (MathUtil.getDistance(earliestMove.getX(), earliestMove.getY(), x, y) > timeSpeed) {
                loggedMoves.clear();
                return false;
            }
        }
        else if ((type.getMovementTypeId() & 0xC0) == 0xC0
            && (type.getMovementTypeId() & 0x20) == 0) { // start keyboard

            if (x2 * x2 + y2 * y2 > 3F * speed * speed) {
                return false;
            }
        }
        else { // other moves
            if (MathUtil.getDistance(owner.getMoveController().getOriginX(), owner
                .getMoveController().getOriginY(), x, y) > 2F * speed)
                return false;
        }

        return true;
    }

    public void clearLoggedMoves() {
        loggedMoves.clear();
    }

    public void setLeewayTime() {
        leewayTime = System.currentTimeMillis();
    }

    public boolean isMountSprinting() {
        return mountSprinting;
    }

    public void setMountSprinting(boolean mountSprinting) {
        this.mountSprinting = mountSprinting;
    }

    public boolean isInGeyser() {
        return inGeyser;
    }

    public void setInGeyser(boolean inGeyser) {
        this.inGeyser = inGeyser;
    }

    public long getLastForceMove() {
        return lastForceMove;
    }

    public void setLastForceMove() {
        this.lastForceMove = System.currentTimeMillis();
        cancelFixer();
    }

    public void clearLastForceMove() {
        this.lastForceMove = 0;
    }

    public boolean isBeingForcedMove() {
        return System.currentTimeMillis() < lastForceMove + 400;
    }

    public float getEffectTargetX() {
        return effectTargetX;
    }

    public void setEffectTargetX(float effectTargetX) {
        this.effectTargetX = effectTargetX;
    }

    public float getEffectTargetY() {
        return effectTargetY;
    }

    public void setEffectTargetY(float effectTargetY) {
        this.effectTargetY = effectTargetY;
    }

    public float getEffectTargetZ() {
        return effectTargetZ;
    }

    public void setEffectTargetZ(float effectTargetZ) {
        this.effectTargetZ = effectTargetZ;
    }

    public void updateFalling(float newZ) {
        if (lastFallZ != 0) {
            fallDistance += lastFallZ - newZ;

            if (fallDistance >= FallDamageConfig.MAXIMUM_DISTANCE_MIDAIR) {
                StatFunctions.calculateFallDamage((Player) owner, fallDistance, false);

                fallDistance = 0;
                lastFallZ = 0;

                return;
            }
        }

        lastFallZ = newZ;
    }

    public void stopFalling(float newZ) {
        if (owner.isFlying()) {
            fallDistance = 0;
            lastFallZ = 0;
        }
        else if (lastFallZ != 0) {
            fallDistance += lastFallZ - newZ;
            lastFallZ = newZ;

            if (Math.abs(GeoEngine2.getInstance().getZ(owner) - newZ) < 10) {
                if (!owner.isFlying())
                    StatFunctions.calculateFallDamage((Player) owner, fallDistance, true);

                fallDistance = 0;
                lastFallZ = 0;
            }
        }
    }

    public void stopFalling() {
        fallDistance = 0;
        lastFallZ = 0;
    }

    public void correctPosition() {
        if (!(owner instanceof Player))
            return;

        // PacketSendUtility.sendMessage((Player) owner,
        // String.format("[DBG] Correcting to %.02f, %.02f, %.02f", originX, originY, originZ));

        stop();

        World.getInstance().updatePosition(owner, originX, originY, originZ, owner.getHeading(),
            false);
        setPosition(originX, originY, originZ);

        PacketSendUtility.sendPacket((Player) owner, new SM_POSITION_CORRECTION((Player) owner));
    }

    public boolean isMoving() {
        return isScheduled() && !isStopped;
    }

    public enum MoveDirection {
        NONE(0),
        FORWARD(1),
        BACKWARD(2),
        STRAFE(4);

        private int id;

        private MoveDirection(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }

        public static MoveDirection getMoveDirectionFromId(int id) {
            for (MoveDirection direction : MoveDirection.values())
                if (direction.getId() == id)
                    return direction;

            return MoveDirection.NONE;
        }

        public static List<MoveDirection> getMoveDirections(int id) {
            List<MoveDirection> directions = new ArrayList<MoveDirection>();

            for (MoveDirection direction : MoveDirection.values())
                if (direction != MoveDirection.NONE
                    && (id & direction.getId()) == direction.getId())
                    directions.add(direction);

            if (directions.isEmpty())
                directions.add(MoveDirection.NONE);

            return directions;
        }
    }

    public static class LoggedMove {
        private float x;
        private float y;
        private float z;
        private long time;

        public LoggedMove(float x, float y, float z, long time) {
            this.x = x;
            this.y = y;
            this.z = z;
            this.time = time;
        }

        public float getX() {
            return x;
        }

        public float getY() {
            return y;
        }

        public float getZ() {
            return z;
        }

        public long getTime() {
            return time;
        }
    }
}
