/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class HopesDemiseEvent extends MobEvent {
    public HopesDemiseEvent() {
        super.mapId = 600010000;
        super.center = new SpawnPosition(1287, 768, 305);
        super.apBasePlayer = 500;
        super.apPoolPerPlayer = 500;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("Hope's Demise in Silentera will house an event in 2 minutes");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at Hope's Demise in Silentera starts in 1 minute", 1 * 60 * 1000);
        announceAll("The event at Hope's Demise in Silentera starts in 30 seconds", 30 * 1000 + 1
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 2 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters in Hope's Demise in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217079, 1289, 822, 303);
        spawnMob(217079, 1280, 821, 303);
        spawnMob(217079, 1272, 810, 303);
        spawnMob(217079, 1264, 798, 303);
        spawnMob(217079, 1260, 741, 303);
        spawnMob(217079, 1265, 734, 303);
        spawnMob(217079, 1272, 724, 303);
        spawnMob(217079, 1282, 713, 303);
        spawnMob(217079, 1290, 726, 305);
        spawnMob(217079, 1282, 741, 305);
        spawnMob(217079, 1282, 790, 305);
        spawnMob(217079, 1292, 806, 304);
        spawnMob(217079, 1314, 755, 309);
        spawnMob(217079, 1314, 762, 309);
        spawnMob(217079, 1313, 772, 309);
        spawnMob(217079, 1313, 780, 309);
        spawnMob(217079, 1276, 767, 305);
        spawnMob(217079, 1297, 767, 305);
        spawnMob(217079, 1288, 777, 305);
        spawnMob(217079, 1289, 758, 305);
    }
}
