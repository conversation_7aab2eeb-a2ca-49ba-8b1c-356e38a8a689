/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dao;

import gameserver.model.bet.Bet;
import gameserver.model.bet.Event;

import java.util.List;

import com.aionemu.commons.database.dao.DAO;

/**
 * <AUTHOR>
 * 
 */
public abstract class BetDAO implements DAO {

    @Override
    public final String getClassName() {
        return BetDAO.class.getName();
    }

    public abstract Bet getBet(int id);

    public abstract int addBet(Bet bet);

    public abstract boolean deleteBet(int id);

    public abstract boolean changeBet(Bet bet, int id);

    public abstract List<Integer> getBetIds();

    public abstract Event getEvent(int id);

    public abstract int addEvent(Event event);

    public abstract boolean deleteEvent(int id);

    public abstract boolean changeEvent(Event event, int id);

    public abstract List<Integer> getEventIds();

}
