/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.dao.InstanceScoreDAO;
import gameserver.dataholders.DataManager;
import gameserver.model.Gender;
import gameserver.model.drop.DropTemplate;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEffectType;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.RateModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.templates.item.EAttackType;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.DropService;
import gameserver.services.GloryService;
import gameserver.services.ItemService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;
import gameserver.world.World;
import gameserver.world.WorldPosition;

import java.util.Arrays;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class SlipperySlope extends Battleground {
    private boolean endCalled = false;

    private AtomicBoolean hardMode = new AtomicBoolean(false);

    private static final int INSTANCE_ID = 1002;

    private static final int INITIAL_DELAY = 15000;

    private AtomicInteger objectivesCompleted = new AtomicInteger(0);
    private AtomicBoolean completed = new AtomicBoolean(false);
    private AtomicBoolean completedPuzzle = new AtomicBoolean(false);
    private AtomicInteger score = new AtomicInteger(0);

    private WorldPosition savedPosition = null;

    private int finishSeconds = 0;

    public SlipperySlope() {
        super.name = "Slippery Slope";
        super.displayName = "Instance";
        super.minSize = 1;
        super.maxSize = 1;
        super.teamCount = 1;
        super.matchLength = INITIAL_DELAY / 1000 + 25 * 60;
        super.isAnonymous = false;
        super.afkKick = false;
        super.shouldDisband = false;
        super.isPvE = true;

        BattlegroundMap map1 = new BattlegroundMap(600020000);
        map1.addSpawn(new SpawnPosition(981, 2696, 371));
        map1.setKillZ(360f);
        map1.setHighestZ(490f);

        BattlegroundMap map2 = new BattlegroundMap(301130000);
        map2.addSpawn(new SpawnPosition(640, 176, 196));
        map2.setKillZ(180f);

        BattlegroundMap map3 = new BattlegroundMap(300270000);
        map3.addSpawn(new SpawnPosition(955, 1230, 54));
        map3.setKillZ(40f);

        map3.addStaticDoor(76); // first door
        map3.addStaticDoor(15); // second door, to first room
        map3.addStaticDoor(64); // elevator door
        map3.addStaticDoor(26); // foundry door
        map3.addStaticDoor(158); // door to 3rd boss
        map3.addStaticDoor(10); // second door to 3rd boss

        super.maps.add(map1);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueSolo(players);

        if (super.getPlayers().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        synchronized (super.getPlayers()) {
            for (Player pl : super.getPlayers()) {
                super.preparePlayerInstance(pl, INITIAL_DELAY);

                SpawnPosition pos = getSpawnPositions().get(pl.getBgIndex());
                performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
            }
        }

        createSpawns();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                intro();
            }
        }, INITIAL_DELAY);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endInstance(false);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public void onStatRecomputation(Player player) {
        super.onStatRecomputation(player);

        if (player.isSpectating())
            return;

        switch (player.getPlayerClass()) {
            case CLERIC:
            case BARD:
                player.getGameStats().setStat(StatEnum.BOOST_HEAL,
                    player.getGameStats().getStatBonus(StatEnum.BOOST_HEAL) - 150, true);
                break;
            case RANGER:
                player.getGameStats().setStat(StatEnum.MAIN_HAND_POWER,
                    player.getGameStats().getStatBonus(StatEnum.MAIN_HAND_POWER) + 300, true);
                break;
        }
    }

    private void intro() {
        final Npc intro = spawnNpc(800966, 981.4f, 2691.4f, 371f, 29, true);

        intro.shout("Welcome to the Slippery Slope!", 500);
        intro.shout("It's just you and me here.", 4500);

        intro.walkTo(980.9f, 2643.5f, 367.21362f, 2000);

        intro.shout("You must vanquish the giants that roam these halls.", 8500);
        intro
            .shout("But try to explore a bit - I've heard of powerful magic in this place!", 14000);
        intro
            .shout(
                "If you find this magic, perhaps it will make you strong enough to take on the giants.",
                19500);
        intro.shout("Of course, the bold and brave will be rewarded plentiful.", 25000);

        String gender = "lad";
        for (Player pl : super.getPlayers())
            if (pl.getCommonData().getGender() == Gender.FEMALE)
                gender = "lass";

        intro.shout("This is it, courageous " + gender + "!", 30500);
        intro.shout("I bid you the best of luck.", 38000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                intro.getController().onDelete();
            }
        }, 42000);
    }

    private void outro() {
        addTimeScore();

        for (Player pl : super.getPlayers())
            GloryService.getInstance().rewardSlippery(pl, score.get() / 20);

        final Npc outro = spawnNpc(800966, 614.1f, 2721.0f, 468f, 85, true);

        spawnNpc(205510, 591.0f, 2627.5f, 472.0086f, 41, false, true);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                getMap().setKillZ(454f);

                for (Player pl : getPlayers()) {
                    switch (objectivesCompleted.get()) {
                        case 4:
                            rewardItem(pl, 166020000, hardMode.get() ? 20 : 7);
                            break;
                        case 3:
                            rewardItem(pl, 166020000, hardMode.get() ? 10 : 4);
                            break;
                        case 2:
                            rewardItem(pl, 166020000, hardMode.get() ? 5 : 3);
                            break;
                        case 1:
                            break;
                    }

                    if (objectivesCompleted.get() < 2) {
                        scheduleAnnouncement(pl, "Slippery Slope",
                            "You didn't complete enough objective to get a bonus reward.", 5000);
                        continue;
                    }
                }
            }
        }, 6000);

        announceAll("Slippery Slope", "You completed " + objectivesCompleted.get()
            + " out of 4 objectives with the score " + score.get() + "!", 3000);

        outro.shout("You made it my dear!", 4000);
        outro.shout("This is truly a magnificent place.", 8000);
        outro.shout("Do you see those flying objects in the distance?", 12000);
        outro.shout("I think it's a challenge for the extra bold.", 16000);

        outro.walkTo(587.91f, 2732.33f, 467.70892f, 17000);

        outro.shout("Follow this ledge and glide off the edge.", 22000);
        outro.shout("May the Gods be with you.", 28000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                outro.getController().onDelete();
            }
        }, 32000);
    }

    private void createSpawns() {
        // Spawn Room
        spawnNpc(281844, 851.9f, 2592.8f, 373f, 0, true); // Gravekeeper Basaim

        spawnNpc(215838, 913.5f, 2588.6f, 368f, 41, true);
        spawnNpc(215838, 845.9f, 2661.5f, 368f, 111, true);
        spawnNpc(215838, 847.6f, 2511.4f, 368f, 25, true);
        spawnNpc(215838, 780.7f, 2589.8f, 368f, 51, true);
        spawnNpc(215838, 724.8f, 2614.5f, 368f, 107, true);

        spawnNpc(282637, 920.6f, 2450.5f, 368f, 52, true);
        spawnNpc(282637, 946.6f, 2491.2f, 368f, 79, true);
        spawnNpc(282637, 962.9f, 2465.7f, 368f, 67, true);

        spawnNpc(700568, 975.8f, 2438.3f, 369f, 44, true); // Jotun Relics

        spawnNpc(282637, 811.6f, 2747.4f, 368f, 77, true);
        spawnNpc(282637, 853.9f, 2781.4f, 369f, 65, true);
        spawnNpc(282637, 848.6f, 2811.4f, 368f, 74, true);

        spawnNpc(701682, 839.4f, 2828.5f, 371f, 91, true); // Frozen Novun

        // North Room
        spawnNpc(215794, 310.6f, 2660.1f, 420f, 119, true); // Anvilface

        spawnNpc(215838, 420.6f, 2654.5f, 417f, 91, true);
        spawnNpc(215838, 376.0f, 2655.5f, 417f, 32, true);
        spawnNpc(215838, 306.9f, 2730.2f, 417f, 113, true);
        spawnNpc(215838, 310.7f, 2794.5f, 416f, 86, true);
        spawnNpc(215838, 235.3f, 2664.1f, 417f, 104, true);
        spawnNpc(215838, 188.5f, 2654.2f, 416f, 43, true);
        spawnNpc(215838, 312.2f, 2584.7f, 417f, 27, true);
        spawnNpc(215838, 338.9f, 2539.1f, 416f, 6, true);

        spawnNpc(282637, 311.2f, 2476.5f, 418f, 0, true);
        spawnNpc(282637, 289.7f, 2435.5f, 417f, 5, true);
        spawnNpc(282637, 384.4f, 2442.5f, 417f, 50, true);

        spawnNpc(700568, 349.7f, 2416.6f, 417f, 34, true); // Jotun Relics

        spawnNpc(282637, 146.8f, 2559.3f, 417f, 112, true);
        spawnNpc(282637, 183.6f, 2527.9f, 417f, 35, true);
        spawnNpc(282637, 217.6f, 2591.8f, 417f, 68, true);

        spawnNpc(701682, 222.2f, 2524.6f, 417f, 35, true); // Frozen Novun

        spawnNpc(282637, 249.7f, 2750.8f, 419f, 45, true);
        spawnNpc(282637, 173.9f, 2826.6f, 419f, 102, true);
        spawnNpc(282637, 196.6f, 2766.3f, 417f, 7, true);

        spawnNpc(700568, 233.4f, 2816.3f, 417f, 79, true); // Jotun Relics

        // High Room
        spawnNpc(215796, 576.6f, 2334.1f, 435f, 29, true); // Gradarim the Collector

        spawnNpc(215838, 584.0f, 2432.2f, 432f, 35, true);
        spawnNpc(215838, 500.5f, 2340.2f, 432f, 108, true);
        spawnNpc(215838, 442.0f, 2315.0f, 431f, 9, true);
        spawnNpc(215838, 580.3f, 2274.0f, 432f, 39, true);
        spawnNpc(215838, 584.0f, 2432.2f, 432f, 35, true);
        spawnNpc(215838, 572.0f, 2225.1f, 432f, 17, true);
        spawnNpc(215838, 663.1f, 2340.6f, 432f, 79, true);
        spawnNpc(215838, 687.0f, 2372.5f, 431f, 97, true);

        spawnNpc(282637, 801.1f, 2425.2f, 432f, 81, true);
        spawnNpc(282637, 750.8f, 2332.3f, 433f, 31, true);
        spawnNpc(282637, 823.3f, 2367.0f, 432f, 58, true);

        spawnNpc(701682, 803.9f, 2378.8f, 434f, 50, true); // Frozen Novun

        spawnNpc(282637, 708.1f, 2177.1f, 432f, 47, true);
        spawnNpc(282637, 683.9f, 2213.6f, 434f, 73, true);
        spawnNpc(282637, 653.0f, 2240.6f, 433f, 82, true);

        spawnNpc(700568, 645.6f, 2170.5f, 433f, 20, true); // Jotun Relics

        spawnNpc(282637, 483.9f, 2274.9f, 434f, 74, true);
        spawnNpc(282637, 413.9f, 2249.6f, 432f, 114, true);
        spawnNpc(282637, 412.9f, 2200.9f, 434f, 116, true);

        spawnNpc(701682, 470.5f, 2222.7f, 432f, 45, true); // Frozen Novun

        // Throne Room
        spawnNpc(281419, 672.8f, 2933.5f, 408f, 81, true); // Debilkarim the Maker (small)

        spawnNpc(215838, 653.2f, 2799.7f, 428f, 42, true);
        spawnNpc(215838, 650.7f, 2835.0f, 418f, 105, true);
        spawnNpc(215838, 657.9f, 2874.2f, 410f, 73, true);

        spawnNpc(700568, 687.4f, 2889.3f, 408f, 7, true); // Jotun Relics
        spawnNpc(701682, 631.1f, 2913.9f, 408f, 59, true); // Frozen Novun

        // Jumping Puzzle
        spawn(3190027, 2992, 1695.59, 202.126, 71, false);
        spawn(3190027, 2988.48, 1693.14, 203.955, 38, false);
        spawn(3190027, 2984.61, 1691.17, 205.87, 39, false);
        spawn(3190027, 2980.76, 1689.76, 207.564, 41, false);
        spawn(3190027, 2977.7, 1686.96, 209.408, 45, false);
        spawn(3190027, 2978.69, 1682.92, 211.15, 67, false);
        spawn(3141000, 2985.99, 1681.51, 213.646, 3, false);
        spawn(3141000, 2992.73, 1684.91, 215.048, 75, false);
        spawn(3141000, 2991.68, 1689.3, 216.39, 5, false);
        spawn(3141000, 2989.66, 1693.32, 217.431, 10, false);
        spawn(3141000, 2985.14, 1695.59, 218.423, 89, false);
        spawn(3200005, 2989.89, 1703.45, 217.924, 59, false);
        spawn(3200005, 2993.97, 1708.69, 218.288, 108, false);
        spawn(3200005, 3001.16, 1705.73, 217.898, 54, false);
        spawn(3200005, 3005.01, 1702.03, 219.255, 72, false);
        spawn(3200005, 3005.35, 1697.49, 220.786, 51, false);
        spawn(3200005, 3006.26, 1693.42, 221.66, 68, false);
        spawn(3420010, 3006.63, 1685.62, 221.602, 63, false);
        spawn(3420046, 514.924, 2702.18, 467.494, 91, false);
        spawn(3420046, 515.36, 2696.97, 468.09, 36, false);
        spawn(3420046, 515.92, 2691.06, 468.75, 33, false);
        spawn(3420046, 521.819, 2690.8, 469.791, 33, false);
        spawn(3420046, 526.585, 2694.78, 470.521, 45, false);
        spawn(3420046, 528.38, 2701.17, 471.521, 58, false);
        spawn(3420008, 538.714, 2705.89, 470.94, 64, false);
        spawn(3420008, 546.422, 2703.3, 471.619, 112, false);
        spawn(3420008, 550.504, 2697.81, 472.566, 37, false);
        spawn(3420008, 552.985, 2691.76, 473.132, 35, false);
        spawn(3420008, 552.084, 2686.38, 473.985, 21, false);
        spawn(3190027, 551.588, 2680.35, 474.593, 45, false);
        spawn(3190027, 554.202, 2674.95, 474.72, 38, false);
        spawn(3190027, 556.148, 2667.99, 475.434, 33, false);
        spawn(3160008, 561.37, 2661.81, 474.288, 104, false);
        spawn(3160008, 568.46, 2654.39, 474.023, 104, false);
        spawn(3160008, 575.949, 2646.31, 471.484, 103, false);
        spawn(3160008, 582.198, 2639.39, 472.44, 102, false);
        spawn(3420010, 590.502, 2628.13, 471.172, 41, false);
        spawn(3200025, 588.585, 2631.16, 471.109, 42, false);
        spawn(3141000, 533.338, 2711.97, 459.407, 56, false);
        spawn(3141000, 534.178, 2714.11, 460.692, 57, false);
        spawn(3141000, 534.702, 2717, 462.194, 60, false);
        spawn(3141000, 533.068, 2719.2, 463.033, 67, false);
    }

    @Override
    public boolean onTalk(Npc npc, Player player) {
        switch (npc.getNpcId()) {
            case 205506: // North Teleport
                npc.shout("Nyerk!");
                TeleportService.teleportTo(player, getMapId(), getInstanceId(), 499f, 2643f, 414f,
                    (byte) 60, TeleportService.TELEPORT_BEAM_DELAY);
                return true;
            case 205507: // High Teleport
                npc.shout("Kekekekeke!");
                TeleportService.teleportTo(player, getMapId(), getInstanceId(), 596f, 2544f, 427f,
                    (byte) 89, TeleportService.TELEPORT_BEAM_DELAY);
                return true;
            case 205508: // Throne Teleport
                npc.shout("NYYYEERK!");
                TeleportService.teleportTo(player, getMapId(), getInstanceId(), 610f, 2721f, 427f,
                    (byte) 21, TeleportService.TELEPORT_BEAM_DELAY);
                return true;
            case 205509: // Final Teleport
                if (!completed.compareAndSet(false, true)) // Only-once
                    return true;

                npc.shout("KEKEKEKEKEKE!");
                TeleportService.teleportTo(player, getMapId(), getInstanceId(), 617f, 2723f, 468f,
                    (byte) 82, TeleportService.TELEPORT_BEAM_DELAY);
                outro();
                return true;
            case 205510: // Jumping Puzzle
                if (!completedPuzzle.compareAndSet(false, true)) // Only-once
                    return true;
                else if (!completed.get()) // Can't do this without having finished instance
                    return true;

                npc.shout("KEKENYYYYYEEERK!");
                npc.getController().onDelete();
                completePuzzle();
                return true;
            case 700568: // Jotun Relics
                SkillTemplate buffRelics = DataManager.SKILL_DATA.getSkillTemplate(19786);

                if (buffRelics != null) {
                    Effect effect = new Effect(player, player, buffRelics, 1, 140 * 1000);
                    player.getEffectController().addEffect(effect);
                    effect.addAllEffectToSucess();
                    effect.startEffect(true);

                    player.getEffectController().updatePlayerEffectIcons();
                }

                player.getLifeStats().increaseHp(TYPE.HP, 7500);
                player.getLifeStats().increaseMp(TYPE.MP, 7500);

                npc.shout("You have discovered some powerful magic!");
                npc.getController().onDelete();

                scorePoints(-50);

                return true;
            case 701682: // Frozen Novun
                SkillTemplate buffFrozen = DataManager.SKILL_DATA.getSkillTemplate(20490);

                if (buffFrozen != null) {
                    Effect effect = new Effect(player, player, buffFrozen, 1, 140 * 1000);
                    player.getEffectController().addEffect(effect);
                    effect.addAllEffectToSucess();
                    effect.startEffect(true);

                    player.getEffectController().updatePlayerEffectIcons();
                }

                player.getLifeStats().increaseHp(TYPE.HP, 7500);
                player.getLifeStats().increaseMp(TYPE.MP, 7500);

                npc.shout("You have discovered some powerful magic!");
                npc.getController().onDelete();

                scorePoints(-50);

                return true;
        }

        return false;
    }

    private void completeObjective(boolean end) {
        if (end) {
            super.announceAll("Slippery Slope", "You have completed the final objective!");
            scorePoints(300);
        }
        else {
            super.announceAll("Slippery Slope", "You have completed an objective!");
            scorePoints(250);
        }

        objectivesCompleted.incrementAndGet();

        int might = hardMode.get() ? 50 : 20;
        int dp = hardMode.get() ? 2000 : 1000;

        for (Player pl : super.getPlayers()) {
            PvpService.getInstance().addMight(pl, might);
            pl.getCommonData().addDp(dp);
        }
    }

    private void completePuzzle() {
        super.announceAll("Slippery Slope",
            "You have completed the Jumping Puzzle bonus objective!");
        super
            .announceAll("Slippery Slope",
                "You have received three [item:166020000] - that deserves a pat on the shoulder.",
                5000);

        int might = hardMode.get() ? 100 : 50;

        for (Player pl : super.getPlayers()) {
            PvpService.getInstance().addMight(pl, might);
            rewardItem(pl, 166020000, 3);
        }

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endInstance(false);
            }
        }, 6000);
    }

    private void rewardItem(Player player, int itemId, long amount) {
        ItemService.addItem(player, itemId, amount);
        PacketSendUtility.sendMessage(player, "You have received " + amount + " x [item:" + itemId
            + "].");
    }

    @Override
    public void onKill(Npc npc, Creature lastAttacker) {
        if (lastAttacker == null && super.getPlayers().size() > 0) {
            lastAttacker = super.getPlayers().get(0);
        }

        if (!(lastAttacker.getMaster() instanceof Player))
            return;

        Player killer = (Player) lastAttacker.getMaster();

        switch (npc.getNpcId()) {
            case 281844: // Gravekeeper Basaim
                spawnNpc(205506, 612.71f, 2616.01f, 365f, 115, false)
                    .setCustomTag("North Teleport");
                completeObjective(false);
                break;
            case 215794: // Anvilface
                spawnNpc(205507, 487.45f, 2635.81f, 414f, 13, false).setCustomTag("High Teleport");
                completeObjective(false);
                break;
            case 215796: // Gradarim the Collector
                spawnNpc(205508, 584.47f, 2514.23f, 429f, 6, false).setCustomTag("Throne Teleport");
                completeObjective(false);
                break;
            case 281419: // Debilkarim the Maker (small)
                spawnNpc(283134, npc.getX(), npc.getY(), npc.getZ(), npc.getHeading(), true)
                    .delete(5000); // Dust storm at small

                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        spawnNpc(215795, 686.1f, 2963.2f, 408f, 81, true).shout("Now I'm mad!",
                            1000); // Debilkarim the Maker
                    }
                }, 3000);

                spawnNpc(283137, 665.5f, 2952.1f, 408f, 103, true).delete(5000); // Debris
                spawnNpc(283137, 691.0f, 2942.0f, 408f, 68, true).delete(5000); // Debris
                spawnNpc(283081, 678.1f, 2947.5f, 408f, 81, true).delete(5000); // Earthquake
                break;
            case 215795: // Debilkarim the Maker
                npc.shout("AARGGH!!!");

                spawnNpc(205509, 620.88f, 2731.10f, 427f, 64, false).setCustomTag("Final Teleport");
                spawnNpc(205509, 690.33f, 2945.38f, 408f, 76, false).setCustomTag("Final Teleport");
                completeObjective(true);
                break;
            case 282469: // Protection of Aion
                break;
            default:
                scorePoints(25);

                int might = hardMode.get() ? 10 : 5;

                PvpService.getInstance().addMight(killer, might);
                break;
        }

        killer.getLifeStats().increaseHp(TYPE.HP, 2000);
        killer.getLifeStats().increaseMp(TYPE.MP, 2000);

        if (!DropService.getInstance().hasDrops(npc)) {
            npc.getController().onDelete();
        }
    }

    private void registerDrops(Npc npc, DropTemplate... drops) {
        DropService.getInstance().registerDrop(npc, Arrays.asList(drops), true);
    }

    private void alterStats(Npc npc) {
        if (hardMode.get()) {
            TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

            mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 40, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 800, true));
            mods.add(AddModifier.newInstance(StatEnum.MAGICAL_RESIST, 700, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL_RESIST, 800, true));
            mods.add(AddModifier.newInstance(StatEnum.EVASION, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 2500, true));

            mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_CRITICAL_RESIST, 400, true));
            mods.add(AddModifier.newInstance(StatEnum.MAGICAL_CRITICAL_RESIST, 200, true));

            mods.add(AddModifier.newInstance(StatEnum.ABNORMAL_RESISTANCE_ALL, -2000, true));
            mods.add(AddModifier.newInstance(StatEnum.ROOT_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SNARE_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SILENCE_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SLEEP_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.OPENAREIAL_RESISTANCE, 1000, true));

            switch (npc.getNpcId()) {
                case 281844: // Gravekeeper Basaim
                    npc.setAttackType(EAttackType.MAGICAL_FIRE);
                    mods.add(AddModifier.newInstance(StatEnum.KNOWLEDGE, 250, true));
                    break;
                case 215794: // Anvilface
                    break;
                case 215796: // Gradarim the Collector
                    break;
                case 281419: // Debilkarim the Maker (small)
                    break;
                case 215795: // Debilkarim the Maker
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_CRITICAL_RESIST, 500, true));
                    mods.add(AddModifier.newInstance(StatEnum.MAGICAL_CRITICAL_RESIST, 500, true));
                    break;
                case 217040: // Jotun Artificer
                    mods.add(AddModifier.newInstance(StatEnum.SLEEP_RESISTANCE, -1000, true));
                    mods.add(AddModifier.newInstance(StatEnum.ROOT_RESISTANCE, -1000, true));
                    break;
                case 282469: // Protection of Aion
                    npc.setAttackType(EAttackType.MAGICAL_WIND);
                    mods.add(AddModifier.newInstance(StatEnum.KNOWLEDGE, 50, true));
                    break;
            }

            npc.getGameStats().endEffect(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT));
            npc.getGameStats().addModifiers(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT), mods);
        }
        else {
            TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

            mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 15, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 400, true));
            mods.add(AddModifier.newInstance(StatEnum.MAGICAL_RESIST, 250, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL_RESIST, 300, true));
            mods.add(AddModifier.newInstance(StatEnum.EVASION, 600, true));

            mods.add(AddModifier.newInstance(StatEnum.ABNORMAL_RESISTANCE_ALL, -2000, true));
            mods.add(AddModifier.newInstance(StatEnum.ROOT_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SILENCE_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SLEEP_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.OPENAREIAL_RESISTANCE, 1000, true));

            switch (npc.getNpcId()) {
                case 281844: // Gravekeeper Basaim
                    npc.setAttackType(EAttackType.MAGICAL_FIRE);
                    mods.add(AddModifier.newInstance(StatEnum.KNOWLEDGE, 175, true));
                    mods.add(AddModifier.newInstance(StatEnum.SNARE_RESISTANCE, 1000, true));
                    break;
                case 215794: // Anvilface
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 2500, true));
                    mods.add(AddModifier.newInstance(StatEnum.MAGICAL_RESIST, 500, true));
                    mods.add(AddModifier.newInstance(StatEnum.FIRE_RESISTANCE, -200, true));
                    mods.add(AddModifier.newInstance(StatEnum.EARTH_RESISTANCE, -200, true));
                    mods.add(AddModifier.newInstance(StatEnum.WIND_RESISTANCE, -200, true));
                    mods.add(AddModifier.newInstance(StatEnum.WATER_RESISTANCE, -200, true));
                    mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, -15, true));
                    break;
                case 215796: // Gradarim the Collector
                    mods.add(AddModifier.newInstance(StatEnum.EVASION, 600, true));
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, -1000, true));
                    break;
                case 281419: // Debilkarim the Maker (small)
                    break;
                case 215795: // Debilkarim the Maker
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_CRITICAL_RESIST, 500, true));
                    mods.add(AddModifier.newInstance(StatEnum.MAGICAL_CRITICAL_RESIST, 500, true));
                    break;
                case 217040: // Jotun Artificer
                    mods.add(AddModifier.newInstance(StatEnum.SLEEP_RESISTANCE, -1000, true));
                    mods.add(AddModifier.newInstance(StatEnum.ROOT_RESISTANCE, -1000, true));
                    break;
                case 282469: // Protection of Aion
                    npc.setAttackType(EAttackType.MAGICAL_WIND);
                    mods.add(AddModifier.newInstance(StatEnum.KNOWLEDGE, 50, true));
                    break;
            }

            npc.getGameStats().endEffect(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT));
            npc.getGameStats().addModifiers(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT), mods);
        }
    }

    private void scorePoints(int points) {
        scorePoints(points, false);
    }

    private void scorePoints(final int points, final boolean time) {
        final int currentScore = score.addAndGet(points);

        getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                if (time) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_GET_SCORE("Time Bonus", points));
                }
                else {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_GET_SCORE_FOR_ENEMY(points));
                }

                if (currentScore - points < 2500 && currentScore >= 2500) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(2500));
                }
                else if (currentScore - points < 2250 && currentScore >= 2250) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(2250));
                }
                else if (currentScore - points < 2000 && currentScore >= 2000) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(2000));
                }
                else if (currentScore - points < 1750 && currentScore >= 1750) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(1750));
                }
                else if (currentScore - points < 1500 && currentScore >= 1500) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(1500));
                }
                else if (currentScore - points < 1250 && currentScore >= 1250) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(1250));
                }
                else if (currentScore - points < 1000 && currentScore >= 1000) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(1000));
                }
                else if (currentScore - points < 750 && currentScore >= 750) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(750));
                }
                else if (currentScore - points < 500 && currentScore >= 500) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(500));
                }

                return true;
            }
        });
    }

    private void addTimeScore() {
        int seconds = getSecondsLeft();

        finishSeconds = seconds;

        int points = (int) Math.max(Math.log(seconds / 60d) * 200, 0);

        scorePoints(points, true);
    }

    private Npc spawnNpc(int npcId, SpawnPosition pos) {
        return spawnNpc(npcId, pos.getX(), pos.getY(), pos.getZ(), pos.getHeading(), true, true);
    }

    private Npc spawnNpc(int npcId, SpawnPosition pos, boolean noHome) {
        return spawnNpc(npcId, pos.getX(), pos.getY(), pos.getZ(), pos.getHeading(), true, noHome);
    }

    private Npc spawnNpc(int npcId, float x, float y, float z, int heading, boolean noHome) {
        return spawnNpc(npcId, x, y, z, heading, true, noHome);
    }

    private Npc spawnNpc(int npcId, float x, float y, float z, int heading, boolean geo,
        boolean noHome) {
        Npc npc = (Npc) spawn(npcId, x, y, z, heading, geo);
        npc.setNoHome(noHome);

        alterStats(npc);

        return npc;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        if (completed.get()) {
            super.announceAll("Slippery Slope", "Better luck on the Puzzle next time!");
        }
        else {
            super.announceAll("Slippery Slope", "You have failed your mission!");
        }

        endInstance(false);
    }

    public void onLeave(final Player player, boolean isLogout, boolean isAfk) {
        if (!player.isSpectating()) {
            if (completed.get()) {
                endInstance(false);
            }
            else {
                log.info("[DEBUG] Slippery Slope: " + player.getName()
                    + " disconnected from instance " + super.getInstanceId() + " with "
                    + super.getSecondsLeft() + " seconds left.");

                savedPosition = player.getPosition().clone();
            }
        }

        super.onLeaveDefault(player, true, isAfk);
    }

    public void onReconnect(Player player) {
        log.info("[DEBUG] Slippery Slope: " + player.getName() + " reconnected to instance "
            + super.getInstanceId() + " with " + super.getSecondsLeft() + " seconds left.");

        if (savedPosition != null)
            World.getInstance().setPosition(player, getMapId(), getInstanceId(),
                savedPosition.getX(), savedPosition.getY(), savedPosition.getZ(),
                savedPosition.getHeading());
    }

    public void recordScore() {
        if (!completed.get())
            return;

        for (Player pl : super.getPlayers()) {
            log.info("[DEBUG] Slippery Slope: " + pl.getName() + " finished instance "
                + super.getInstanceId() + ". Objectives: " + objectivesCompleted.get()
                + ", Score: " + score.get() + ", Puzzle: " + (completedPuzzle.get() ? "Yes" : "No"));

            DAOManager.getDAO(InstanceScoreDAO.class).insertScore(pl, INSTANCE_ID, score.get(),
                objectivesCompleted.get(), finishSeconds, completedPuzzle.get() ? 1 : 0);
        }
    }

    private void endInstance(boolean timer) {
        if (endCalled)
            return;

        endCalled = true;

        super.onEndFirstDefault();

        super.deleteNpcs();

        recordScore();

        if (!timer) {
            super.onEndDefault();
            return;
        }

        if (completed.get()) {
            super.announceAll("Slippery Slope", "Time's up! That's all for now.");
        }
        else {
            super.announceAll("Slippery Slope", "The time has run out! Better luck next time.");
        }

        onEndDefault();
    }
}