/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dataholders;

import gameserver.model.templates.MountTemplate;
import gnu.trove.TIntObjectHashMap;

import java.util.List;

import javax.xml.bind.Unmarshaller;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 */
@XmlRootElement(name = "mount_datas")
@XmlAccessorType(XmlAccessType.FIELD)
public class MountData {
    @XmlElement(name = "mount")
    private List<MountTemplate> mounts;
    
    private TIntObjectHashMap<MountTemplate> templates;
    
    void afterUnmarshal(Unmarshaller u, Object parent) {
        templates = new TIntObjectHashMap<MountTemplate>();
        
        for (MountTemplate mt : mounts)
            templates.put(mt.getId(), mt);
        
        mounts = null;
    }
    
    public int size() {
        return templates.size();
    }
    
    public MountTemplate getMountTemplate(int mountId) {
        return templates.get(mountId);
    }
}
