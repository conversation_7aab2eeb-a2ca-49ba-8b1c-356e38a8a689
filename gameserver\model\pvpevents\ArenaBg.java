/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.dao.MightDAO;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.GloryService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.utils.MathUtil;
import gameserver.utils.ThreadPoolManager;

import java.util.List;

import com.aionemu.commons.database.dao.DAOManager;
import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class ArenaBg extends Battleground {
    private boolean endCalled = false;

    public ArenaBg() {
        super.name = "Arena";
        super.description = "You are alone and must kill the enemy. Whoever ends up with the most kills in total wins. The map will rotate half-way through the match!";
        super.minSize = 2;
        super.maxSize = 2;
        super.teamCount = 1;
        super.matchLength = 325;

        BattlegroundMap map2 = new BattlegroundMap(300360000);
        map2.addSpawn(new SpawnPosition(1887.1f, 1737.4f, 311.7f));
        map2.addSpawn(new SpawnPosition(1817.5f, 1737.3f, 311.7f));
        map2.addSpawn(new SpawnPosition(1851.9f, 1765.3f, 306f));
        map2.addSpawn(new SpawnPosition(1851.7f, 1709.4f, 306f));
        map2.addStaticDoor(148);
        map2.addStaticDoor(149);
        map2.addStaticDoor(150);
        map2.addStaticDoor(151);
        map2.setKillZ(300f);

        BattlegroundMap map3 = new BattlegroundMap(300360000);
        map3.addSpawn(new SpawnPosition(1868.6f, 1068.6f, 337.5f));
        map3.addSpawn(new SpawnPosition(1841.2f, 1041.2f, 338.3f));
        map3.addSpawn(new SpawnPosition(1842.1f, 1069.2f, 338.1f));
        map3.addSpawn(new SpawnPosition(1869.2f, 1041.9f, 337.9f));
        map3.addStaticDoor(140);
        map3.addStaticDoor(141);
        map3.addStaticDoor(142);
        map3.addStaticDoor(139);
        map3.setKillZ(330f);

        BattlegroundMap map15 = new BattlegroundMap(301140000); // 4.0
        map15.addSpawn(new SpawnPosition(886.0f, 873.0f, 280.0f));
        map15.addSpawn(new SpawnPosition(927.0f, 848.0f, 280.0f));
        map15.addSpawn(new SpawnPosition(921.0f, 882.0f, 279.0f));
        map15.addSpawn(new SpawnPosition(892.0f, 839.0f, 279.0f));
        map15.setKillZ(276f);

        BattlegroundMap map16 = new BattlegroundMap(301230000); // 4.5
        map16.addSpawn(new SpawnPosition(255f, 294f, 322f));
        map16.addSpawn(new SpawnPosition(256f, 216f, 322f));
        map16.addSpawn(new SpawnPosition(295f, 255f, 296f));
        map16.addSpawn(new SpawnPosition(216f, 254f, 296f));
        map16.setKillZ(255f);

        super.maps.add(map2);
        super.maps.add(map3);
        // super.maps.add(map15);
        // super.maps.add(map16);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueSolo(players);

        if (super.getPlayers().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        synchronized (super.getPlayers()) {
            for (Player pl : super.getPlayers()) {
                super.preparePlayer(pl, 25000);

                SpawnPosition pos = getSpawnPositions().get(pl.getBgIndex());
                if (pos != null)
                    performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                else
                    log.error("pos == null!");
            }
        }

        super.specAnnounce("The match has begun!!!", 25000);

        startBattleground(25000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingPlayers() <= 1)
                    endArenaMatch(false);
            }
        });

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                openStaticDoors();

                if (isDone())
                    return;

                setExtraTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        if (isDone())
                            return;

                        createNewInstance();
                        
                        int delay = 0;

                        synchronized (getPlayers()) {
                            for (Player pl : getPlayers()) {
                                if (pl.getLifeStats().isAlreadyDead())
                                    pl.getReviveController().fullRevive();

                                SpawnPosition spawnPos = getSpawnPositions().get(pl.getBgIndex());
                                TeleportService.teleportTo(pl, getMapId(), getInstanceId(),
                                    spawnPos.getX(), spawnPos.getY(), spawnPos.getZ(),
                                    TeleportService.TELEPORT_BEAM_DELAY + delay);
                                
                                delay += 50;
                            }
                        }

                        synchronized (getSpectators()) {
                            for (Player pl : getSpectators()) {
                                SpawnPosition spawnPos = getSpawnPositions().get(
                                    Rnd.get(getSpawnPositions().size()));
                                TeleportService.teleportTo(pl, getMapId(), getInstanceId(),
                                    spawnPos.getX(), spawnPos.getY(), spawnPos.getZ(),
                                    TeleportService.TELEPORT_BEAM_DELAY + delay);
                                
                                delay += 50;
                            }
                        }

                        ThreadPoolManager.getInstance().schedule(new Runnable() {
                            @Override
                            public void run() {
                                synchronized (getPlayers()) {
                                    for (Player pl : getPlayers()) {
                                        if (pl.getLifeStats().isAlreadyDead())
                                            pl.getReviveController().fullRevive();

                                        SpawnPosition spawnPos = getSpawnPositions().get(
                                            pl.getBgIndex());

                                        if (MathUtil.getDistance(pl, spawnPos.getX(),
                                            spawnPos.getY(), spawnPos.getZ()) > 5)
                                            TeleportService.teleportTo(pl, getMapId(),
                                                getInstanceId(), spawnPos.getX(), spawnPos.getY(),
                                                spawnPos.getZ(),
                                                TeleportService.TELEPORT_BEAM_DELAY);
                                    }
                                }
                            }
                        }, 3500);

                        ThreadPoolManager.getInstance().schedule(new Runnable() {
                            @Override
                            public void run() {
                                openStaticDoors();
                                announceAll("The map has been rotated!");
                            }
                        }, 5000);
                    }
                }, (getMatchLength() - 25) * 1000 / 2));
            }
        }, 25 * 1000);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endArenaMatch(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public boolean createTournament(List<List<Player>> players) {
        if (!super.createPlayers(players))
            return false;

        this.matchLength = 595;

        startMatch();

        return true;
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        if (is1v1() && getPlayers().size() == 2) { // always make sure someone gets the kill
            lastAttacker = getPlayers().get(player.getBgIndex() == 0 ? 1 : 0);
        }

        super.onDieDefault(player, lastAttacker);

        player.getEffectController().removeAllNonItemEffects();
        player.getEffectController().removeRobotEffects();

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;

            announceAll(killer.getName() + " has slain " + player.getName());

            PvpService.getInstance().addMight(killer, is1v1() ? 4 : 3);

            if (killer.getTotalKills() - player.getTotalKills() >= 5)
                endArenaMatch(false);
        }

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (player.getBattleground() != null && player.getLifeStats().isAlreadyDead()) {
                    player.getReviveController().fullRevive();
                    spawnProtection(player, 3000);

                    SpawnPosition spawnPos = getSpawnPositions().get(
                        Rnd.get(getSpawnPositions().size()));
                    TeleportService.teleportTo(player, getMapId(), getInstanceId(),
                        spawnPos.getX(), spawnPos.getY(), spawnPos.getZ(), 0);
                }
            }
        }, 6000);
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (is1v1() && !player.getController().isInShutdownProgress())
            DAOManager.getDAO(MightDAO.class).addMight(player, -25);

        if (isStarted() && getRemainingPlayers() <= 1)
            endArenaMatch(false);
    }

    private Player getWinner() {
        Player winner = null;
        int drawPoints = 0;

        for (Player pl : super.getPlayers()) {
            if (winner == null && pl.getTotalKills() > drawPoints) {
                winner = pl;
            }
            else if (winner == null) {
                continue;
            }
            else if (winner.getTotalKills() < pl.getTotalKills()) {
                winner = pl;
            }
            else if (winner.getTotalKills() == pl.getTotalKills()) {
                drawPoints = winner.getTotalKills();
                winner = null;
            }
        }

        return winner;
    }

    private void endArenaMatch(boolean isDraw) {
        if (endCalled)
            return;

        endCalled = true;

        for (Player pl : super.getPlayers())
            super.freezeNoEnd(pl);

        super.onEndFirstDefault();

        Player winner = getWinner();
        if (winner == null) {
            super.announceAll("The match was a draw! Better luck next time.");

            for (Player pl : super.getPlayers()) {
                if (is1v1()) {
                    PvpService.getInstance().addMight(pl, 20);
                    GloryService.getInstance().rewardOneVsOne(pl, false);
                }
                else {
                    super.rewardPlayer(pl, 15, false);
                }
            }
        }
        else {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            for (Player pl : super.getPlayers()) {
                if (pl.getObjectId() == winner.getObjectId()) {
                    super.playerWinMatch(pl, super.K_VALUE / 2);
                    super.scheduleAnnouncement(pl,
                        "You have won the match with " + winner.getTotalKills() + " kills!", 0);
                    super.scheduleAnnouncement(pl, "You have been rewarded with might "
                        + (is1v1() ? "for your effort!" : "and rating for your great effort!"),
                        3000);

                    if (is1v1()) {
                        PvpService.getInstance().addMight(pl, 30);
                        GloryService.getInstance().rewardOneVsOne(pl, true);
                    }
                    else {
                        super.rewardPlayer(pl, 20, true);
                    }
                }
                else {
                    super.playerLoseMatch(pl, -super.K_VALUE / 3);
                    super.scheduleAnnouncement(pl, winner.getName() + " has won the match with "
                        + winner.getTotalKills() + " to your " + pl.getTotalKills() + " kills!", 0);
                    super.scheduleAnnouncement(pl, "You have received some might"
                        + (is1v1() ? "." : " but lost rating."), 3000);

                    if (is1v1()) {
                        PvpService.getInstance().addMight(pl, 15);
                        GloryService.getInstance().rewardOneVsOne(pl, false);
                    }
                    else {
                        super.rewardPlayer(pl, 10, false);
                    }
                }
            }

            super.specAnnounce(winner.getName() + " has won the match with "
                + winner.getTotalKills() + " kills!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}