/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects;

import gameserver.controllers.DecorationObjectController;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.world.WorldPosition;

/**
 * <AUTHOR>
 */
public class DecorationObject extends VisibleObject {
    private int templateId;
    
    public DecorationObject(int objectId, DecorationObjectController controller,
        SpawnTemplate spawnTemplate, int templateId) {
        super(objectId, controller, spawnTemplate, null, new WorldPosition());
        this.templateId = templateId;
        controller.setOwner(this);
    }
    
    public int getTemplateId() {
        return templateId;
    }

    @Override
    public String getName() {
        return "DecorationObject-" + templateId;
    }
}
