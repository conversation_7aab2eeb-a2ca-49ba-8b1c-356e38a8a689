/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects;

import gameserver.controllers.GatherableController;
import gameserver.model.templates.GatherableTemplate;
import gameserver.model.templates.VisibleObjectTemplate;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.world.WorldPosition;

/**
 * <AUTHOR>
 */
public class Gatherable extends VisibleObject {

    public Gatherable(SpawnTemplate spawnTemplate, VisibleObjectTemplate objectTemplate, int objId,
        GatherableController controller) {
        super(objId, controller, spawnTemplate, objectTemplate, new WorldPosition());
        
        if (controller != null)
            controller.setOwner(this);
    }

    @Override
    public String getName() {
        return objectTemplate != null ? objectTemplate.getName() : "gatherable";
    }

    @Override
    public GatherableTemplate getObjectTemplate() {
        return (GatherableTemplate) objectTemplate;
    }

    @Override
    public GatherableController getController() {
        return (GatherableController) super.getController();
    }
}