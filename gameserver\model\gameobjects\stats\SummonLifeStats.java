/*
 * This file is part of aion-unique <aion-unique.org>.
 *
 *  aion-unique is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-unique is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-unique.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.stats;

import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.Summon.SummonMode;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_SUMMON_UPDATE;
import gameserver.utils.PacketSendUtility;

/**
 * <AUTHOR>
 * 
 */
public class SummonLifeStats extends CreatureLifeStats<Summon> {

    public SummonLifeStats(Summon owner) {
        super(owner, owner.getGameStats().getCurrentStat(StatEnum.MAXHP), owner.getGameStats()
            .getCurrentStat(StatEnum.MAXMP));
    }

    @Override
    protected void onIncreaseHp(TYPE type, int value, int skillId, int logId) {
        sendAttackStatusPacketUpdate(type, value, skillId, logId);

        //PacketSendUtility.sendPacket(getOwner().getMaster(), new SM_SUMMON_UPDATE(getOwner()));
        getOwner().getGameStats().updateVisualStatOnly();
    }

    @Override
    protected void onIncreaseMp(TYPE type, int value, int skillId, int logId) {
        //PacketSendUtility.sendPacket(getOwner().getMaster(), new SM_SUMMON_UPDATE(getOwner()));
        getOwner().getGameStats().updateVisualStatOnly();
    }

    @Override
    protected void onReduceHp() {
        //PacketSendUtility.sendPacket(getOwner().getMaster(), new SM_SUMMON_UPDATE(getOwner()));
        getOwner().getGameStats().updateVisualStatOnly();
    }

    @Override
    protected void onReduceMp() {
        //PacketSendUtility.sendPacket(getOwner().getMaster(), new SM_SUMMON_UPDATE(getOwner()));
        getOwner().getGameStats().updateVisualStatOnly();
    }

    @Override
    public Summon getOwner() {
        return (Summon) super.getOwner();
    }

    @Override
    public void restoreHp() {
        int currentRegenHp = getOwner().getGameStats().getCurrentStat(StatEnum.REGEN_HP);
        if (getOwner().getMode() == SummonMode.REST)
            currentRegenHp *= 8;
        increaseHp(TYPE.NATURAL_HP, currentRegenHp);
    }

    @Override
    public void restoreMp() {
        int currentRegenHp = getOwner().getGameStats().getCurrentStat(StatEnum.REGEN_MP);
        if (getOwner().getMode() == SummonMode.REST)
            currentRegenHp *= 8;
        increaseHp(TYPE.NATURAL_MP, currentRegenHp);
    }
}
