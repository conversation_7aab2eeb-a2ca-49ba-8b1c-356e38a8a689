/*
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.ai.state.handler;

import gameserver.ai.AI;
import gameserver.ai.desires.impl.AttackDesire;
import gameserver.ai.desires.impl.MoveToTargetDesire;
import gameserver.ai.desires.impl.SkillUseDesire;
import gameserver.ai.events.Event;
import gameserver.ai.state.AIState;
import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.BoundRadius;
import gameserver.model.EmotionType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_LOOKATOBJECT;
import gameserver.utils.PacketSendUtility;

/**
 * <AUTHOR>
 */
public class AttackingStateHandler extends StateHandler {
    @Override
    public AIState getState() {
        return AIState.ATTACKING;
    }

    /**
     * State ATTACKING AI MonsterAi AI AggressiveAi
     */
    @Override
    public void handleState(AIState state, final AI<?> ai) {
        ai.clearDesires();

        final Creature target = ((Npc) ai.getOwner()).getAggroList().getMostHated();
        if (target == null) {
            ai.handleEvent(Event.TIRED_ATTACKING_TARGET);
            return;
        }

        if (target.getLifeStats().isAlreadyDead()) {
            ai.handleEvent(Event.TIRED_ATTACKING_TARGET);
            return;
        }

        if (!ai.getOwner().isEnemy(target)) {
            ai.handleEvent(Event.TIRED_ATTACKING_TARGET);
            return;
        }

        final Npc owner = (Npc) ai.getOwner();
        owner.setTarget(target);
        PacketSendUtility.broadcastPacket(owner, new SM_LOOKATOBJECT(owner));

        if (!owner.isInState(CreatureState.WEAPON_EQUIPPED)) {
            owner.unsetState(CreatureState.NPC_IDLE);
            owner.setState(CreatureState.WEAPON_EQUIPPED);
            PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                EmotionType.START_EMOTE2, 0, target.getObjectId()));
            PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner, EmotionType.ATTACKMODE,
                0, target.getObjectId()));
        }
        
        BoundRadius bound = owner.getBoundRadius();

        owner.getMoveController().setSpeed(
            owner.getGameStats().getCurrentStat(StatEnum.SPEED) / 1000f);
        owner.getMoveController().setDistance(
            owner.getGameStats().getCurrentStat(StatEnum.ATTACK_RANGE) / 1000f
                + bound.getFront());

        if (owner.getNpcSkillList() != null && !owner.getNpcSkillList().getNpcSkills().isEmpty()) {
            if (owner.getController() instanceof BossController) {
                if (!((BossController) owner.getController()).hasCustomSkills())
                    ai.addDesire(new SkillUseDesire(owner, AIState.USESKILL.getPriority()));
            }
            else
                ai.addDesire(new SkillUseDesire(owner, AIState.USESKILL.getPriority()));
        }

        ai.addDesire(new AttackDesire(owner, target, AIState.ATTACKING.getPriority()));
        if (owner.getGameStats().getCurrentStat(StatEnum.SPEED) != 0)
            ai.addDesire(new MoveToTargetDesire(owner, target, AIState.ATTACKING.getPriority()));

        ai.schedule();
    }
}
