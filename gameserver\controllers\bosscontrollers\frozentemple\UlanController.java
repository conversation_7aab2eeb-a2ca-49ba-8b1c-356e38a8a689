/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.frozentemple;

import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.MathUtil;

/**
 * <AUTHOR>
 */
public class UlanController extends BossController {
    private final BossSkill CRYSTAL_WAVE = new BossSkill(19289, 1);

    public UlanController() {
        super(212315, true);
    }

    @Override
    protected void think() {
        Npc owner = getOwner();

        Player priority = getPriorityTarget();
        if (priority == null || !MathUtil.isIn3dRange(owner, priority, 20))
            return;

        getOwner().getAggroList().addHate(priority, 10000);
        
        int cooldown = (getOwner().getBattleground() != null
            && getOwner().getBattleground().isSpecial()) ? 1 : 2;

        if (CRYSTAL_WAVE.timeSinceUse() > cooldown)
            queueSkill(CRYSTAL_WAVE, owner);
    }
}