package gameserver.model.templates.bossevent;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "point3D", namespace = "pvpevent", propOrder = { "x", "y", "z" })
public class Point3DTemplate {
    @XmlElement(defaultValue = "0")
    protected float x;
    @XmlElement(defaultValue = "0")
    protected float y;
    @XmlElement(defaultValue = "0")
    protected float z;

    public Position3D Point3DValue() {
        return new Position3D(x, y, z);
    }

}
