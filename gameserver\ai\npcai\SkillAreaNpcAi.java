package gameserver.ai.npcai;

import gameserver.ai.AI;
import gameserver.ai.desires.AbstractDesire;
import gameserver.ai.events.Event;
import gameserver.ai.events.handler.EventHandler;
import gameserver.ai.state.AIState;
import gameserver.ai.state.handler.StateHandler;
import gameserver.model.gameobjects.SkillAreaNpc;
import gameserver.skillengine.SkillEngine;
import gameserver.skillengine.model.Skill;

/**
 * <AUTHOR>
 * 
 */
public class SkillAreaNpcAi extends NpcAi {
    public SkillAreaNpcAi() {
        /**
         * Event handlers
         */
        this.addEventHandler(new RespawnedEventHandler());

        /**
         * State handlers
         */
        this.addStateHandler(new ActiveNpcStateHandler());
    }

    public static class RespawnedEventHandler implements EventHandler {
        @Override
        public Event getEvent() {
            return Event.RESPAWNED;
        }

        @Override
        public void handleEvent(Event event, AI<?> ai) {
            ai.setAiState(AIState.ACTIVE);
            if (!ai.isScheduled())
                ai.analyzeState();
        }

    }

    public class ActiveNpcStateHandler extends StateHandler {
        @Override
        public AIState getState() {
            return AIState.ACTIVE;
        }

        @Override
        public void handleState(AIState state, AI<?> ai) {
            ai.clearDesires();
            SkillAreaNpc owner = (SkillAreaNpc) ai.getOwner();

            ai.addDesire(new SkillUseDesire(owner, AIState.ACTIVE.getPriority()));

            if (ai.desireQueueSize() == 0)
                ai.handleEvent(Event.RESPAWNED);
            else
                ai.schedule();
        }
    }

    public static class SkillUseDesire extends AbstractDesire {
        /**
         * SkillAreaNpc object
         */
        private SkillAreaNpc owner;

        /**
         * 
         * @param desirePower
         * @param owner
         */
        private SkillUseDesire(SkillAreaNpc owner, int desirePower) {
            super(desirePower);
            this.owner = owner;
        }

        @Override
        public boolean handleDesire(AI<?> ai) {
            if (owner.hasCustomHandler())
                return false;

            owner.getAi().setAiState(AIState.ACTIVE);

            if (owner.getMaster() == null || owner.getPosition().getMapId() == 0) {
                owner.getLifeStats().reduceHp(100000, owner);
                return false;
            }

            Skill skill = SkillEngine.getInstance().getSkill(owner, owner.getSkillId(), 1, owner);

            if (skill != null)
                skill.useSkill();

            return true;
        }

        @Override
        public int getExecutionInterval() {
            return 3;
        }

        @Override
        public void onClear() {
            // TODO Auto-generated method stub
        }
    }

}
