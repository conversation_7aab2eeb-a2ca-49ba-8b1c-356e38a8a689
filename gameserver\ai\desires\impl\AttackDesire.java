/*
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.ai.desires.impl;

import gameserver.ai.AI;
import gameserver.ai.desires.AbstractDesire;
import gameserver.ai.events.Event;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.BoundRadius;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.Trap;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.templates.stats.NpcRank;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;

/**
 * This class indicates that character wants to attack somebody
 * 
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
public class AttackDesire extends AbstractDesire {
    private int attackNotPossibleCounter;

    private int attackCounter = 1;

    /**
     * Target of this desire
     */
    protected Creature target;

    protected Npc owner;

    /**
     * Creates new attack desire, target can't be changed
     * 
     * @param npc
     *            The Npc that's attacking
     * @param target
     *            whom to attack
     * @param desirePower
     *            initial attack power
     */
    public AttackDesire(Npc npc, Creature target, int desirePower) {
        super(desirePower);
        this.target = target;
        this.owner = npc;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean handleDesire(AI<?> ai) {
        if (owner instanceof Trap)
            return false;

        if (target == null || target.getLifeStats().isAlreadyDead()
            || target.getLifeStats().getCurrentHp() <= 0) {
            owner.getAggroList().stopHating(target);
            target = owner.getAggroList().getMostHated();
            if (target == null) {
                owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);
                return false;
            }
        }

        if (!owner.isEnemy(target)
        /*
         * || (target instanceof Npc && ((Npc) target).getObjectTemplate().getNpcType().getId() ==
         * NpcType.NON_ATTACKABLE .getId())
         */) {
            owner.getAggroList().stopHating(target);
            owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);
            return false;
        }

        if (owner.getMoveController().hasFollowLeader()
            && MathUtil.getDistance(owner, owner.getMoveController().getFollowLeader()) > 25) {
            owner.getAggroList().clearHate();
            owner.setTarget(null);
            owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);
            return false;
        }

        double distance = MathUtil.getDistance(owner.getX(), owner.getY(), owner.getZ(),
            target.getX(), target.getY(), target.getZ());

        if (distance > (owner.isGuard() ? 80 : 40)) {
            owner.getAggroList().stopHating(target);
            owner.getMoveController().stop();
            owner.getController().stopMoving();
            owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);

            if (target instanceof Player && owner.getObjectTemplate() != null)
                PacketSendUtility.sendPacket((Player) target, SM_SYSTEM_MESSAGE
                    .STR_UI_COMBAT_NPC_RETURN(owner.getObjectTemplate().getNameId()));
            return false;
        }
        else if (!owner.hasWalkRoutes() && !owner.getMoveController().hasFollowLeader()
            && !owner.isNoHome()) {
            double distToSpawn = MathUtil.getDistance(owner.getX(), owner.getY(), owner.getZ(),
                owner.getSpawn().getX(), owner.getSpawn().getY(), owner.getSpawn().getZ());

            if (distToSpawn > (owner.isGuard() ? 160 : 120)) {
                owner.getAggroList().stopHating(target);
                owner.getMoveController().stop();
                owner.getController().stopMoving();
                owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);

                if (target instanceof Player && owner.getObjectTemplate() != null)
                    PacketSendUtility.sendPacket((Player) target, SM_SYSTEM_MESSAGE
                        .STR_UI_COMBAT_NPC_RETURN(owner.getObjectTemplate().getNameId()));
                return false;
            }
        }

        if (target.getVisualState() != 0 && owner instanceof Npc) {
            NpcRank npcrank = owner.getObjectTemplate().getRank();
            /*
             * 3 currently GM invis This will only exclude elites from hide1
             */
            if (target.getVisualState() == 3
                || (npcrank != NpcRank.LEGENDARY && npcrank != NpcRank.HERO && (target
                    .getVisualState() != 1 || npcrank != NpcRank.ELITE))) {
                owner.getAggroList().stopHating(target);
                owner.getController().cancelCurrentSkill(true);// prevent npc from ending cast of skill
                owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);
                return false;
            }
        }

        attackCounter++;

        if (attackCounter % 2 == 0) {
            if (!owner.getAggroList().isMostHated(target)) {
                owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);
                return false;
            }
        }

        int attackRange = owner.getGameStats().getCurrentStat(StatEnum.ATTACK_RANGE)
            + (int) (owner.getBoundRadius().getFront() * 1000);

        if (target instanceof Npc)
            attackRange += target.getBoundRadius().getFront() * 1000 / 2;

        if (distance * 1000 <= (attackRange + 1000)
            && System.currentTimeMillis() > owner.getNextAttack()) {
            owner.getController().attackTarget(target);
            owner.setNextAttack(System.currentTimeMillis()
                + (owner.getObjectTemplate() != null ? owner.getObjectTemplate().getAttackRate()
                    : owner.getGameStats().getCurrentStat(StatEnum.ATTACK_SPEED)));
            attackNotPossibleCounter = 0;
        }
        else if (System.currentTimeMillis() > owner.getNextAttack()) {
            attackNotPossibleCounter++;
        }

        if (Math.abs(owner.getZ() - target.getZ()) > (attackRange + 1000))
            attackNotPossibleCounter += 2;

        if (!GeoEngine2.getInstance().canSee(owner, target))
            attackNotPossibleCounter += 3;

        if (attackNotPossibleCounter > (owner.isGuard() ? 15 : 10)) {
            owner.getAggroList().stopHating(target);
            owner.getMoveController().stop();
            owner.getController().stopMoving();
            owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);

            if (target instanceof Player && owner.getObjectTemplate() != null)
                PacketSendUtility.sendPacket((Player) target, SM_SYSTEM_MESSAGE
                    .STR_UI_COMBAT_NPC_RETURN(owner.getObjectTemplate().getNameId()));
            return false;
        }
        return true;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (!(o instanceof AttackDesire))
            return false;

        AttackDesire that = (AttackDesire) o;

        return target.equals(that.target);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public int hashCode() {
        return target.hashCode();
    }

    /**
     * Returns target of this desire
     * 
     * @return target of this desire
     */
    public Creature getTarget() {
        return target;
    }

    @Override
    public int getExecutionInterval() {
        return 1;
    }

    @Override
    public void onClear() {
        // owner.unsetState(CreatureState.WEAPON_EQUIPPED);
    }

}