/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.geoEngine2;

import gameserver.geoEngine2.bounding.BoundingBox;
import gameserver.geoEngine2.collision.CollisionResult;
import gameserver.geoEngine2.collision.CollisionResults;
import gameserver.geoEngine2.math.Ray;
import gameserver.geoEngine2.math.Vector3f;
import gameserver.geoEngine2.scene.DoorGeometry;
import gameserver.geoEngine2.scene.Node;
import gameserver.geoEngine2.scene.Spatial;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * 
 */
public class GeoMap extends Node {
    private byte[] terrainInfo;
    private float[][] terrain;
    private float singleZ;
    private int size;
    private int worldSize = 3072;

    private List<BoundingBox> tmpBox = new ArrayList<BoundingBox>();

    private Map<Integer, DoorGeometry> doors = new ConcurrentHashMap<Integer, DoorGeometry>();

    private static final int BB_SIZE = 256;

    public GeoMap(String name) {
        this.name = name;
    }

    private void initialiseBoundingBoxes() {
        for (int x = 0; x < worldSize; x += BB_SIZE) {
            for (int y = 0; y < worldSize; y += BB_SIZE) {
                Node geoNode = new Node(null);
                tmpBox.add(new BoundingBox(new Vector3f(x, y, 0.0F), new Vector3f(x + BB_SIZE, y
                    + BB_SIZE, 4000.0F)));
                super.attachChild(geoNode);
            }
        }
    }

    public boolean attachDoor(int doorId, DoorGeometry door) {
        if (doors.containsKey(doorId))
            return false;
        
        return false;/*

        doors.put(doorId, door);

        return attachChild(door) > 0;*/
    }

    public boolean setDoorOpen(int instanceId, int doorId, boolean open) {
        if (!doors.containsKey(doorId))
            return false;

        doors.get(doorId).setDoorOpen(instanceId, open);

        return true;
    }

    public int attachChild(Spatial child) {
        int i = 0;
        for (Spatial spatial : getChildren()) {
            if (tmpBox.get(i).intersects(child.getWorldBound())) {
                ((Node) spatial).attachChild(child);
            }
            ++i;
        }
        return 0;
    }

    public void setTerrainData(short[] terrainData, byte[] terrainInfo) {
        this.terrainInfo = terrainInfo;
        this.size = (int) Math.sqrt(terrainData.length);

        if (terrainData.length > 1) {
            this.worldSize = size * 2;
            terrain = new float[size][size];

            for (int y = 0; y < size; y++) {
                for (int x = 0; x < size; x++) {
                    short point = terrainData[y + x * size];

                    terrain[x][y] = (float) (point / 32.0F);
                }
            }
        }
        else
            singleZ = terrainData[0] / 32.0F;

        this.initialiseBoundingBoxes();
    }

    public int getWorldSize() {
        return worldSize;
    }
    
    public float getZ(float x, float y, float z, float zFix) {
        return getZ(x, y, z, zFix, false);
    }

    public float getZ(float x, float y, float z, float zFix, boolean debug) {
        CollisionResults results = new CollisionResults();
        results.setDebug(debug);
        
        float newZ = getZ(x, y);

        Vector3f pos = new Vector3f(x, y, z + zFix);
        Vector3f dir = new Vector3f(x, y, z - 100.0F);
        Float limit = Float.valueOf(pos.distance(dir));
        dir.subtractLocal(pos).normalizeLocal();
        Ray r = new Ray(pos, dir);
        r.setLimit(limit.floatValue());
        collideWith(r, results);

        if (newZ < z + zFix) {
            CollisionResult result = new CollisionResult(new Vector3f(x, y, newZ), z + zFix - newZ);
            results.addCollision(result);
        }

        if (results.size() == 0)
            return newZ;

        return results.getClosestCollision().getContactPoint().z;
    }

    private float cubic(float[] p, float x) {
        int xi = (int) x;
        x -= xi;

        float p0 = p[Math.max(0, xi - 1)];
        float p1 = p[xi];
        float p2 = p[Math.min(p.length - 1, xi + 1)];
        float p3 = p[Math.min(p.length - 1, xi + 2)];

        return p1
            + 0.5f
            * x
            * (p2 - p0 + x
                * (2.0f * p0 - 5.0f * p1 + 4.0f * p2 - p3 + x * (3.0f * (p1 - p2) + p3 - p0)));
    }

    public float getBicubicZ(float x, float y) {
        if (terrain == null)
            return singleZ;

        float[] arr = new float[4];

        x /= 2.0F;
        y /= 2.0F;

        if (terrainInfo != null && terrainInfo[(int) y + ((int) x) * size] == 0x3F)
            return -1F;

        int xi = (int) x;
        x -= xi;

        arr[0] = cubic(terrain[Math.max(0, xi - 1)], y);
        arr[1] = cubic(terrain[xi], y);
        arr[2] = cubic(terrain[Math.min(terrain.length - 1, xi + 1)], y);
        arr[3] = cubic(terrain[Math.min(terrain.length - 1, xi + 2)], y);

        return cubic(arr, x + 1);
    }

    public float getBilinearZ(float x, float y) {
        if (terrain == null)
            return singleZ;

        try {

            x /= 2.0F;
            y /= 2.0F;

            int xi = (int) x;
            int yi = (int) y;

            if (terrainInfo != null && terrainInfo[yi + xi * size] == 0x3F)
                return -1F;

            x -= xi;
            y -= yi;

            float p0 = terrain[xi][yi];
            float p1 = terrain[xi][Math.min(terrain.length - 1, yi + 1)];
            float p2 = terrain[Math.min(terrain.length - 1, xi + 1)][yi];
            float p3 = terrain[Math.min(terrain.length - 1, xi + 1)][Math.min(terrain.length - 1,
                yi + 1)];

            float p02 = p0 + (p2 - p0) * x;
            float p13 = p1 + (p3 - p1) * x;
            float p0213 = p02 + (p13 - p02) * y;

            return p0213;
        }
        catch (Exception e) {
            return 0F;
        }
    }

    public float getZ(float x, float y) {
        // return getBicubicZ(x, y);
        if (x < 0)
            x = 0;
        else if (x > worldSize)
            x = worldSize;

        if (y < 0)
            y = 0;
        else if (y > worldSize)
            y = worldSize;

        return getBilinearZ(x, y);
    }

    public int getTerrainInfo(float x, float y) {
        if (terrainInfo == null)
            return 0;

        try {
            if (x < 0)
                x = 0;
            else if (x > worldSize)
                x = worldSize;

            if (y < 0)
                y = 0;
            else if (y > worldSize)
                y = worldSize;

            int xi = (int) (x / 2);
            int yi = (int) (y / 2);

            return terrainInfo[yi + xi * size];
        }
        catch (Exception e) {
        }

        return 0;
    }

    public boolean canSee(float x, float y, float z, float targetX, float targetY, float targetZ,
        boolean terrainCheck) {
        try {
            targetZ += 1.5F;
            z += 1.5F;

            float x2 = x - targetX;
            float y2 = y - targetY;
            float distance = (float) Math.sqrt(x2 * x2 + y2 * y2);
            if (distance > 80.0F)
                return false;

            Vector3f pos = new Vector3f(x, y, z);
            Vector3f dir = new Vector3f(targetX, targetY, targetZ);
            Float limit = Float.valueOf(pos.distance(dir));
            dir.subtractLocal(pos).normalizeLocal();

            if (terrainCheck) {
                Vector3f terrain = terrainCollision(x, y, z, targetX, targetY, targetZ);

                if (terrain != null) {
                    Vector3f va = new Vector3f(1F, 0F, getZ(terrain.getX() + 1, terrain.getY())
                        - getZ(terrain.getX() - 1, terrain.getY())).normalizeLocal();
                    Vector3f vb = new Vector3f(0F, 1F, getZ(terrain.getX(), terrain.getY() + 1)
                        - getZ(terrain.getX(), terrain.getY() - 1)).normalizeLocal();

                    if (dir.dot(va.crossLocal(vb)) <= 0)
                        return false;
                }
            }

            Ray r = new Ray(pos, dir);
            r.setLimit(limit.floatValue());
            CollisionResults results = new CollisionResults(true);
            collideWith(r, results);

            return (results.size() == 0);
        }
        catch (Exception e) {
        }

        return false;
    }

    public CollisionResults getCollisions(float x, float y, float z, float targetX, float targetY,
        float targetZ) {
        CollisionResults results = new CollisionResults();

        targetZ += 1.5F;
        z += 1.5F;

        Vector3f pos = new Vector3f(x, y, z);
        Vector3f dir = new Vector3f(targetX, targetY, targetZ);
        Float limit = Float.valueOf(pos.distance(dir));
        dir.subtractLocal(pos).normalizeLocal();
        Ray r = new Ray(pos, dir);
        r.setLimit(limit.floatValue());

        // try {
        collideWith(r, results);

        Vector3f terrain = terrainCollision(x, y, z, targetX, targetY, targetZ);
        if (terrain != null) {
            CollisionResult result = new CollisionResult(terrain, terrain.distance(pos));

            Vector3f va = new Vector3f(1F, 0F, getZ(terrain.getX() + 1, terrain.getY())
                - getZ(terrain.getX() - 1, terrain.getY())).normalizeLocal();
            Vector3f vb = new Vector3f(0F, 1F, getZ(terrain.getX(), terrain.getY() + 1)
                - getZ(terrain.getX(), terrain.getY() - 1)).normalizeLocal();

            result.setContactNormal(va.crossLocal(vb));

            if (dir.dot(result.getContactNormal()) <= 0)
                results.addCollision(result);
        }
        // }
        // catch (Exception e) {
        // }

        if (results.size() == 0) {
            results
                .addCollision(new CollisionResult(new Vector3f(targetX, targetY, targetZ), limit));
        }
        else {
            for (CollisionResult coll : results) {
                if (coll.getContactNormal() != null)
                    coll.getContactPoint().addLocal(coll.getContactNormal().multLocal(0.5f));
            }
        }

        return results;
    }

    public CollisionResult getClosestCollision(float x, float y, float z, float targetX,
        float targetY, float targetZ) {
        return getCollisions(x, y, z, targetX, targetY, targetZ).getClosestCollision();
    }

    public Vector3f getClosestCollisionPoint(float x, float y, float z, float targetX,
        float targetY, float targetZ) {
        Vector3f coll = getClosestCollision(x, y, z, targetX, targetY, targetZ).getContactPoint();

        return coll;
    }

    public Vector3f terrainCollision(float x, float y, float z, float targetX, float targetY,
        float targetZ) {
        float x2 = targetX - x;
        float y2 = targetY - y;
        float z2 = targetZ - z;
        float distance = (float) Math.sqrt(x2 * x2 + y2 * y2);

        int intD = (int) Math.abs(distance);
        float terrainZ = getZ(x, y);

        if (intD > 100 || terrainZ < 0)
            return null;

        if (intD == 0) {
            if ((z > targetZ && terrainZ < z && terrainZ > targetZ)
                || (z < targetZ && terrainZ > z && terrainZ < targetZ))
                return new Vector3f(x, y, terrainZ);
            else
                return null;
        }

        boolean terrain = terrainZ < z;

        Vector3f collision = null;

        for (float s = 1F; s < intD; s += 1F) {
            float tempX = x + x2 * s / distance;
            float tempY = y + y2 * s / distance;
            float tempZ = z + z2 * s / distance;

            float newZ = getZ(tempX, tempY);

            if (newZ < 0) {
                collision = null;
                break;
            }

            if (collision != null)
                continue;

            if (terrain) {
                if (newZ > tempZ) {
                    collision = new Vector3f(tempX, tempY, newZ);
                }
            }
            else if (newZ < tempZ) {
                collision = new Vector3f(tempX, tempY, newZ);
            }
        }

        return collision;
    }

    public float getLowestZ(float x, float y) {
        float newZ = getZ(x, y);

        Vector3f pos = new Vector3f(x, y, 0F);
        Vector3f dir = new Vector3f(x, y, 4000F);

        dir.subtractLocal(pos).normalizeLocal();
        Ray r = new Ray(pos, dir);
        r.setLimit(newZ);

        CollisionResults results = new CollisionResults(false);
        collideWith(r, results);

        if (results.size() == 0)
            return newZ;

        return results.getClosestCollision().getContactPoint().z;
    }

    public float getHighestZ(float x, float y) {
        float newZ = getZ(x, y);

        Vector3f pos = new Vector3f(x, y, 4000F);
        Vector3f dir = new Vector3f(x, y, 0F);

        dir.subtractLocal(pos).normalizeLocal();
        Ray r = new Ray(pos, dir);
        r.setLimit(4000 - newZ);

        CollisionResults results = new CollisionResults(false);
        collideWith(r, results);

        if (results.size() == 0)
            return newZ;

        return results.getClosestCollision().getContactPoint().z;
    }

    public void updateModelBound() {
        if (getChildren() != null) {
            Iterator<Spatial> i = getChildren().iterator();

            while (i.hasNext()) {
                Spatial s = (Spatial) i.next();

                if ((s instanceof Node) && (((Node) s).getChildren().isEmpty()))
                    i.remove();
            }

            // this.tmpBox = null;
        }

        super.updateModelBound();
    }
}
