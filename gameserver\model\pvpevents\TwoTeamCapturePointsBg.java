/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.controllers.NpcController;
import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.ActionObserver.ObserverType;
import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.model.templates.spawn.SpawnGroup;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_DELETE;
import gameserver.network.aion.serverpackets.SM_NPC_INFO;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.idfactory.IDFactory;
import gameserver.world.Executor;
import gameserver.world.World;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
public class TwoTeamCapturePointsBg extends Battleground {
    private int extraCounter = 0;
    private Map<Integer, Integer> teamPoints = new ConcurrentHashMap<Integer, Integer>();

    private static final int NPC_CAPTURE_POINT = 283130;
    private static final int NPC_NEUTRAL_MOB = 214662;

    private static final int SKILL_NEUTRAL_BUFF_ID = 18458;
    private static final int SKILL_NEUTRAL_BUFF_DURATION = 2 * 60 * 1000;

    private static final int INITIAL_DELAY = 30;

    private static final int CAPTURE_POINT_RADIUS = 5;
    private static final int CAPTURE_POINT_DELAY = 10 * 1000;

    private static final int CAPTURE_POINT_RESET_DELAY = 150 * 1000;

    private static final int POINTS_TO_WIN = 300;

    private static final int NEUTRAL = -1;
    private static final int BLUE_TEAM = 0;
    private static final int GREEN_TEAM = 1;

    private Npc[] points = new Npc[4];
    private Npc[] flags = new Npc[4];
    private int[] owners = new int[4];
    private ScheduledFuture<?>[] tasks = new ScheduledFuture<?>[4];
    private int[] taskOwners = new int[4];
    private long[] lastActivities = new long[4];
    private ReentrantLock[] locks = new ReentrantLock[4];

    private Npc neutralMob;
    private Npc neutralMob2;

    private boolean endCalled = false;

    public TwoTeamCapturePointsBg() {
        super.name = "2-Team Capture Points";
        super.description = "You are on a team and must capture points to gain points. You capture points by standing on them. You will respawn at your base every 60 seconds. If a team reaches "
            + POINTS_TO_WIN + " they win.";
        super.minSize = 5;
        super.maxSize = 6;
        super.teamCount = 2;
        super.matchLength = 600 + INITIAL_DELAY;

        BattlegroundMap map1 = new BattlegroundMap(600030000);
        map1.addSpawn(new SpawnPosition(1735.54f, 1383.12f, 246.16f));
        map1.addSpawn(new SpawnPosition(1731.00f, 1682.96f, 247.88f));
        map1.setKillZ(170f);

        BattlegroundMap map2 = new BattlegroundMap(600010000);
        map2.addSpawn(841f, 967f, 322f);
        map2.addSpawn(844f, 569f, 321f);
        map2.setKillZ(280f);

        super.maps.add(map2);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    private void spawnStuff() {
        if (super.getMapId() == 600030000) {
            points[0] = (Npc) spawn(NPC_CAPTURE_POINT, 1865.2579f, 1538.2681f, 235f);
            points[1] = (Npc) spawn(NPC_CAPTURE_POINT, 2045.9285f, 1537.7911f, 210f);
            points[2] = (Npc) spawn(NPC_CAPTURE_POINT, 1997.0569f, 1316.9027f, 234f);
            points[3] = (Npc) spawn(NPC_CAPTURE_POINT, 1970.3755f, 1756.8999f, 234f);
        }
        else if (super.getMapId() == 600010000) {
            points[0] = (Npc) spawn(NPC_CAPTURE_POINT, 918.766f, 716.4384f, 297f);
            points[1] = (Npc) spawn(NPC_CAPTURE_POINT, 914.6418f, 821.4658f, 297f);
            points[2] = (Npc) spawn(NPC_CAPTURE_POINT, 625.2744f, 675.16516f, 297f);
            points[3] = (Npc) spawn(NPC_CAPTURE_POINT, 625.505f, 857.81165f, 297f);
        }

        for (int i = 0; i < points.length; i++) {
            locks[i] = new ReentrantLock();
        }

        Arrays.fill(owners, NEUTRAL);
        Arrays.fill(taskOwners, NEUTRAL);
        Arrays.fill(lastActivities, 0);
    }

    public void startMatch() {
        super.createInstance();

        spawnStuff();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, INITIAL_DELAY * 1000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", INITIAL_DELAY * 1000);

        startBattleground(INITIAL_DELAY * 1000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups() <= 1)
                    endTwoTeamCaptureMatch();
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamCaptureMatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                showPointsOnMap();

                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    @Override
                    public void run() {
                        extraCounter++;

                        handleCapturePoints();

                        if ((extraCounter % 5) == 0) {
                            givePointsToOwners();
                        }

                        if ((extraCounter % 15) == 0) {
                            showPointsOnMap();
                            showTeamPositions();
                        }

                        if ((extraCounter % 60) == 0) {
                            extraCounter = 0;
                            autoResurrection();
                        }
                    }
                }, 1 * 1000, 1 * 1000));
            }
        }, INITIAL_DELAY * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (done)
                    return;

                spawnNeutralMob();
            }
        }, INITIAL_DELAY * 1000 + 10 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (done)
                    return;

                spawnNeutralMob();
            }
        }, INITIAL_DELAY * 1000 + 5 * 60 * 1000);
    }

    private void spawnNeutralMob() {
        if (super.getMapId() == 600030000) {
            announceAll("A bountiful monster has appeared at the center of the map!");

            neutralMob = (Npc) spawn(NPC_NEUTRAL_MOB, 1920.2194f, 1538.3677f, 177f);
        }
        else if (super.getMapId() == 600010000) {
            announceAll("A bountiful monster has appeared to the North of Jotun Square!");

            neutralMob = (Npc) spawn(NPC_NEUTRAL_MOB, 576.9918f, 767.4584f, 300f);
        }

        neutralMob.getObserveController().attach(new ActionObserver(ObserverType.DEATH) {
            @Override
            public void died(Creature creature) {
                Player player = creature.getAggroList().getMostPlayerDamage();

                if (player == null || !player.isInGroup())
                    return;

                announceAll("The monster has been slain by the "
                    + LadderService.getInstance().getNameByIndex(
                        player.getPlayerGroup().getBgIndex()));

                addPoints(player.getPlayerGroup().getBgIndex(), 25);

                SkillTemplate template = DataManager.SKILL_DATA
                    .getSkillTemplate(SKILL_NEUTRAL_BUFF_ID);

                for (Player pl : player.getPlayerGroup().getMembers()) {
                    Effect effect = new Effect(pl, pl, template, 1, SKILL_NEUTRAL_BUFF_DURATION);
                    pl.getEffectController().addEffect(effect);
                    effect.addAllEffectToSucess();
                    effect.startEffect(true);
                }

                creature.getController().delete();
            }
        });
    }

    private void handleCapturePoints() {
        for (int _i = 0; _i < points.length; _i++) {
            final int i = _i;

            locks[i].lock();

            try {
                if (points[i] != null) {
                    int blueTeam = 0;
                    int greenTeam = 0;

                    for (PlayerGroup group : getGroups()) {
                        for (Player pl : group.getMembers()) {
                            if (!pl.getLifeStats().isAlreadyDead()
                                && MathUtil.isIn3dRange(points[i], pl, CAPTURE_POINT_RADIUS)) {
                                if (group.getBgIndex() == BLUE_TEAM)
                                    blueTeam++;
                                else if (group.getBgIndex() == GREEN_TEAM)
                                    greenTeam++;
                            }
                        }
                    }

                    if (blueTeam > 0 || greenTeam > 0)
                        lastActivities[i] = System.currentTimeMillis();

                    /*
                     * if (owners[i] == BLUE_TEAM) blueTeam *= 2; else if (owners[i] == GREEN_TEAM) greenTeam *= 2;
                     */

                    int check = blueTeam - greenTeam;

                    if (check == 0) {
                        if (tasks[i] != null) {
                            if (taskOwners[i] != NEUTRAL) {
                                announceAll(LadderService.getInstance().getNameByIndex(
                                    taskOwners[i])
                                    + "'s attempt to capture a point failed.");
                            }

                            taskOwners[i] = NEUTRAL;
                            tasks[i].cancel(true);
                            tasks[i] = null;
                        }
                    }
                    else if (check > 0) {
                        if (taskOwners[i] != BLUE_TEAM && tasks[i] != null) {
                            announceAll(LadderService.getInstance().getNameByIndex(taskOwners[i])
                                + "'s attempt to capture a point failed.");

                            taskOwners[i] = NEUTRAL;
                            tasks[i].cancel(true);
                            tasks[i] = null;
                        }

                        if (taskOwners[i] != BLUE_TEAM && owners[i] != BLUE_TEAM) {
                            if (owners[i] != NEUTRAL) {
                                announceAll(LadderService.getInstance().getNameByIndex(BLUE_TEAM)
                                    + " is neutralizing a point.");

                                taskOwners[i] = BLUE_TEAM;
                                tasks[i] = ThreadPoolManager.getInstance().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        announceAll(LadderService.getInstance().getNameByIndex(
                                            BLUE_TEAM)
                                            + " has neutralized a point!");

                                        locks[i].lock();

                                        try {
                                            owners[i] = NEUTRAL;
                                            taskOwners[i] = NEUTRAL;
                                            tasks[i] = null;
                                        }
                                        finally {
                                            locks[i].unlock();
                                        }

                                        updatePoint(i);
                                    }
                                }, CAPTURE_POINT_DELAY / 2);
                            }
                            else {
                                announceAll(LadderService.getInstance().getNameByIndex(BLUE_TEAM)
                                    + " is capturing a point.");

                                taskOwners[i] = BLUE_TEAM;
                                tasks[i] = ThreadPoolManager.getInstance().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        announceAll(LadderService.getInstance().getNameByIndex(
                                            BLUE_TEAM)
                                            + " has captured a point!");

                                        locks[i].lock();

                                        try {
                                            owners[i] = BLUE_TEAM;
                                            taskOwners[i] = NEUTRAL;
                                            tasks[i] = null;
                                        }
                                        finally {
                                            locks[i].unlock();
                                        }

                                        updatePoint(i);
                                    }
                                }, CAPTURE_POINT_DELAY);
                            }
                        }
                    }
                    else if (check < 0) {
                        if (taskOwners[i] != GREEN_TEAM && tasks[i] != null) {
                            announceAll(LadderService.getInstance().getNameByIndex(taskOwners[i])
                                + "'s attempt to capture a point failed.");

                            taskOwners[i] = NEUTRAL;
                            tasks[i].cancel(true);
                            tasks[i] = null;
                        }

                        if (taskOwners[i] != GREEN_TEAM && owners[i] != GREEN_TEAM) {
                            if (owners[i] != NEUTRAL) {
                                announceAll(LadderService.getInstance().getNameByIndex(GREEN_TEAM)
                                    + " is neutralizing a point.");

                                taskOwners[i] = GREEN_TEAM;
                                tasks[i] = ThreadPoolManager.getInstance().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        announceAll(LadderService.getInstance().getNameByIndex(
                                            GREEN_TEAM)
                                            + " has neutralized a point!");

                                        locks[i].lock();

                                        try {
                                            owners[i] = NEUTRAL;
                                            taskOwners[i] = NEUTRAL;
                                            tasks[i] = null;
                                        }
                                        finally {
                                            locks[i].unlock();
                                        }

                                        updatePoint(i);
                                    }
                                }, CAPTURE_POINT_DELAY / 2);
                            }
                            else {
                                announceAll(LadderService.getInstance().getNameByIndex(GREEN_TEAM)
                                    + " is capturing a point.");

                                taskOwners[i] = GREEN_TEAM;
                                tasks[i] = ThreadPoolManager.getInstance().schedule(new Runnable() {
                                    @Override
                                    public void run() {
                                        announceAll(LadderService.getInstance().getNameByIndex(
                                            GREEN_TEAM)
                                            + " has captured a point!");

                                        locks[i].lock();

                                        try {
                                            owners[i] = GREEN_TEAM;
                                            taskOwners[i] = NEUTRAL;
                                            tasks[i] = null;
                                        }
                                        finally {
                                            locks[i].unlock();
                                        }

                                        updatePoint(i);
                                    }
                                }, CAPTURE_POINT_DELAY);
                            }
                        }
                    }

                    if (owners[i] != NEUTRAL
                        && (System.currentTimeMillis() - lastActivities[i]) >= CAPTURE_POINT_RESET_DELAY) {
                        announceAll("A " + LadderService.getInstance().getNameByIndex(owners[i])
                            + " capture point reset to neutral status.");

                        owners[i] = NEUTRAL;
                        updatePoint(i);
                    }
                }
            }
            catch (Exception e) {
                log.error("Error in CapturePoints handling: ", e);
            }
            finally {
                locks[i].unlock();
            }
        }
    }

    private int getNpcIdForFlag(int bgIndex) {
        switch (bgIndex) {
            case 0:
                return 801601;
            case 1:
                return 801600;
        }

        return 801602;
    }

    private Npc createFlag(int npcId, VisibleObject loc) {
        SpawnTemplate spawn = new SpawnTemplate(loc.getX(), loc.getY(), loc.getZ(), (byte) 0, 0, 0,
            0);
        SpawnGroup spawnGroup = new SpawnGroup(this.getMapId(), npcId, 295, 1);
        spawn.setSpawnGroup(spawnGroup);
        spawnGroup.getObjects().add(spawn);

        final Npc flag = new Npc(IDFactory.getInstance().nextPlayerId(), new NpcController(),
            spawn, DataManager.NPC_DATA.getNpcTemplate(npcId));

        World.getInstance().setPosition(flag, mapId, this.getInstanceId(), loc.getX(), loc.getY(),
            loc.getZ(), (byte) 0);

        flag.setOutpostFlag(true);

        this.getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                PacketSendUtility.sendPacket(pl, new SM_NPC_INFO(flag, pl));
                return true;
            }
        }, true);

        return flag;
    }

    private void destroyFlag(Npc flag) {
        if (flag == null)
            return;

        final SM_DELETE pck = new SM_DELETE(flag, 19);

        this.getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                PacketSendUtility.sendPacket(pl, pck);
                return true;
            }
        }, true);
    }

    private void showPointsOnMap() {
        for (int i = 0; i < flags.length; i++) {
            updatePoint(i);
        }
    }

    private void updatePoint(int index) {
        if (index < 0 || index >= flags.length)
            return;

        destroyFlag(flags[index]);
        flags[index] = createFlag(getNpcIdForFlag(owners[index]), points[index]);
    }

    private void givePointsToOwners() {
        for (int i = 0; i < owners.length; i++)
            if (owners[i] != -1)
                addPoints(owners[i], 1);
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    private void addPoints(int bgIndex, int points) {
        if (bgIndex < 0)
            return;

        if (!teamPoints.containsKey(bgIndex))
            teamPoints.put(bgIndex, 0);

        int result = teamPoints.get(bgIndex);
        teamPoints.put(bgIndex, result + points);

        if (getTeamPoints(bgIndex) >= POINTS_TO_WIN)
            endTwoTeamCaptureMatch();
    }

    private void showTeamPositions() {
        String msg = "";
        msg += String.format("Blue: %d - Green: %d", getTeamPoints(0), getTeamPoints(1));

        for (PlayerGroup group : super.getGroups())
            for (Player pl : group.getMembers())
                super.scheduleAnnouncement(pl, msg, 0);

        super.specAnnounce(msg);
    }

    private void autoResurrection() {
        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead()) {
                    pl.getReviveController().fullRevive();
                    super.spawnProtection(pl);
                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    TeleportService.teleportTo(pl, getMapId(), super.getInstanceId(), pos.getX(),
                        pos.getY(), pos.getZ(), 0);
                    super
                        .scheduleAnnouncement(pl, "You have been automatically resurrected!", 1500);
                }
            }
        }
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super.scheduleAnnouncement(player, "You will automatically resurrect every 60 seconds.", 0);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;

            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in TwoTeamCapturePointsBg!");
            }
            else {
                addPoints(killer.getPlayerGroup().getBgIndex(), 3);

                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 2);
            }
        }
    }

    @Override
    public boolean createTournament(List<List<Player>> teams) {
        if (!super.createGroups(teams))
            return false;

        startMatch();

        return true;
    }

    private int getTeamPoints(int teamIndex) {
        if (!teamPoints.containsKey((Integer) teamIndex))
            return 0;

        return teamPoints.get((Integer) teamIndex);
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups() <= 1)
            endTwoTeamCaptureMatch();
    }

    private void endTwoTeamCaptureMatch() {
        if (endCalled)
            return;

        endCalled = true;

        super.onEndFirstDefault();

        PlayerGroup winner = null;
        boolean isDraw = false;
        if (getTeamPoints(0) == getTeamPoints(1))
            isDraw = true;
        else if (getTeamPoints(0) > getTeamPoints(1))
            winner = super.getGroups().get(0);
        else if (getTeamPoints(1) > getTeamPoints(0))
            winner = super.getGroups().get(1);

        String score = getTeamPoints(0) + " : " + getTeamPoints(1);

        if (isDraw) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.scheduleAnnouncement(pl,
                        "The battleground has ended in a draw with the score " + score + "!", 0);
                    super.scheduleAnnouncement(pl,
                        "For your effort you have been rewarded with some might.", 3000);
                    super.rewardPlayer(pl, 40, false);
                }
            }
            super.specAnnounce("The battleground has ended in a draw!");
        }
        else if (winner != null) {
            propagateWin(winner);
            
            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    if (group.getObjectId() == winner.getObjectId()) {
                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, super.K_VALUE);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.scheduleAnnouncement(pl,
                            "Your team has won the match with the score " + score + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);

                        super.rewardPlayer(pl, 60, true);
                    }
                    else {
                        super.playerLoseMatch(pl, -super.K_VALUE / 2);

                        super.scheduleAnnouncement(pl,
                            "Your team has lost the match with the score " + score + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);

                        super.rewardPlayer(pl, 40, false);
                    }
                }
            }

            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match with the score " + score + "!");
        }
        else {
            log.error("No winner or draw in a TwoTeamCaptureBg!??!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}