/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.bet;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * 
 */
public class Bet {
    private int playerObjectId;
    private int value = 1;
    private double rate = 1;
    private Date endDate = new Date();
    private List<Condition> conditions = new ArrayList<Condition>();

    public Bet(int owner) {
        this.playerObjectId = owner;
    }

    public Bet(int owner, int value, double rate, Date endDate, List<Condition> conditions) {
        this.playerObjectId = owner;
        this.value = value;
        this.rate = rate;
        this.endDate = endDate;
        this.conditions = conditions;
    }

    public int getOwnerObjectId() {
        return playerObjectId;
    }

    public int getValue() {
        return value;
    }

    public double getRate() {
        return rate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public List<Condition> getConditions() {
        return conditions;
    }

    public void setOwner(int ownerObjectId) {
        this.playerObjectId = ownerObjectId;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public void setRate(double rate) {
        this.rate = rate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public void setConditions(List<Condition> conditions) {
        this.conditions = conditions;
    }
}
