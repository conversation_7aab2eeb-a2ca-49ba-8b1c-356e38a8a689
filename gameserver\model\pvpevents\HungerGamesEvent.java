/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.dataholders.DataManager;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.drop.DropTemplate;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnGroup;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_SERIAL_STATUS;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.DropService;
import gameserver.services.ItemService;
import gameserver.services.PlayerService;
import gameserver.services.PvpService;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;
import gnu.trove.TIntIntHashMap;
import gnu.trove.TIntIntIterator;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class HungerGamesEvent extends Battleground {
    private boolean endCalled = false;

    private final static int INITIAL_DELAY = 30000;

    private Map<Player, Integer> oldPlayerMap = new HashMap<Player, Integer>();

    private Map<Integer, List<DropTemplate>> dropList = new HashMap<Integer, List<DropTemplate>>() {
        {
            // Crucible pots
            put(212958, Arrays.asList(new DropTemplate(-1, 164000168, 1, 1, 50), new DropTemplate(
                -1, 164000242, 1, 1, 50), new DropTemplate(-1, 164000208, 1, 1, 50),
                new DropTemplate(-1, 164000180, 1, 1, 50)));
            // Primus Pilus accessories
            put(210582, Arrays.asList(new DropTemplate(-1, 121000977, 1, 1, 20), new DropTemplate(
                -1, 120001067, 1, 1, 20), new DropTemplate(-1, 122001204, 1, 1, 20),
                new DropTemplate(-1, 123001066, 1, 1, 20),
                new DropTemplate(-1, 121000970, 1, 1, 20),
                new DropTemplate(-1, 120001060, 1, 1, 20),
                new DropTemplate(-1, 122001197, 1, 1, 20),
                new DropTemplate(-1, 123001059, 1, 1, 20),
                new DropTemplate(-1, 125002349, 1, 1, 20),
                new DropTemplate(-1, 125003577, 1, 1, 20),
                new DropTemplate(-1, 125002351, 1, 1, 20),
                new DropTemplate(-1, 125002352, 1, 1, 20)));
            // Manastones and scrolls
            put(214633, Arrays.asList(new DropTemplate(-1, 164000076, 1, 2, 50), new DropTemplate(
                -1, 164000073, 1, 2, 50), new DropTemplate(-1, 164000134, 1, 2, 50),
                new DropTemplate(-1, 167000705, 4, 6, 50),
                new DropTemplate(-1, 167000749, 4, 6, 50)));
            // Armors and weapons
            put(212928, Arrays.asList(new DropTemplate(-1, 110101094, 1, 1, 20), new DropTemplate(
                -1, 113101006, 1, 1, 20), new DropTemplate(-1, 112100950, 1, 1, 20),
                new DropTemplate(-1, 111100994, 1, 1, 20),
                new DropTemplate(-1, 114101035, 1, 1, 20),
                new DropTemplate(-1, 101900770, 1, 1, 20),
                new DropTemplate(-1, 100001237, 1, 1, 20),
                new DropTemplate(-1, 100900948, 1, 1, 20),
                new DropTemplate(-1, 102000804, 1, 1, 20),
                new DropTemplate(-1, 100500965, 1, 1, 20),
                new DropTemplate(-1, 101700990, 1, 1, 20),
                new DropTemplate(-1, 100601019, 1, 1, 20),
                new DropTemplate(-1, 100201099, 1, 1, 20),
                new DropTemplate(-1, 101500973, 1, 1, 20),
                new DropTemplate(-1, 100100944, 1, 1, 20),
                new DropTemplate(-1, 102100674, 1, 1, 20),
                new DropTemplate(-1, 115001296, 1, 1, 20)));
            // Pots and foods
            put(210596, Arrays.asList(new DropTemplate(-1, 162000080, 1, 1, 50), new DropTemplate(
                -1, 162000081, 1, 1, 50), new DropTemplate(-1, 162000023, 1, 1, 50),
                new DropTemplate(-1, 160003003, 1, 1, 10)));
        }
    };

    private TIntIntHashMap spawns = new TIntIntHashMap() {
        {
            put(212958, 2);
            put(210582, 1);
            put(214633, 2);
            put(212928, 1);
            put(210596, 2);
        }
    };

    public HungerGamesEvent() {
        super.name = "Hunger Games";
        super.description = "Maintain a food at all times to not starve!";
        super.displayName = "Hunger Games";
        super.minSize = 12;
        super.maxSize = 36;
        super.teamCount = 1;
        super.matchLength = INITIAL_DELAY / 1000 + 20 * 60;
        super.isAnonymous = false;
        super.afkKick = false;

        BattlegroundMap map1 = new BattlegroundMap(220010000);
        map1.addSpawn(new SpawnPosition(945.23f, 1698.44f, 276f));
        map1.setKillZ(245f);

        super.maps.add(map1);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueSolo(players);

        if (super.getPlayers().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        populateMapWithSpawns();

        synchronized (super.getPlayers()) {
            List<Player> oldPlayers = new ArrayList<Player>(super.getPlayers().size());
            oldPlayers.addAll(super.getPlayers());

            super.getPlayers().clear();

            for (Player master : oldPlayers) {
                Player pl = PlayerService.createTemporaryPlayer(master);

                getLeavers().put(pl.getObjectId(), pl);
                getLeaversDead().put(pl.getObjectId(), false);

                oldPlayerMap.put(pl, master.getObjectId());
                super.addPlayer(pl);
            }

            for (Player pl : super.getPlayers()) {
                super.preparePlayer(pl, INITIAL_DELAY);

                addInitialItems(pl);

                pl.setSerialKiller(2);

                SpawnPosition pos = getSpawnPositions().get(Rnd.get(getSpawnPositions().size()));
                performTeleport(pl, pos.getX() + (float) (Rnd.nextDouble() * 2 - 1), pos.getY()
                    + (float) (Rnd.nextDouble() * 2 - 1), pos.getZ());
            }
        }

        super.specAnnounce("The Hunger Games have begun!", INITIAL_DELAY);
        
        startBattleground(INITIAL_DELAY, new Runnable() {
            @Override
            public void run() {
                if (getRemainingPlayers() <= 1)
                    endHungerGames(false);
            }
        });

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                synchronized (getPlayers()) {
                    for (Player pl : getPlayers()) {
                        SkillTemplate template = DataManager.SKILL_DATA.getSkillTemplate(2573);
                        Effect effect = new Effect(pl, pl, template, 1, 65000);
                        pl.getEffectController().addEffect(effect);
                        effect.addAllEffectToSucess();
                        effect.startEffect(true);

                        SkillTemplate template2 = DataManager.SKILL_DATA.getSkillTemplate(182);
                        Effect effect2 = new Effect(pl, pl, template2, 1, 35000);
                        pl.getEffectController().addEffect(effect2);
                        effect2.addAllEffectToSucess();
                        effect2.startEffect(true);
                    }
                }
            }
        }, INITIAL_DELAY - 5000);

        super.setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
            private int extraCounter = 0;

            @Override
            public void run() {
                punishStarvingPlayers();

                if (System.currentTimeMillis() - getStartStamp() < 8 * 60 * 1000)
                    return;

                extraCounter++;

                if ((extraCounter % 6) == 0) {
                    extraCounter = 0;
                    alertMapLocations();
                }
            }
        }, INITIAL_DELAY + 5000, 10000));

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endHungerGames(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    private void alertMapLocations() {
        SM_SERIAL_STATUS pck = new SM_SERIAL_STATUS(new ArrayList<Player>(super.getPlayers()));

        synchronized (super.getPlayers()) {
            for (Player pl : super.getPlayers()) {
                if (pl.getLifeStats().isAlreadyDead())
                    continue;

                super.scheduleAnnouncement(pl, "Event",
                    "Open your map to see the locations of the remaining survivors!", 0);

                PacketSendUtility.sendPacket(pl, pck);
            }
        }

        super.specAnnounce("Open your map to see the locations of the remaining survivors!",
            "Event");
        for (Player pl : super.getSpectators())
            PacketSendUtility.sendPacket(pl, pck);
    }

    private void punishStarvingPlayers() {
        synchronized (super.getPlayers()) {
            for (Player pl : super.getPlayers()) {
                if (pl.getLifeStats().isAlreadyDead())
                    continue;

                boolean hasFood = false;

                for (Effect effect : pl.getEffectController().getAbnormalEffects()) {
                    if (effect.isFood()) {
                        hasFood = true;
                        break;
                    }
                }

                if (!hasFood) {
                    int amount = 200 + (int) (System.currentTimeMillis() - super.getStartStamp())
                        / (2 * 1000);
                    pl.getLifeStats().reduceHp(amount, pl);
                    pl.getLifeStats().reduceMp(amount);

                    PacketSendUtility.sendMessage(pl,
                        "You're starving! Quickly, eat some food to regain your health!");
                }
            }
        }
    }

    private void populateMapWithSpawns() {
        final float gridSize = 20.0f;
        final int length = (int) Math.ceil(3072 / gridSize);

        int[][] grid = new int[length][length];

        List<Integer> weightedList = new ArrayList<Integer>();

        for (TIntIntIterator it = spawns.iterator(); it.hasNext();) {
            it.advance();
            for (int i = 0; i < it.value(); i++)
                weightedList.add(it.key());
        }

        int spawns = 5000;

        while (!isFull(grid)) {
            int[] cell = getFreeCell(grid);
            if (cell == null)
                break;

            for (int i = -2; i <= 2; i++)
                for (int j = -2; j <= 2; j++)
                    if (cell[0] + i >= 0 && cell[0] + i < length && cell[1] + j >= 0
                        && cell[1] + j < length)
                        grid[cell[0] + i][cell[1] + j] = 1;

            Integer npcId = weightedList.get(Rnd.get(weightedList.size()));
            spawn(npcId, cell[0] * gridSize, cell[1] * gridSize);

            if (--spawns <= 0)
                break;
        }
    }

    private boolean isFull(int[][] grid) {
        for (int i = 0; i < grid.length; i++)
            for (int j = 0; j < grid.length; j++)
                if (grid[i][j] == 0)
                    return false;
        return true;
    }

    private int[] getFreeCell(int[][] grid) {
        int iterations = 0;

        while (iterations++ < grid.length) {
            int i = Rnd.get(grid.length);
            int j = Rnd.get(grid.length);

            if (grid[i][j] == 0)
                return new int[] { i, j };
        }

        return null;
    }

    private void spawn(int npcId, float x, float y) {
        float baseZ = GeoEngine2.getInstance().getLowestZ(super.getMapId(), x, y);

        if (baseZ <= 0f)
            return;

        baseZ += 10f;

        SpawnTemplate st = SpawnEngine.getInstance().addNewSpawn(super.getMapId(),
            super.getInstanceId(), npcId, x, y,
            GeoEngine2.getInstance().getZ(super.getMapId(), x, y, baseZ), (byte) 0, 0, 0, true);
        VisibleObject vo = SpawnEngine.getInstance().spawnObject(st, super.getInstanceId());

        if (vo instanceof Npc && dropList.containsKey(npcId)) {
            DropService.getInstance().registerDrop((Npc) vo, dropList.get(npcId), false);
        }
    }

    private void addInitialItems(Player player) {
        TIntIntHashMap initialItems = new TIntIntHashMap();

        initialItems.put(110101552, 1);
        initialItems.put(111101396, 1);
        initialItems.put(112101350, 1);
        initialItems.put(113101414, 1);
        initialItems.put(114101443, 1);
        initialItems.put(125300054, 1); // Head

        switch (player.getPlayerClass()) {
            case ASSASSIN:
                initialItems.put(100001493, 2); // Sword
                initialItems.put(100201304, 2); // Dagger
                initialItems.put(169300010, 1000); // Poison
                break;
            case RANGER:
                initialItems.put(101701190, 1); // Bow
                initialItems.put(169300008, 1000); // Seed
                initialItems.put(169300009, 1000); // Seed
                break;
            case CLERIC:
            case CHANTER:
                initialItems.put(101501172, 1); // Staff
                initialItems.put(100101140, 1); // Mace
                initialItems.put(115001528, 1); // Shield
                break;
            case GLADIATOR:
                initialItems.put(101301091, 1); // Polearm
            case TEMPLAR:
                initialItems.put(100101140, 1); // Mace
                initialItems.put(100001493, 1); // Sword
                initialItems.put(115001528, 1); // Shield
                initialItems.put(100901156, 1); // Greatsword
                break;
            case SORCERER:
            case SPIRIT_MASTER:
                initialItems.put(100501146, 1); // Orb
                initialItems.put(100601228, 1); // Book
                break;
            case GUNNER:
                initialItems.put(101800952, 2); // Guns
                initialItems.put(101900939, 1); // Cannon
                initialItems.put(167000555, 10);
                break;
            case BARD:
                initialItems.put(102000995, 1); // Harp
                break;
            case RIDER:
                initialItems.put(102100785, 1); // Cipher-blade
                break;
        }

        initialItems.put(160003003, 1); // Food
        initialItems.put(162000023, 1); // Greater Healing Potion
        initialItems.put(169300007, 10); // Odella

        for (TIntIntIterator it = initialItems.iterator(); it.hasNext();) {
            it.advance();
            ItemService.addItem(player, it.key(), it.value());
        }
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        if (lastAttacker instanceof Player && lastAttacker.getObjectId() != player.getObjectId()) {
            PvpService.getInstance().addMight((Player) lastAttacker, 20);
        }

        resetSerialKiller(player);

        super.scheduleAnnouncement(player, "Event", "You have been defeated in the Hunger Games!",
            3000);
        super.scheduleAnnouncement(player, "Event",
            "Continue to spectate the Games by typing .spectate join " + super.getBgId(), 8000);
        super.scheduleAnnouncement(player, "Event", "Now you will be returned to the real world",
            13000);

        spawnTombstone(player);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                onLeave(player, false, false);
            }
        }, 8000);
    }

    private void resetSerialKiller(final Player player) {
        player.setSerialKiller(0);

        final SM_SERIAL_STATUS pck = new SM_SERIAL_STATUS(Arrays.asList(player));

        super.getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                PacketSendUtility.sendPacket(pl,
                    SM_SYSTEM_MESSAGE.STR_ABYSS_ORDER_RANKER_DIE(player));
                PacketSendUtility.sendPacket(pl, pck);
                return true;
            }
        });
    }

    private void spawnTombstone(final Player player) {
        final SpawnTemplate st = new SpawnTemplate(player.getX(), player.getY(), player.getZ(),
            player.getHeading(), 0, 0, 0);
        SpawnGroup spawnGroup = new SpawnGroup(player.getWorldId(), 700047, 295, 1);
        st.setSpawnGroup(spawnGroup);
        spawnGroup.getObjects().add(st);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                VisibleObject vo = SpawnEngine.getInstance().spawnObject(st, getInstanceId());

                if (vo instanceof Npc) {
                    Npc npc = (Npc) vo;

                    List<DropTemplate> dropTemplates = new ArrayList<DropTemplate>();
                    for (Item item : player.getAllItems())
                        if (!item.getItemTemplate().isStigma() && !item.getItemTemplate().isKinah())
                            dropTemplates.add(new DropTemplate(-1, item.getItemId(), 1, 1, 100));

                    while (dropTemplates.size() > 10)
                        dropTemplates.remove(Rnd.get(dropTemplates.size()));

                    DropService.getInstance().registerDrop(npc, dropTemplates, true);
                }
            }
        }, 1000);
    }

    public void onLeave(final Player player, boolean isLogout, boolean isAfk) {
        if (player.getBattleground() == null)
            return;
        
        if (player.isSpectating()) {
            onSpectatorLeave(player, false);
            return;
        }

        if (!isAfk) {
            List<Player> players = getPlayers();
            synchronized (players) {
                players.remove(player);
            }
        }

        player.setBattleground(null);

        endTimer(player);

        if (!player.getLifeStats().isAlreadyDead())
            resetSerialKiller(player);

        if (!isLogout) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    if (oldPlayerMap.containsKey(player))
                        PlayerService.forceRelog(player, oldPlayerMap.get(player));
                }
            }, 10000);
        }

        if (isStarted() && getRemainingPlayers() <= 1)
            endHungerGames(false);
    }

    private void endHungerGames(boolean isDraw) {
        if (endCalled)
            return;

        endCalled = true;

        for (Player pl : super.getPlayers())
            super.freezeNoEnd(pl);

        super.onEndFirstDefault();

        for (Player pl : super.getPlayers()) {
            super.scheduleAnnouncement(pl, "Event",
                "The Hunger Games are over... and you have survived!", 0);
            PvpService.getInstance().addMight(pl, 50);
        }

        super.specAnnounce("The Hunger Games are over...", "Event");

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                synchronized (getPlayers()) {
                    for (Player pl : getPlayers()) {
                        pl.setBattleground(null);
                        PlayerService.forceRelog(pl, oldPlayerMap.get(pl));
                    }

                    getPlayers().clear();
                }

                onEndDefault();
            }
        }, 15000);
    }
}