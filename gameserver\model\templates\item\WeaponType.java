/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.templates.item;

import gameserver.configs.main.GSConfig;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlType(name = "weapon_type")
@XmlEnum
public enum WeaponType {
    DAGGER_1H(new int[] { 30, 9 }, 1),
    MACE_1H(new int[] { 3, 10 }, 1),
    SWORD_1H(new int[] { 1, 8 }, 1),
    TOOLHOE_1H(new int[] {}, 1),
    BOOK_2H(new int[] { 64 }, 2),
    ORB_2H(new int[] { 64 }, 2),
    POLEARM_2H(new int[] { 16 }, 2),
    STAFF_2H(new int[] { 53 }, 2),
    SWORD_2H(new int[] { 15 }, 2),
    TOOLPICK_2H(new int[] {}, 2),
    TOOLROD_2H(new int[] {}, 2),
    BOW(new int[] { 17 }, 2),
    GUN_1H(new int[] { 76 }, 1),
    CANNON_2H(new int[] { 77 }, 2),
    HARP_2H(new int[] { 78 }, 2),
    KEYBLADE_2H(new int[] { 79 }, 2),
    KEYHAMMER(new int[] { 80 }, 2);

    private int[] requiredSkill;
    private int slots;

    private WeaponType(int[] requiredSkills, int slots) {
        if (requiredSkills.length == 2) {
            if (requiredSkills[0] == 30 && requiredSkills[1] == 9) // Dagger
                requiredSkills = new int[] { 38, 45 };
            else if (requiredSkills[0] == 3 && requiredSkills[1] == 10) // Mace
                requiredSkills = new int[] { 39, 46 };
            else if (requiredSkills[0] == 1 && requiredSkills[1] == 8) // Sword
                requiredSkills = new int[] { 37, 44 };
        }
        else if (requiredSkills.length == 1) {
            if (requiredSkills[0] == 64) // Book/Orb
                requiredSkills = new int[] { 100, 107, 111 };
            else if (requiredSkills[0] == 16) // Polearm
                requiredSkills = new int[] { 52 };
            else if (requiredSkills[0] == 53) // Staff
                requiredSkills = new int[] { 84, 89 };
            else if (requiredSkills[0] == 15) // Greatsword
                requiredSkills = new int[] { 51, 63 };
            else if (requiredSkills[0] == 17) // Bow
                requiredSkills = new int[] { 53, 67 };
            else if (requiredSkills[0] == 76) // Gun
                requiredSkills = new int[] { 112, 117 };
            else if (requiredSkills[0] == 77) // Cannon
                requiredSkills[0] = 113;
            else if (requiredSkills[0] == 78) // Harp
                requiredSkills[0] = 114;
            else if (requiredSkills[0] == 79) // Keyblade
                requiredSkills[0] = 115;
        }

        this.requiredSkill = requiredSkills;
        this.slots = slots;
    }

    public int[] getRequiredSkills() {
        return requiredSkill;
    }

    public int getRequiredSlots() {
        return slots;
    }

    /**
     * @return int
     */
    public int getMask() {
        return 1 << this.ordinal();
    }
}
