/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 *
 */
public class BasilikaCavernEvent extends MobEvent {
    public BasilikaCavernEvent() {
        super.mapId = 600030000;
        super.center = new SpawnPosition(1498, 1535, 16);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("An event at Basilika Cavern in Tiamaranta will commence in 2 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at Basilika Cavern starts in 1 minute", 1 * 60 * 1000);
        announceAll("The event at Basilika Cavern starts in 30 seconds", 30 * 1000 + 1
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 2 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters at Basilika Cavern in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217937, 1543, 1559, 17);
        spawnMob(217937, 1557, 1552, 17);
        spawnMob(217937, 1550, 1539, 17);
        spawnMob(217937, 1560, 1528, 17);
        spawnMob(217937, 1545, 1517, 17);
        spawnMob(217937, 1552, 1501, 17);
        spawnMob(217937, 1535, 1498, 17);
        spawnMob(217937, 1530, 1479, 17);
        spawnMob(217937, 1517, 1486, 17);
        spawnMob(217937, 1512, 1473, 17);
        spawnMob(217937, 1499, 1484, 17);
        spawnMob(217937, 1486, 1475, 17);
        spawnMob(217937, 1473, 1490, 17);
        spawnMob(217937, 1455, 1486, 17);
        spawnMob(217937, 1456, 1506, 17);
        spawnMob(217937, 1441, 1513, 17);
        spawnMob(217937, 1449, 1527, 17);
        spawnMob(217937, 1437, 1543, 17);
        spawnMob(217937, 1451, 1552, 17);
        spawnMob(217937, 1447, 1567, 17);
        spawnMob(217937, 1463, 1569, 17);
        spawnMob(217937, 1462, 1585, 17);
        spawnMob(217937, 1476, 1579, 17);
        spawnMob(217937, 1483, 1594, 17);
        spawnMob(217937, 1496, 1585, 17);
        spawnMob(217937, 1511, 1584, 17);
        spawnMob(217937, 1530, 1589, 17);
        spawnMob(217937, 1530, 1573, 17);
        spawnMob(217937, 1547, 1576, 17);
    }
}
