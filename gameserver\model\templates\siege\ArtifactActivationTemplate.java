/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.templates.siege;

import gameserver.skillengine.properties.TargetRelationAttribute;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ArtifactActivation")
public class ArtifactActivationTemplate {
    @XmlAttribute(name = "itemid")
    protected int itemId;
    @XmlAttribute(name = "count")
    protected int count;
    @XmlAttribute(name = "skill")
    protected int skill;
    @XmlAttribute(name = "cd")
    protected int cd;
    @XmlAttribute(name = "target")
    protected TargetRelationAttribute target;

    public int getItemId() {
        return itemId;
    }

    public int getCount() {
        return count;
    }

    public int getSkillId() {
        return skill;
    }

    public int getCd() {
        return cd;
    }

    public TargetRelationAttribute getTargetType() {
        return target;
    }
}
