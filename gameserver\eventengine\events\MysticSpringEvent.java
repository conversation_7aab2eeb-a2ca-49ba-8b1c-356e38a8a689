/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class MysticSpringEvent extends MobEvent {
    public MysticSpringEvent() {
        super.mapId = 210020000;
        super.center = new SpawnPosition(1215, 1622, 278);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("An event at the Mystic Spring in Eltnen will commence in 2 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at the Mystic Spring starts in 1 minute", 1 * 60 * 1000);
        announceAll("The event at the Mystic Spring starts in 30 seconds", 30 * 1000 + 1
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 2 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters at the Mystic Spring in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217079, 1236, 1614, 271);
        spawnMob(217079, 1237, 1624, 271);
        spawnMob(217079, 1233, 1637, 271);
        spawnMob(217079, 1222, 1643, 271);
        spawnMob(217079, 1205, 1643, 271);
        spawnMob(217079, 1195, 1634, 271);
        spawnMob(217079, 1191, 1622, 271);
        spawnMob(217079, 1197, 1609, 271);
        spawnMob(217079, 1207, 1603, 271);
        spawnMob(217079, 1209, 1581, 271);
        spawnMob(217079, 1216, 1581, 271);
        spawnMob(217079, 1252, 1591, 271);
        spawnMob(217079, 1259, 1596, 271);
        spawnMob(217079, 1266, 1622, 271);
        spawnMob(217079, 1263, 1631, 271);
        spawnMob(217079, 1250, 1658, 271);
        spawnMob(217079, 1242, 1660, 271);
        spawnMob(217079, 1224, 1686, 271);
        spawnMob(217079, 1223, 1695, 271);
        spawnMob(217079, 1187, 1687, 271);
        spawnMob(217079, 1180, 1680, 271);
        spawnMob(217079, 1172, 1652, 271);
        spawnMob(217079, 1170, 1630, 271);
        spawnMob(217079, 1165, 1622, 271);
        spawnMob(217079, 1167, 1605, 271);
        spawnMob(217079, 1174, 1588, 271);
        spawnMob(217079, 1188, 1585, 271);
        spawnMob(217079, 1206, 1581, 271);
        spawnMob(217079, 1216, 1581, 271);
    }
}
