-- Run this SQL in your not-aion database to create the shop_purchases table
-- This table is used by the game server's CashShopManager to process web purchases safely

CREATE TABLE IF NOT EXISTS `shop_purchases` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `char_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` bigint(20) NOT NULL DEFAULT '1',
  `gift` tinyint(1) NOT NULL DEFAULT '0',
  `gifter` varchar(50) DEFAULT '',
  `added` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `char_id` (`char_id`),
  <PERSON>EY `added` (`added`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Also create shop_removals table if it doesn't exist (used by the game server)
CREATE TABLE IF NOT EXISTS `shop_removals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `char_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` bigint(20) NOT NULL DEFAULT '1',
  `done` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `char_id` (`char_id`),
  KEY `done` (`done`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
