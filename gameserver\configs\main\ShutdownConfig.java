/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.configs.main;

import com.aionemu.commons.configuration.Property;

/**
 * <AUTHOR>
 */
public class ShutdownConfig {
    @Property(key = "gameserver.shutdown.mode", defaultValue = "1")
    public static int HOOK_MODE;

    @Property(key = "gameserver.shutdown.delay", defaultValue = "60")
    public static int HOOK_DELAY;

    @Property(key = "gameserver.shutdown.interval", defaultValue = "1")
    public static int ANNOUNCE_INTERVAL;

    @Property(key = "gameserver.shutdown.safereboot", defaultValue = "true")
    public static boolean SAFE_REBOOT;
}
