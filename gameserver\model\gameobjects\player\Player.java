/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.player;

import gameserver.configs.administration.AdminConfig;
import gameserver.configs.main.CustomConfig;
import gameserver.configs.main.PeriodicSaveConfig;
import gameserver.controllers.FlyController;
import gameserver.controllers.PlayerController;
import gameserver.controllers.ReviveController;
import gameserver.controllers.effect.PlayerEffectController;
import gameserver.controllers.instances.DredgionController;
import gameserver.dao.AbyssRankDAO;
import gameserver.dao.InventoryDAO;
import gameserver.dao.ItemStoneListDAO;
import gameserver.dao.MightDAO;
import gameserver.dao.PlayerDAO;
import gameserver.dao.PlayerQuestListDAO;
import gameserver.dao.PlayerSkillListDAO;
import gameserver.dao.PlayerWorldBanDAO;
import gameserver.dataholders.DataManager;
import gameserver.eventengine.Event;
import gameserver.model.BoundRadius;
import gameserver.model.Gender;
import gameserver.model.PlayerClass;
import gameserver.model.TaskId;
import gameserver.model.account.Account;
import gameserver.model.alliance.PlayerAlliance;
import gameserver.model.alliance.PlayerAllianceEvent;
import gameserver.model.alliance.PlayerAllianceGroup;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.Kisk;
import gameserver.model.gameobjects.Monster;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.PersistentState;
import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.gameobjects.state.CreatureVisualState;
import gameserver.model.gameobjects.stats.PlayerGameStats;
import gameserver.model.gameobjects.stats.PlayerLifeStats;
import gameserver.model.group.PlayerGroup;
import gameserver.model.items.ItemCooldown;
import gameserver.model.items.PackageItem;
import gameserver.model.legion.Legion;
import gameserver.model.legion.LegionMember;
import gameserver.model.pvpevents.Battleground;
import gameserver.model.siege.ArtifactProtector;
import gameserver.model.siege.FortressGeneral;
import gameserver.model.templates.stats.PlayerStatsTemplate;
import gameserver.network.aion.AionConnection;
import gameserver.network.aion.serverpackets.SM_ITEM_COOLDOWN;
import gameserver.network.aion.serverpackets.SM_SKILL_COOLDOWN;
import gameserver.questEngine.model.QuestCookie;
import gameserver.questEngine.model.QuestState;
import gameserver.questEngine.model.QuestStatus;
import gameserver.services.AllianceService;
import gameserver.services.ArenaService;
import gameserver.services.BrokerService;
import gameserver.services.ExchangeService;
import gameserver.services.GroupService;
import gameserver.services.PlayerService;
import gameserver.services.TeleportService.TeleportAnimation;
import gameserver.services.TownArenaService;
import gameserver.skillengine.task.CraftingTask;
import gameserver.taskmanager.tasks.MightUpdater;
import gameserver.utils.HumanTime;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.i18n.CustomMessageId;
import gameserver.utils.i18n.LanguageHandler;
import gameserver.utils.rates.Rates;
import gameserver.utils.rates.RegularRates;
import gameserver.world.World;
import gameserver.world.zone.ZoneInstance;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicInteger;

import org.apache.log4j.Logger;

import com.aionemu.commons.database.dao.DAOManager;
import com.aionemu.commons.utils.Rnd;

/**
 * This class is representing Player object, it contains all needed data.
 * 
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
public class Player extends Creature {
    public static final int CHAT_NOT_FIXED = 0;
    public static final int CHAT_FIXED_ON_WORLD = 1;
    public static final int CHAT_FIXED_ON_ELYOS = 2;
    public static final int CHAT_FIXED_ON_ASMOS = 4;
    public static final int CHAT_FIXED_ON_BOTH = CHAT_FIXED_ON_ELYOS | CHAT_FIXED_ON_ASMOS;

    private static final Logger log = Logger.getLogger(Player.class);

    private PlayerAppearance playerAppearance;
    private PlayerAppearance savedPlayerAppearance;
    private PlayerCommonData playerCommonData;
    private Account playerAccount;
    private LegionMember legionMember;
    private MacroList macroList;
    private SkillList skillList;
    private FriendList friendList;
    private BlockList blockList;
    private ResponseRequester requester;
    private boolean lookingForGroup = false;
    private Storage inventory;
    private Storage[] petBag = new Storage[10];
    private Storage regularWarehouse;
    private Storage accountWarehouse;
    private Equipment equipment;
    private Mailbox mailbox;
    private PrivateStore store;
    private PlayerStatsTemplate playerStatsTemplate;
    private TitleList titleList;
    private PlayerSettings playerSettings;
    private QuestStateList questStateList;
    private QuestCookie questCookie;
    private List<Integer> nearbyQuestList = new ArrayList<Integer>();
    private ZoneInstance zoneInstance;
    private PlayerGroup playerGroup;
    private AbyssRank abyssRank;
    private Rates rates;
    private RecipeList recipeList;
    private EmotionList emotionList;
    private MotionList motionList;
    private int flyState = 0;
    private boolean isTrading;
    private long prisonTimer = 0;
    private long startPrison;
    private boolean invul;
    private boolean protect;
    private FlyController flyController;
    private ReviveController reviveController;
    private CraftingTask craftingTask;
    private int flightTeleportId;
    private int flightDistance;
    private Summon summon;
    private Kisk kisk;
    private Prices prices;
    private boolean isGagged = false;
    private DredgionController dredgion = null;

    private boolean isWhisperable = true;
    private long lastZephyrInvokationSeconds = 0;
    private int zephyrObjectId = 0;
    private ToyPet toyPet;
    private boolean edit_mode = false;
    private int arenaKillStreak = 0;
    private int totalKills = 0;
    private int lastSkillId = 0;
    private boolean lastChainSuccess = false;
    private boolean usingObject = false;
    private AionObject useObject = null;
    private int shopCategoryId = 0;
    private boolean connectedChat = false;
    private Battleground battleground = null;
    private long lastAction = 0;
    private int bgIndex = 0;
    private boolean isSpectating = false;
    private long nextSummonSkillTime = 0;
    private int instancePlayerScore = 0;
    private int instancePlayerAp = 0;
    private int instancePvpKills = 0;
    private int instanceBalaurKills = 0;
    private int instanceCaptured = 0;
    private boolean inDredgion = false;
    private Event event = null;
    private boolean mentor = false;
    private boolean showTag = true;
    private boolean animationLogging = false;
    private Player animationLoggingTarget = null;
    private boolean packetLogging = false;
    private Player packetLoggingTarget = null;
    private boolean diffLogging = false;
    private Player diffLoggingTarget = null;
    private boolean lawless = false;
    private boolean outlaw = false;
    private boolean bandit = false;
    private boolean allFriend = false;
    private int windstreamId = 0;
    private int windstreamDistance = 0;
    private boolean in_arena = false;
    private boolean usingMount = false;
    private int mountId = 0;
    private boolean mountSprinting = false;
    private int polymorphId = 0;
    private int polymorphState = 0;
    private boolean inSafeZone = false;
    private boolean temporary = false;
    private int serialKiller = 0;
    private int serialGuard = 0;
    private boolean inZergingZone = false;
    private int robot = 0;
    private long blockMovementTimer = 0;
    private int activityCounter = 0;
    private int bonusIcon = 1;
    private AtomicInteger deltaMight = new AtomicInteger(0);
    private AtomicInteger cachedMight = new AtomicInteger(0);
    private long cachedMightTime = 0;
    private long nextSwitchHands = 0;
    private boolean isInFearGap = false;
    private boolean inMorphCancel = false;
    private float speedOverride = Float.NaN;
    private long nextRadarScan = 0;
    private TeleportAnimation teleportAnimation = TeleportAnimation.JUMP;
    private boolean combatLong = false;
    private long nextPotUse = 0;

    private List<PackageItem> currentPackage = null;

    private Map<Integer, ItemCooldown> itemCoolDowns;

    public int CHAT_FIX_WORLD_CHANNEL = CHAT_NOT_FIXED;
    private boolean bannedFromWorld = false;
    private String bannedFromWorldBy = "";
    private long bannedFromWorldDuring = 0;
    private Date bannedFromWorldDate = null;
    private String bannedFromWorldReason = "";
    private ScheduledFuture<?> taskToUnbanFromWorld = null;

    private Map<Integer, Motion> motions = new HashMap<Integer, Motion>();

    public long lastChat = 0;

    /**
     * Static information for players
     */
    private static final int CUBE_SPACE = 9;
    private static final int WAREHOUSE_SPACE = 8;

    /**
     * Connection of this Player.
     */
    private AionConnection clientConnection;

    private AionConnection secondConnection;

    public Player(PlayerController controller, PlayerCommonData plCommonData,
        PlayerAppearance appereance, Account account) {
        super(plCommonData.getPlayerObjId(), controller, null, plCommonData, plCommonData
            .getPosition());
        // TODO may be pcd->visibleObjectTemplate ?
        this.playerCommonData = plCommonData;
        this.playerAppearance = appereance;
        this.playerAccount = account;

        getCommonData().setBoundRadius(
            new BoundRadius(0.5f, 0.7f, getPlayerAppearance().getHeight()));

        this.prices = new Prices();
        this.requester = new ResponseRequester(this);
        this.questStateList = new QuestStateList();
        this.emotionList = new EmotionList();
        this.motionList = new MotionList();
        this.titleList = new TitleList();
        controller.setOwner(this);

        /*
         * Calendar cal = Calendar.getInstance(); cal.setTimeInMillis(System.currentTimeMillis()); if
         * (cal.get(Calendar.DAY_OF_MONTH) != 1 || cal.get(Calendar.MONTH) != 3) return; this.bonusIcon = Rnd.get(4, 9);
         */
    }

    public PlayerCommonData getCommonData() {
        return playerCommonData;
    }

    @Override
    public String getName() {
        return playerCommonData.getName();
    }

    public PlayerAppearance getPlayerAppearance() {
        return playerAppearance;
    }

    public void setPlayerAppearance(PlayerAppearance playerAppearance) {
        this.playerAppearance = playerAppearance;
    }

    /**
     * Only use for the Size admin command
     * 
     * @return PlayerAppearance : The saved player's appearance, to rollback his appearance
     */
    public PlayerAppearance getSavedPlayerAppearance() {
        return savedPlayerAppearance;
    }

    /**
     * Only use for the Size admin command
     * 
     * @param playerAppearance
     *            PlayerAppearance : The saved player's appearance, to rollback his appearance
     */
    public void setSavedPlayerAppearance(PlayerAppearance savedPlayerAppearance) {
        this.savedPlayerAppearance = savedPlayerAppearance;
    }

    /**
     * Set connection of this player.
     * 
     * @param clientConnection
     */
    public void setClientConnection(AionConnection clientConnection) {
        this.clientConnection = clientConnection;
    }

    /**
     * Get connection of this player.
     * 
     * @return AionConnection of this player.
     */
    public AionConnection getClientConnection() {
        return this.clientConnection;
    }

    public void setSecondConnection(AionConnection secondConnection) {
        this.secondConnection = secondConnection;
    }

    public AionConnection getSecondConnection() {
        return this.secondConnection;
    }

    public MacroList getMacroList() {
        return macroList;
    }

    public void setMacroList(MacroList macroList) {
        this.macroList = macroList;
    }

    public SkillList getSkillList() {
        return skillList;
    }

    public void setSkillList(SkillList skillList) {
        this.skillList = skillList;
    }

    /**
     * @return the toyPet
     */
    public ToyPet getToyPet() {
        return toyPet;
    }

    /**
     * @param toyPet
     *            the toyPet to set
     */
    public void setToyPet(ToyPet toyPet) {
        this.toyPet = toyPet;
    }

    /**
     * Gets this players Friend List
     * 
     * @return FriendList
     */
    public FriendList getFriendList() {
        return friendList;
    }

    /**
     * Is this player looking for a group
     * 
     * @return true or false
     */
    public boolean isLookingForGroup() {
        return lookingForGroup;
    }

    /**
     * Sets whether or not this player is looking for a group
     * 
     * @param lookingForGroup
     */
    public void setLookingForGroup(boolean lookingForGroup) {
        this.lookingForGroup = lookingForGroup;
    }

    /**
     * Sets this players friend list. <br />
     * Remember to send the player the <tt>SM_FRIEND_LIST</tt> packet.
     * 
     * @param list
     */
    public void setFriendList(FriendList list) {
        this.friendList = list;
    }

    public BlockList getBlockList() {
        return blockList;
    }

    public void setBlockList(BlockList list) {
        this.blockList = list;
    }

    /**
     * @return the playerLifeStats
     */
    @Override
    public PlayerLifeStats getLifeStats() {
        return (PlayerLifeStats) super.getLifeStats();
    }

    /**
     * @param lifeStats
     *            the lifeStats to set
     */
    public void setLifeStats(PlayerLifeStats lifeStats) {
        super.setLifeStats(lifeStats);
    }

    /**
     * @return the gameStats
     */
    @Override
    public PlayerGameStats getGameStats() {
        PlayerGameStats pgs = (PlayerGameStats) super.getGameStats();
        if (pgs == null)
            log.warn("Player.getGameStats() coud not be retrieved. " + "PlayerId: " + getObjectId()
                + ", PlayerName: " + getName());
        return pgs;
    }

    /**
     * @param gameStats
     *            the gameStats to set
     */
    public void setGameStats(PlayerGameStats gameStats) {
        super.setGameStats(gameStats);
    }

    /**
     * Gets the ResponseRequester for this player
     * 
     * @return ResponseRequester
     */
    public ResponseRequester getResponseRequester() {
        return requester;
    }

    public boolean isOnline() {
        return getClientConnection() != null;
    }

    public int getCubeSize() {
        return this.playerCommonData.getCubeSize();
    }

    public PlayerClass getPlayerClass() {
        return playerCommonData.getPlayerClass();
    }

    public Gender getGender() {
        return playerCommonData.getGender();
    }

    /**
     * Return PlayerController of this Player Object.
     * 
     * @return PlayerController.
     */
    @Override
    public PlayerController getController() {
        return (PlayerController) super.getController();
    }

    @Override
    public byte getLevel() {
        return (byte) playerCommonData.getLevel();
    }

    /**
     * @return the equipment
     */

    public Equipment getEquipment() {
        return equipment;
    }

    public void setEquipment(Equipment equipment) {
        this.equipment = equipment;
    }

    /**
     * @return the player private store
     */
    public PrivateStore getStore() {
        return store;
    }

    /**
     * @param store
     *            the store that needs to be set
     */
    public void setStore(PrivateStore store) {
        this.store = store;
    }

    /**
     * @return the questStatesList
     */
    public QuestStateList getQuestStateList() {
        return questStateList;
    }

    /**
     * @param questStateList
     *            the QuestStateList to set
     */
    public void setQuestStateList(QuestStateList questStateList) {
        this.questStateList = questStateList;
    }

    /**
     * @return the questCookie
     */
    public QuestCookie getQuestCookie() {
        return questCookie;
    }

    /**
     * @param questCookie
     *            the questCookie to set
     */
    public void setQuestCookie(QuestCookie questCookie) {
        this.questCookie = questCookie;
    }

    /**
     * @return the playerStatsTemplate
     */
    public PlayerStatsTemplate getPlayerStatsTemplate() {
        return playerStatsTemplate;
    }

    /**
     * @param playerStatsTemplate
     *            the playerStatsTemplate to set
     */
    public void setPlayerStatsTemplate(PlayerStatsTemplate playerStatsTemplate) {
        this.playerStatsTemplate = playerStatsTemplate;
    }

    public List<Integer> getNearbyQuests() {
        return nearbyQuestList;
    }

    public RecipeList getRecipeList() {
        return recipeList;
    }

    public void setRecipeList(RecipeList recipeList) {
        this.recipeList = recipeList;
    }

    /**
     * @param inventory
     *            the inventory to set Inventory should be set right after player object is created
     */
    public void setStorage(Storage storage, StorageType storageType) {
        if (storageType == StorageType.CUBE) {
            this.inventory = storage;
            inventory.setOwner(this);
        }

        if (storageType.getId() > 31 && storageType.getId() < 41) {
            this.petBag[storageType.getId() - 32] = storage;
        }

        if (storageType == StorageType.REGULAR_WAREHOUSE) {
            this.regularWarehouse = storage;
            regularWarehouse.setOwner(this);
        }

        if (storageType == StorageType.ACCOUNT_WAREHOUSE) {
            this.accountWarehouse = storage;
            accountWarehouse.setOwner(this);
        }
    }

    /**
     * @param storageType
     * @return
     */
    public Storage getStorage(int storageType) {
        if (storageType == StorageType.REGULAR_WAREHOUSE.getId())
            return regularWarehouse;

        if (storageType == StorageType.ACCOUNT_WAREHOUSE.getId())
            return accountWarehouse;

        if (storageType == StorageType.LEGION_WAREHOUSE.getId() && getLegion() != null)
            return getLegion().getLegionWarehouse();

        if (storageType > 31 && storageType < 41)
            return petBag[storageType - 32];

        if (storageType == StorageType.CUBE.getId())
            return inventory;
        else
            return null;
    }

    /**
     * Items from UPDATE_REQUIRED storages and equipment
     * 
     * @return
     */
    public List<Item> getDirtyItemsToUpdate() {
        List<Item> dirtyItems = new ArrayList<Item>();

        Storage cubeStorage = getStorage(StorageType.CUBE.getId());
        if (cubeStorage != null
            && cubeStorage.getPersistentState() == PersistentState.UPDATE_REQUIRED) {
            dirtyItems.addAll(cubeStorage.getAllItems());
            dirtyItems.addAll(cubeStorage.getDeletedItems());
            cubeStorage.setPersistentState(PersistentState.UPDATED);
        }

        Storage regularWhStorage = getStorage(StorageType.REGULAR_WAREHOUSE.getId());
        if (regularWhStorage != null
            && regularWhStorage.getPersistentState() == PersistentState.UPDATE_REQUIRED) {
            dirtyItems.addAll(regularWhStorage.getAllItems());
            dirtyItems.addAll(regularWhStorage.getDeletedItems());
            regularWhStorage.setPersistentState(PersistentState.UPDATED);
        }

        Storage accountWhStorage = getStorage(StorageType.ACCOUNT_WAREHOUSE.getId());
        if (accountWhStorage != null
            && accountWhStorage.getPersistentState() == PersistentState.UPDATE_REQUIRED) {
            dirtyItems.addAll(accountWhStorage.getAllItems());
            dirtyItems.addAll(accountWhStorage.getDeletedItems());
            accountWhStorage.setPersistentState(PersistentState.UPDATED);
        }

        Storage legionWhStorage = getStorage(StorageType.LEGION_WAREHOUSE.getId());
        if (legionWhStorage != null) {
            if (legionWhStorage.getPersistentState() == PersistentState.UPDATE_REQUIRED) {
                dirtyItems.addAll(legionWhStorage.getAllItems());
                dirtyItems.addAll(legionWhStorage.getDeletedItems());
                legionWhStorage.setPersistentState(PersistentState.UPDATED);
            }
        }

        for (int petBagId = 32; petBagId < 41; petBagId++) {
            Storage petBag = getStorage(petBagId);
            if (petBag != null && petBag.getPersistentState() == PersistentState.UPDATE_REQUIRED) {
                dirtyItems.addAll(petBag.getAllItems());
                dirtyItems.addAll(petBag.getDeletedItems());
                petBag.setPersistentState(PersistentState.UPDATED);
            }
        }

        Equipment equipment = getEquipment();
        if (equipment != null && equipment.getPersistentState() == PersistentState.UPDATE_REQUIRED) {
            dirtyItems.addAll(equipment.getEquippedItems());
            equipment.setPersistentState(PersistentState.UPDATED);
        }

        return dirtyItems;
    }

    /**
     * //TODO probably need to optimize here
     * 
     * @return
     */
    public List<Item> getAllItems() {
        List<Item> allItems = new ArrayList<Item>();

        Storage cubeStorage = getStorage(StorageType.CUBE.getId());
        if (cubeStorage != null)
            allItems.addAll(cubeStorage.getAllItems());

        Storage regularWhStorage = getStorage(StorageType.REGULAR_WAREHOUSE.getId());
        if (regularWhStorage != null)
            allItems.addAll(regularWhStorage.getStorageItems());

        Storage accountWhStorage = getStorage(StorageType.ACCOUNT_WAREHOUSE.getId());
        if (accountWhStorage != null)
            allItems.addAll(accountWhStorage.getStorageItems());

        Equipment equipment = getEquipment();
        if (equipment != null)
            allItems.addAll(equipment.getEquippedItems());

        return allItems;
    }

    public Storage getInventory() {
        if (inventory == null) {
            World world = World.getInstance();
            Player player = world.findPlayer(getName());
            if (player == null)
                return null;
            log.warn("Storage.getInventory(): Could not be restored for " + "playerId: "
                + player.getObjectId() + ", playerName: " + getName());
        }
        return inventory;
    }

    public void setInventory(Storage inventory) {
        this.inventory = inventory;
    }

    /**
     * @param CubeUpgrade
     *            int Sets the cubesize
     */
    public void setCubesize(int cubesize) {
        this.playerCommonData.setCubesize(cubesize);
        getInventory().setLimit(getInventory().getLimit() + (cubesize * CUBE_SPACE));
    }

    /**
     * @return the playerSettings
     */
    public PlayerSettings getPlayerSettings() {
        return playerSettings;
    }

    /**
     * @param playerSettings
     *            the playerSettings to set
     */
    public void setPlayerSettings(PlayerSettings playerSettings) {
        this.playerSettings = playerSettings;
    }

    /**
     * @return the zoneInstance
     */
    public ZoneInstance getZoneInstance() {
        return zoneInstance;
    }

    /**
     * @param zoneInstance
     *            the zoneInstance to set
     */
    public void setZoneInstance(ZoneInstance zoneInstance) {
        this.zoneInstance = zoneInstance;
    }

    public TitleList getTitleList() {
        return titleList;
    }

    public void setTitleList(TitleList titleList) {
        this.titleList = titleList;

        if (titleList != null)
            this.titleList.setOwner(this);
    }

    /**
     * @return the playerGroup
     */
    public PlayerGroup getPlayerGroup() {
        return playerGroup;
    }

    /**
     * @param playerGroup
     *            the playerGroup to set
     */
    public void setPlayerGroup(PlayerGroup playerGroup) {
        this.playerGroup = playerGroup;
    }

    /**
     * @return the abyssRank
     */
    public AbyssRank getAbyssRank() {
        return abyssRank;
    }

    /**
     * @param abyssRank
     *            the abyssRank to set
     */
    public void setAbyssRank(AbyssRank abyssRank) {
        this.abyssRank = abyssRank;
    }

    @Override
    public PlayerEffectController getEffectController() {
        return (PlayerEffectController) super.getEffectController();
    }

    @Override
    public void initializeAi() {
        // Empty
    }

    /**
     * <b><font color='red'>NOTICE: </font>this method is supposed to be called only from
     * {@link PlayerService#playerLoggedIn(Player)}</b>
     */
    public void onLoggedIn() {
        if (this.isTemporary())
            return;

        getController()
            .addTask(
                TaskId.PLAYER_UPDATE,
                ThreadPoolManager.getInstance().scheduleAtFixedRate(new GeneralUpdateTask(this),
                    PeriodicSaveConfig.PLAYER_GENERAL * 1000,
                    PeriodicSaveConfig.PLAYER_GENERAL * 1000));
        getController().addTask(
            TaskId.INVENTORY_UPDATE,
            ThreadPoolManager.getInstance().scheduleAtFixedRate(new ItemUpdateTask(this),
                PeriodicSaveConfig.PLAYER_ITEMS * 1000, PeriodicSaveConfig.PLAYER_ITEMS * 1000));

        // getCommonData().updateAbyssSkills(this, abyssRank);
        // update stat modifier -- called by CM_LEVEL_READY handler!
        // getCommonData().updateApModifiers();
    }

    /**
     * <b><font color='red'>NOTICE: </font>this method is supposed to be called only from
     * {@link PlayerService#playerLoggedOut(Player)}</b>
     */
    public void onLoggedOut() {
        // requester.denyAll();
        friendList.setStatus(FriendList.Status.OFFLINE);
        BrokerService.getInstance().removePlayerCache(this);
        ExchangeService.getInstance().cancelExchange(this);
    }

    /**
     * Returns true if has valid LegionMember
     */
    public boolean isLegionMember() {
        return legionMember != null;
    }

    /**
     * @param legionMember
     *            the legionMember to set
     */
    public void setLegionMember(LegionMember legionMember) {
        this.legionMember = legionMember;
    }

    /**
     * @return the legionMember
     */
    public LegionMember getLegionMember() {
        return legionMember;
    }

    /**
     * @return the legion
     */
    public Legion getLegion() {
        if (legionMember != null)
            return legionMember.getLegion();
        else
            return null;
    }

    /**
     * Checks if object id's are the same
     * 
     * @return true if the object id is the same
     */
    public boolean sameObjectId(int objectId) {
        return this.getObjectId() == objectId;
    }

    /**
     * @return true if a player has a store opened
     */
    public boolean hasStore() {
        if (getStore() != null)
            return true;
        return false;
    }

    /**
     * Removes legion from player
     */
    public void resetLegionMember() {
        setLegionMember(null);
    }

    /**
     * This method will return true if player is in a group
     * 
     * @return true or false
     */
    public boolean isInGroup() {
        return playerGroup != null;
    }

    /**
     * Access level of this player
     * 
     * @return byte
     */
    public byte getAccessLevel() {
        return playerAccount.getAccessLevel();
    }

    /**
     * accountName of this player
     * 
     * @return int
     */
    public String getAccountName() {
        return playerAccount.getName();
    }

    /**
     * @return the rates
     */
    public Rates getRates() {
        if (rates == null)
            rates = new RegularRates();
        return rates;
    }

    /**
     * @param rates
     *            the rates to set
     */
    public void setRates(Rates rates) {
        this.rates = rates;
    }

    /**
     * @return warehouse size
     */
    public int getWarehouseSize() {
        return this.playerCommonData.getWarehouseSize();
    }

    /**
     * @param warehouseSize
     */
    public void setWarehouseSize(int warehouseSize) {
        this.playerCommonData.setWarehouseSize(warehouseSize);
        getWarehouse().setLimit(getWarehouse().getLimit() + (warehouseSize * WAREHOUSE_SPACE));
    }

    /**
     * @return regularWarehouse
     */
    public Storage getWarehouse() {
        return regularWarehouse;
    }

    /**
     * @param regularWarehouse
     */
    public void setWarehouse(Storage regularWarehouse) {
        this.regularWarehouse = regularWarehouse;
    }

    /**
     * 0: regular, 1: fly, 2: glide
     */
    public int getFlyState() {
        return this.flyState;
    }

    public void setFlyState(int flyState) {
        this.flyState = flyState;
    }

    /**
     * @return the isTrading
     */
    public boolean isTrading() {
        return isTrading;
    }

    /**
     * @param isTrading
     *            the isTrading to set
     */
    public void setTrading(boolean isTrading) {
        this.isTrading = isTrading;
    }

    /**
     * @return the isInPrison
     */
    public boolean isInPrison() {
        return prisonTimer != 0;
    }

    /**
     * @param prisonTimer
     *            the prisonTimer to set
     */
    public void setPrisonTimer(long prisonTimer) {
        if (prisonTimer < 0)
            prisonTimer = 0;

        this.prisonTimer = prisonTimer;
    }

    /**
     * @return the prisonTimer
     */
    public long getPrisonTimer() {
        return prisonTimer;
    }

    /**
     * @return the time in ms of start prison
     */
    public long getStartPrison() {
        return startPrison;
    }

    /**
     * @param start
     *            : The time in ms of start prison
     */
    public void setStartPrison(long start) {
        this.startPrison = start;
    }

    /**
     * @return
     */
    public boolean isProtectionActive() {
        return isInVisualState(CreatureVisualState.BLINKING);
    }

    /**
     * Check is player is invul
     * 
     * @return boolean
     */
    public boolean isInvul() {
        return invul;
    }

    /**
     * Sets invul on player
     * 
     * @param invul
     *            - boolean
     */
    public void setInvul(boolean invul) {
        this.invul = invul;
    }

    /**
     * Check is player is Protected
     * 
     * @return boolean
     */
    public boolean isProtect() {
        return protect;
    }

    /**
     * Sets Protected on player
     * 
     * @param protect
     *            - boolean
     */
    public void setProtect(boolean protect) {
        this.protect = protect;
    }

    public void setMailbox(Mailbox mailbox) {
        this.mailbox = mailbox;
    }

    public Mailbox getMailbox() {
        return mailbox;
    }

    /**
     * @return the flyController
     */
    public FlyController getFlyController() {
        return flyController;
    }

    /**
     * @param flyController
     *            the flyController to set
     */
    public void setFlyController(FlyController flyController) {
        this.flyController = flyController;
    }

    public ReviveController getReviveController() {
        return reviveController;
    }

    public void setReviveController(ReviveController reviveController) {
        this.reviveController = reviveController;
    }

    public int getLastOnline() {
        Timestamp lastOnline = playerCommonData.getLastOnline();
        if (lastOnline == null || isOnline())
            return 0;

        return (int) (lastOnline.getTime() / 1000);
    }

    /**
     * @param craftingTask
     */
    public void setCraftingTask(CraftingTask craftingTask) {
        this.craftingTask = craftingTask;
    }

    /**
     * @return
     */
    public CraftingTask getCraftingTask() {
        return craftingTask;
    }

    /**
     * @param flightTeleportId
     */
    public void setFlightTeleportId(int flightTeleportId) {
        this.flightTeleportId = flightTeleportId;
    }

    /**
     * @return flightTeleportId
     */
    public int getFlightTeleportId() {
        return flightTeleportId;
    }

    /**
     * @param flightDistance
     */
    public void setFlightDistance(int flightDistance) {
        this.flightDistance = flightDistance;
    }

    /**
     * @return flightDistance
     */
    public int getFlightDistance() {
        return flightDistance;
    }

    /**
     * @return
     */
    public boolean isUsingFlyTeleport() {
        return isInState(CreatureState.FLIGHT_TELEPORT) && flightTeleportId != 0;
    }

    public boolean isInWindstream() {
        return isInState(CreatureState.FLIGHT_TELEPORT) && windstreamId != 0;
    }

    public boolean isGM() {
        return getAccessLevel() >= AdminConfig.GM_LEVEL;
    }

    /**
     * Npc enemies:<br>
     * - monsters<br>
     * - aggressive npcs<br>
     * 
     * @param npc
     * @return
     */
    @Override
    public boolean isEnemyNpc(Npc npc) {
        if (npc.getMaster() instanceof Player)
            return isEnemy((Player) npc.getMaster());

        if (this.getBattleground() != null && npc.getBgIndex() != -1) {
            if (this.isInGroup())
                return this.getPlayerGroup().getBgIndex() != npc.getBgIndex();
            else if (this.isInAlliance())
                return this.getPlayerAlliance().getBgIndex() != npc.getBgIndex();

            return this.getBgIndex() != npc.getBgIndex();
        }

        return npc instanceof Monster || npc.isAggressiveTo(this) || isHostileFrom(npc);
    }

    /**
     * Player enemies:<br>
     * - different race<br>
     * - duel partner<br>
     * 
     * @param player
     * @return
     */
    @Override
    public boolean isEnemyPlayer(Player player) {
        if (this.isSpectating() && !player.isSpectating())
            return true;
        else if (player.isSpectating())
            return false;

        if (player.getBattleground() != null) {
            if (this.isInAlliance() && player.isInAlliance()) {
                return this.getPlayerAlliance().getBgIndex() != player.getPlayerAlliance()
                    .getBgIndex();
            }
            else if (this.isInGroup() && player.isInGroup()) {
                return this.getPlayerGroup().getBgIndex() != player.getPlayerGroup().getBgIndex();
            }
            else {
                return (player.getObjectId() != this.getObjectId()); // true for all but you
            }
        }

        if (ArenaService.getInstance().isInArena(player)) {
            if (this.isInAlliance() && player.isInAlliance())
                return this.getPlayerAlliance() != player.getPlayerAlliance();
            else if (this.isInGroup() && player.isInGroup())
                return this.getPlayerGroup() != player.getPlayerGroup();

            return player.getObjectId() != this.getObjectId();
        }

        if (TownArenaService.getInstance().isInArena(player))
            return player.getObjectId() != this.getObjectId();

        if (player.isInSafeZone() || this.isInSafeZone())
            return getController().isDueling(player);

        if (!getController().isDueling(player)
            && ((this.isInGroup() && player.isInGroup() && this.getPlayerGroup().getGroupId() == player
                .getPlayerGroup().getGroupId()) || (this.isInAlliance() && player.isInAlliance() && this
                .getPlayerAlliance().getObjectId() == player.getPlayerAlliance().getObjectId())))
            return false;

        if (player.isLawless() || this.isLawless() || player.isOutlaw() || this.isOutlaw()
            || player.isBandit() || this.isBandit())
            return player.getObjectId() != this.getObjectId();
        else if (player.isAllFriend())
            return getController().isDueling(player);

        return (getAdminEnmity() > 1 || player.getAdminEnmity() > 1)
            && player.getObjectId() != getObjectId() ? true
            : player.getCommonData().getRace() != getCommonData().getRace()
                || getController().isDueling(player);
    }

    /**
     * Summon enemies:<br>
     * - master not null and master is enemy<br>
     */
    @Override
    public boolean isEnemySummon(Summon summon) {
        return summon.getMaster() != null && isEnemyPlayer(summon.getMaster());
    }

    /**
     * Player-player friends:<br>
     * - not in duel<br>
     * - same race<br>
     * 
     * @param player
     * @return
     */
    public boolean isFriend(Player player) {
        if (this.isSpectating() && !player.isSpectating())
            return false;
        else if (player.isSpectating())
            return true;

        if (player.getBattleground() != null) {
            if (this.isInAlliance() && player.isInAlliance()) {
                return this.getPlayerAlliance().getBgIndex() == player.getPlayerAlliance()
                    .getBgIndex();
            }
            else if (this.isInGroup() && player.isInGroup()) {
                return this.getPlayerGroup().getBgIndex() == player.getPlayerGroup().getBgIndex();
            }
            else {
                return player.getObjectId() == this.getObjectId(); // false for all but you
            }
        }

        if (ArenaService.getInstance().isInArena(player)) {
            if (this.isInAlliance() && player.isInAlliance())
                return this.getPlayerAlliance() == player.getPlayerAlliance();
            else if (this.isInGroup() && player.isInGroup())
                return this.getPlayerGroup() == player.getPlayerGroup();

            return player.getObjectId() == this.getObjectId();
        }

        if (TownArenaService.getInstance().isInArena(player))
            return player.getObjectId() == this.getObjectId();

        if (!getController().isDueling(player)
            && ((this.isInGroup() && player.isInGroup() && this.getPlayerGroup().getGroupId() == player
                .getPlayerGroup().getGroupId()) || (this.isInAlliance() && player.isInAlliance() && this
                .getPlayerAlliance().getObjectId() == player.getPlayerAlliance().getObjectId())))
            return true;

        if (player.isLawless() || this.isLawless() || player.isOutlaw() || this.isOutlaw()
            || player.isBandit() || this.isBandit())
            return player.getObjectId() == this.getObjectId();
        else if (player.isAllFriend())
            return !getController().isDueling(player);

        return player.getCommonData().getRace() == getCommonData().getRace()
            && !getController().isDueling(player);
    }

    @Override
    public String getTribe() {
        switch (getCommonData().getRace()) {
            case ELYOS:
                return "PC";
            default:
                return "PC_DARK";
        }
    }

    @Override
    public boolean isAggressiveTo(Creature creature) {
        return creature.isAggroFrom(this);
    }

    @Override
    public boolean isAggroFrom(Npc npc) {
        String currentTribe = npc.getTribe();
        if (npc instanceof FortressGeneral || npc instanceof ArtifactProtector)
            return true;

        if (isLawless() || isOutlaw() || isBandit())
            return true;

        // npc's that are 10 or more levels lower don't get aggro on players
        if (npc.getLevel() + 10 <= getLevel())
            return false;

        return getAdminEnmity() == 1 || getAdminEnmity() == 3 ? true : isAggroIconTo(currentTribe);
    }

    @Override
    public boolean isHostileFrom(Npc npc) {
        return isAggroIconTo(npc.getTribe())
            || DataManager.TRIBE_RELATIONS_DATA.isMonster(npc.getTribe());
    }

    /**
     * Used in SM_NPC_INFO to check aggro irrespective to level
     * 
     * @param npcTribe
     * @return
     */
    public boolean isAggroIconTo(String npcTribe) {
        if (getAdminEnmity() == 1 || getAdminEnmity() == 3)
            return true;

        if (isLawless() || isOutlaw() || isBandit())
            return true;

        switch (getCommonData().getRace()) {
            case ELYOS:
                if (DataManager.TRIBE_RELATIONS_DATA.isGuardDark(npcTribe))
                    return true;
                return DataManager.TRIBE_RELATIONS_DATA.isAggressiveRelation(npcTribe, "PC");
            case ASMODIANS:
                if (DataManager.TRIBE_RELATIONS_DATA.isGuardLight(npcTribe))
                    return true;
                return DataManager.TRIBE_RELATIONS_DATA.isAggressiveRelation(npcTribe, "PC_DARK");
        }
        return false;
    }

    @Override
    protected boolean canSeeNpc(Npc npc) {
        return true; // TODO
    }

    @Override
    protected boolean canSeePlayer(Player player) {
        return player.getVisualState() <= getSeeState();
    }

    /**
     * @return the summon
     */
    public Summon getSummon() {
        return summon;
    }

    /**
     * @param summon
     *            the summon to set
     */
    public void setSummon(Summon summon) {
        this.summon = summon;
    }

    /**
     * @param new kisk to bind to (null if unbinding)
     */
    public void setKisk(Kisk newKisk) {
        this.kisk = newKisk;
    }

    /**
     * @return
     */
    public Kisk getKisk() {
        return this.kisk;
    }

    /**
     * @param delayId
     * @return
     */
    public boolean isItemUseDisabled(int delayId) {
        if (itemCoolDowns == null || !itemCoolDowns.containsKey(delayId))
            return false;

        Long coolDown = itemCoolDowns.get(delayId).getReuseTime();
        if (coolDown == null)
            return false;

        if (coolDown < System.currentTimeMillis()) {
            itemCoolDowns.remove(delayId);
            return false;
        }

        return true;
    }

    /**
     * @return the itemCoolDowns
     */
    public Map<Integer, ItemCooldown> getItemCoolDowns() {
        return itemCoolDowns;
    }

    /**
     * @param delayId
     * @param time
     * @param useDelay
     */
    public void addItemCoolDown(int delayId, long time, int useDelay) {
        if (itemCoolDowns == null)
            itemCoolDowns = new ConcurrentHashMap<Integer, ItemCooldown>();

        itemCoolDowns.put(delayId, new ItemCooldown(time, useDelay));
    }

    /**
     * @param itemMask
     */
    public void removeItemCoolDown(int itemMask) {
        if (itemCoolDowns == null)
            return;
        itemCoolDowns.remove(itemMask);
    }

    /**
     * @return prices
     */
    public Prices getPrices() {
        return this.prices;
    }

    /**
     * @param isGagged
     *            the isGagged to set
     */
    public void setGagged(boolean isGagged) {
        this.isGagged = isGagged;
    }

    /**
     * @return the isGagged
     */
    public boolean isGagged() {
        return isGagged;
    }

    /**
     * @param isWhisperable
     *            the isWhisperable to set
     */
    public void setWhisperable(boolean isWhisperable) {
        this.isWhisperable = isWhisperable;
    }

    /**
     * @return the isWhisperable
     */
    public boolean isWhisperable() {
        return isWhisperable;
    }

    private static class GeneralUpdateTask implements Runnable {
        private Player player;

        private GeneralUpdateTask(Player player) {
            this.player = player;
        }

        @Override
        public void run() {
            if (player == null || player.isTemporary())
                return;

            try {
                DAOManager.getDAO(AbyssRankDAO.class).storeAbyssRank(player);
                DAOManager.getDAO(PlayerSkillListDAO.class).storeSkills(player);
                DAOManager.getDAO(PlayerQuestListDAO.class).store(player);
                DAOManager.getDAO(PlayerDAO.class).storePlayer(player);
            }
            catch (Exception ex) {
                log.error("Exception during periodic saving of player " + player.getName() + " "
                    + ex.getCause() != null ? ex.getCause().getMessage() : "null");
            }
        }
    }

    private static class ItemUpdateTask implements Runnable {
        private Player player;

        private ItemUpdateTask(Player player) {
            this.player = player;
        }

        @Override
        public void run() {
            try {
                if (!player.isTemporary()) {
                    DAOManager.getDAO(InventoryDAO.class).store(player);
                    DAOManager.getDAO(ItemStoneListDAO.class).save(player);
                }
            }
            catch (Exception ex) {
                log.error("Exception during periodic saving of player items " + player.getName()
                    + " " + ex.getCause() != null ? ex.getCause().getMessage() : "null");
            }
        }
    }

    public void setPlayerAlliance(PlayerAlliance playerAlliance) {
        this.playerAlliance = playerAlliance;
    }

    private PlayerAlliance playerAlliance;

    /**
     * @return
     */
    public PlayerAlliance getPlayerAlliance() {
        return playerAlliance;
    }

    /**
     * @return
     */
    public PlayerAllianceGroup getPlayerAllianceGroup() {
        if (!isInAlliance())
            return null;

        return playerAlliance.getPlayerAllianceGroupForMember(getObjectId());
    }

    /**
     * @return boolean value is in alliance
     */
    public boolean isInAlliance() {
        return (this.playerAlliance != null);
    }

    public static String getChanName(int chanId) {
        switch (chanId) {
            case CHAT_FIXED_ON_ASMOS:
                return LanguageHandler.translate(CustomMessageId.CHANNEL_NAME_ASMOS);
            case CHAT_FIXED_ON_ELYOS:
                return LanguageHandler.translate(CustomMessageId.CHANNEL_NAME_ELYOS);
            case CHAT_FIXED_ON_WORLD:
                return LanguageHandler.translate(CustomMessageId.CHANNEL_NAME_WORLD);
            case CHAT_FIXED_ON_BOTH:
                return LanguageHandler.translate(CustomMessageId.CHANNEL_NAME_BOTH);
        }
        return "";
    }

    public static String getChanCommand(int chanId) {
        switch (chanId) {
            case CHAT_FIXED_ON_ASMOS:
                return "." + LanguageHandler.translate(CustomMessageId.CHANNEL_COMMAND_ASMOS);
            case CHAT_FIXED_ON_ELYOS:
                return "." + LanguageHandler.translate(CustomMessageId.CHANNEL_COMMAND_ELYOS);
            case CHAT_FIXED_ON_WORLD:
                return "." + LanguageHandler.translate(CustomMessageId.CHANNEL_COMMAND_WORLD);
            case CHAT_FIXED_ON_BOTH:
                return "." + LanguageHandler.translate(CustomMessageId.CHANNEL_COMMAND_BOTH);
        }
        return "";
    }

    public boolean isBannedFromWorld() {
        return bannedFromWorld;
    }

    public void setBannedFromWorld(String by, String reason, long duration, Date date) {
        bannedFromWorld = true;
        bannedFromWorldBy = by;
        bannedFromWorldDate = date;
        bannedFromWorldDuring = duration;
        bannedFromWorldReason = reason;
    }

    public boolean banFromWorld(String by, String reason, long duration) {
        if (isBannedFromWorld()) {
            return false;
        }
        else {
            bannedFromWorld = true;
            bannedFromWorldDate = Calendar.getInstance().getTime();
            bannedFromWorldDuring = duration;
            bannedFromWorldBy = by;
            bannedFromWorldReason = reason;
            PlayerWorldBanDAO dao = DAOManager.getDAO(PlayerWorldBanDAO.class);
            if (!dao.addWorldBan(getObjectId(), by, bannedFromWorldDuring, bannedFromWorldDate,
                bannedFromWorldReason)) {
                return false;
            }

            if (bannedFromWorldDuring > 0) {
                scheduleUnbanFromWorld();
            }
        }
        return true;
    }

    public boolean unbanFromWorld() {
        bannedFromWorld = false;
        PlayerWorldBanDAO dao = DAOManager.getDAO(PlayerWorldBanDAO.class);
        cancelUnbanFromWorld();
        dao.removeWorldBan(getObjectId());
        return true;
    }

    public void scheduleUnbanFromWorld() {
        if (!isBannedFromWorld()) {
            throw new RuntimeException("scheduling unban task when not banned from "
                + getChanCommand(CHAT_FIX_WORLD_CHANNEL));
        }
        cancelUnbanFromWorld();
        final int playerObjId = getObjectId();
        final String playerName = getName();
        final String adminName = bannedFromWorldBy;
        final long time = bannedFromWorldDuring;
        if (time > 0) {
            final Date endDate = new Date(bannedFromWorldDate.getTime() + bannedFromWorldDuring);
            taskToUnbanFromWorld = ThreadPoolManager.getInstance().schedule(new Runnable() {
                public void run() {
                    World world = World.getInstance();
                    Player player = world.findPlayer(playerName);
                    Player admin = world.findPlayer(adminName);
                    if (endDate.getTime() <= Calendar.getInstance().getTimeInMillis()) {
                        DAOManager.getDAO(PlayerWorldBanDAO.class).removeWorldBan(playerObjId);
                    }
                    if (player != null) {
                        player.bannedFromWorld = false;
                        PacketSendUtility.sendSysMessage(player,
                            LanguageHandler.translate(CustomMessageId.CHANNEL_BAN_ENDED));
                    }
                    if (admin != null) {
                        PacketSendUtility.sendSysMessage(admin, LanguageHandler.translate(
                            CustomMessageId.CHANNEL_BAN_ENDED_FOR, playerName));
                    }
                }
            }, time);
        }
    }

    private void cancelUnbanFromWorld() {
        if (taskToUnbanFromWorld != null) {
            taskToUnbanFromWorld.cancel(false);
            taskToUnbanFromWorld = null;
        }
    }

    public String getBannedFromWorldBy() {
        return bannedFromWorldBy;
    }

    public String getBannedFromWorldReason() {
        return bannedFromWorldReason;
    }

    public String getBannedFromWorldRemainingTime() {
        long elapsed = 0;
        if (bannedFromWorldDuring == 0) {
            return "undetermined";
        }
        else {
            elapsed = bannedFromWorldDuring
                - (Calendar.getInstance().getTimeInMillis() - bannedFromWorldDate.getTime());
            return HumanTime.approximately(elapsed - (elapsed % 1000));
        }
    }

    public Account getPlayerAccount() {
        return playerAccount;
    }

    public long getLastZephyrInvokationSeconds() {
        return lastZephyrInvokationSeconds;
    }

    public void setLastZephyrInvokationSeconds(long seconds) {
        this.lastZephyrInvokationSeconds = seconds;
    }

    /**
     * @return the zephyrObjectId
     */
    public int getZephyrObjectId() {
        return zephyrObjectId;
    }

    /**
     * @param zephyrObjectId
     *            the zephyrObjectId to set
     */
    public void setZephyrObjectId(int zephyrObjectId) {
        this.zephyrObjectId = zephyrObjectId;
    }

    /**
     * @param editmode
     */
    public void setEditMode(boolean edit_mode) {
        this.edit_mode = edit_mode;
    }

    /**
     * @return editmode
     */
    public boolean isInEditMode() {
        return edit_mode;
    }

    private boolean noExperienceGain = false;

    public int getKillStreak() {
        return arenaKillStreak;
    }

    public void setKillStreak(int killStreak) {
        arenaKillStreak = killStreak;
    }

    public void setNoExperienceGain(boolean noExperienceGain) {
        this.noExperienceGain = noExperienceGain;
    }

    /**
     * @return
     */
    public boolean isNoExperienceGain() {
        return noExperienceGain;
    }

    /**
     * @return KinahAmount
     */
    public long getKinah() {
        return inventory.getKinahItem().getItemCount();
    }

    /**
     * This method will increase the kinah amount of a player
     * 
     * @param price
     */
    public void addKinah(long price) {
        inventory.increaseKinah(price);
    }

    /**
     * This method will decrease the kinah amount of a player
     * 
     * @param price
     */
    public void removeKinah(long price) {
        inventory.decreaseKinah(price);
    }

    /**
     * isQuestComplete
     * 
     * @param questId
     * @param return boolean value if Quest Complete or not.
     */
    public boolean isQuestComplete(int questId) {
        QuestState questState = questStateList.getQuestState(questId);
        if (questState == null)
            return false;
        return (questState.getStatus() == QuestStatus.COMPLETE);
    }

    /**
     * isQuestStart
     * 
     * @param questId
     * @param return boolean value if Quest Started or not.
     */
    public boolean isQuestStart(int questId) {
        QuestState questState = questStateList.getQuestState(questId);
        if (questState == null)
            return false;
        return (questState.getStatus() == QuestStatus.START);
    }

    /**
     * @param lastSkillId
     *            the lastSkillId to set
     */
    public void setLastSkillId(int lastSkillId) {
        this.lastSkillId = lastSkillId;
    }

    /**
     * @return the lastSkillId
     */
    public int getLastSkillId() {
        return lastSkillId;
    }

    /**
     * @param lastChainSuccess
     *            the lastChainSuccess to set
     */
    public void setLastChainSuccess(boolean lastChainSuccess) {
        this.lastChainSuccess = lastChainSuccess;
    }

    /**
     * @return the lastChainSuccess
     */
    public boolean isLastChainSuccess() {
        return lastChainSuccess;
    }

    /**
     * @param usingObject
     *            the usingObject to set
     */
    public void setUsingObject(boolean usingObject) {
        this.usingObject = usingObject;
    }

    /**
     * @return the usingObject
     */
    public boolean isUsingObject() {
        return usingObject;
    }

    /**
     * @param useItem
     *            the useItem to set
     */
    public void setUseObject(AionObject useObject) {
        this.useObject = useObject;
    }

    /**
     * @return the useItem
     */
    public AionObject getUseObject() {
        return useObject;
    }

    /**
     * @param shopCategoryId
     *            the shopCategoryId to set
     */
    public void setShopCategoryId(int shopCategoryId) {
        this.shopCategoryId = shopCategoryId;
    }

    /**
     * @return the shopCategoryId
     */
    public int getShopCategoryId() {
        return shopCategoryId;
    }

    /**
     * @param connectedChat
     *            the connectedChat to set
     */
    public void setConnectedChat(boolean connectedChat) {
        this.connectedChat = connectedChat;
    }

    /**
     * @return the connectedChat
     */
    public boolean isConnectedChat() {
        return connectedChat;
    }

    /**
     * @param battleground
     *            the battleground to set
     */
    public void setBattleground(Battleground battleground) {
        this.battleground = battleground;
    }

    /**
     * @return the battleground
     */
    public Battleground getBattleground() {
        return battleground;
    }

    /**
     * @param totalKills
     *            the totalKills to set
     */
    public void setTotalKills(int totalKills) {
        this.totalKills = totalKills;
    }

    /**
     * @return the totalKills
     */
    public int getTotalKills() {
        return totalKills;
    }

    /**
     * Sets timestamp of last action
     */
    public void setLastAction() {
        this.lastAction = System.currentTimeMillis();
    }

    /**
     * @return the lastAction
     */
    public long getLastAction() {
        return lastAction;
    }

    /**
     * @param bgIndex
     *            the bgIndex to set
     */
    public void setBgIndex(int bgIndex) {
        this.bgIndex = bgIndex;
    }

    /**
     * @return the bgIndex
     */
    public int getBgIndex() {
        return bgIndex;
    }

    /**
     * @param isSpectating
     *            the isSpectating to set
     */
    public void setSpectating(boolean isSpectating) {
        this.isSpectating = isSpectating;
    }

    /**
     * @return the isSpectating
     */
    public boolean isSpectating() {
        return isSpectating;
    }

    /**
     * @param nextSummonSkillTime
     *            the nextSummonSkillTime to set
     */
    public void setNextSummonSkillTime(long nextSummonSkillTime) {
        this.nextSummonSkillTime = nextSummonSkillTime;
    }

    /**
     * @return the nextSummonSkillTime
     */
    public long getNextSummonSkillTime() {
        return nextSummonSkillTime;
    }

    /**
     * @param instancePlayerScore
     *            the instancePlayerScore to set
     */
    public void setInstancePlayerScore(int instancePlayerScore) {
        this.instancePlayerScore = instancePlayerScore;
    }

    /**
     * @return the instancePlayerScore
     */
    public int getInstancePlayerScore() {
        return instancePlayerScore;
    }

    /**
     * @param instancePlayerAp
     *            the instancePlayerAp to set
     */
    public void setInstancePlayerAp(int instancePlayerAp) {
        this.instancePlayerAp = instancePlayerAp;
    }

    /**
     * @return the instancePlayerAp
     */
    public int getInstancePlayerAp() {
        return instancePlayerAp;
    }

    /**
     * @param instancePvpKills
     *            the instancePvpKills to set
     */
    public void setInstancePvpKills(int instancePvpKills) {
        this.instancePvpKills = instancePvpKills;
    }

    /**
     * @return the instancePvpKills
     */
    public int getInstancePvpKills() {
        return instancePvpKills;
    }

    /**
     * @param instanceBalaurKills
     *            the instanceBalaurKills to set
     */
    public void setInstanceBalaurKills(int instanceBalaurKills) {
        this.instanceBalaurKills = instanceBalaurKills;
    }

    /**
     * @return the instanceBalaurKills
     */
    public int getInstanceBalaurKills() {
        return instanceBalaurKills;
    }

    /**
     * @param instanceCaptured
     *            the instanceCaptured to set
     */
    public void setInstanceCaptured(int instanceCaptured) {
        this.instanceCaptured = instanceCaptured;
    }

    /**
     * @return the instanceCaptured
     */
    public int getInstanceCaptured() {
        return instanceCaptured;
    }

    /**
     * @param inDredgion
     *            the inDredgion to set
     */
    public void setInDredgion(boolean inDredgion) {
        this.inDredgion = inDredgion;
    }

    /**
     * @return the inDredgion
     */
    public boolean getInDredgion() {
        return inDredgion;
    }

    /**
     * @param event
     *            the event to set
     */
    public void setEvent(Event event) {
        this.event = event;
    }

    /**
     * @return the event
     */
    public Event getEvent() {
        return event;
    }

    /**
     * @param mentor
     *            the mentor to set
     */
    public void setMentor(boolean mentor) {
        this.mentor = mentor;
    }

    /**
     * @return the mentor
     */
    public boolean isMentor() {
        return mentor;
    }

    /**
     * @param dredgion
     *            the dredgion to set
     */
    public void setDredgion(DredgionController dredgion) {
        this.dredgion = dredgion;
    }

    /**
     * @return the dredgion
     */
    public DredgionController getDredgion() {
        return dredgion;
    }

    public EmotionList getEmotionList() {
        return emotionList;
    }

    public void setEmotionList(EmotionList emotionList) {
        this.emotionList = emotionList;

        if (emotionList != null)
            this.emotionList.setOwner(this);
    }

    public void setMotionList(MotionList motionList) {
        this.motionList = motionList;

        if (motionList != null)
            this.motionList.setOwner(this);
    }

    public MotionList getMotionList() {
        return motionList;
    }

    /**
     * @param motions
     *            the motions to set
     */
    public void setMotions(Map<Integer, Motion> motions) {
        this.motions = motions;
    }

    /**
     * @return the motions
     */
    public Map<Integer, Motion> getMotions() {
        return motions;
    }

    public void setShowTag(boolean showTag) {
        this.showTag = showTag;
    }

    public boolean showTag() {
        return showTag;
    }

    /**
     * @param animationLogging
     *            the animationLogging to set
     */
    public void setAnimationLogging(boolean animationLogging) {
        this.animationLogging = animationLogging;
    }

    /**
     * @return the animationLogging
     */
    public boolean isAnimationLogging() {
        return animationLogging;
    }

    /**
     * @param animationLoggingTarget
     *            the animationLoggingTarget to set
     */
    public void setAnimationLoggingTarget(Player animationLoggingTarget) {
        this.animationLoggingTarget = animationLoggingTarget;
    }

    /**
     * @return the animationLoggingTarget
     */
    public Player getAnimationLoggingTarget() {
        return animationLoggingTarget;
    }

    /**
     * @see gameserver.model.gameobjects.Creature#resetSkillCoolDowns()
     */
    @Override
    public void resetSkillCoolDowns() {
        if (skillCoolDowns != null) {
            skillCoolDowns.clear();

            List<Integer> skills = new ArrayList<Integer>(getSkillList().getAllSkills().length);

            for (SkillListEntry skillEntry : getSkillList().getAllSkills())
                skills.add(skillEntry.getSkillId());

            PacketSendUtility.sendPacket(this, new SM_SKILL_COOLDOWN(skills));
        }
    }

    public void resetItemCoolDowns() {
        if (itemCoolDowns != null) {
            Map<Integer, ItemCooldown> items = new HashMap<Integer, ItemCooldown>();

            items.putAll(itemCoolDowns);

            itemCoolDowns.clear();

            for (ItemCooldown cd : items.values()) {
                cd.setReuseTime(System.currentTimeMillis() + cd.getUseDelay() * 1000);
                cd.setUseDelay(0);
            }

            PacketSendUtility.sendPacket(this, new SM_ITEM_COOLDOWN(items));
        }
    }

    public void setPacketLogging(boolean packetLogging) {
        this.packetLogging = packetLogging;
    }

    public boolean isPacketLogging() {
        return packetLogging;
    }

    public void setPacketLoggingTarget(Player packetLoggingTarget) {
        this.packetLoggingTarget = packetLoggingTarget;
    }

    public Player getPacketLoggingTarget() {
        return packetLoggingTarget;
    }

    public boolean isLawless() {
        return lawless;
    }

    public void setLawless(boolean lawless) {
        this.lawless = lawless;

        if (lawless) {
            if (isInGroup())
                GroupService.getInstance().removePlayerFromGroup(this, false);
            if (isInAlliance())
                AllianceService.getInstance().removeMemberFromAlliance(getPlayerAlliance(),
                    getObjectId(), PlayerAllianceEvent.LEAVE);
        }
    }

    public boolean isOutlaw() {
        return outlaw;
    }

    public void setOutlaw(boolean outlaw) {
        this.outlaw = outlaw;

        if (outlaw) {
            if (isInGroup())
                GroupService.getInstance().removePlayerFromGroup(this, false);
            if (isInAlliance())
                AllianceService.getInstance().removeMemberFromAlliance(getPlayerAlliance(),
                    getObjectId(), PlayerAllianceEvent.LEAVE);
        }
    }

    public boolean isBandit() {
        return bandit;
    }

    public void setBandit(boolean bandit) {
        this.bandit = bandit;
    }

    public boolean isAllFriend() {
        return allFriend;
    }

    public void setAllFriend(boolean allFriend) {
        this.allFriend = allFriend;
    }

    public int getWindstreamId() {
        return windstreamId;
    }

    public void setWindstreamId(int windstreamId) {
        this.windstreamId = windstreamId;
    }

    public int getWindstreamDistance() {
        return windstreamDistance;
    }

    public void setWindstreamDistance(int windstreamDistance) {
        this.windstreamDistance = windstreamDistance;
    }

    /**
     * @return in_arena
     */
    public boolean getInArena() {
        return in_arena;
    }

    public void setInArena(boolean in_arena) {
        this.in_arena = in_arena;
    }

    /**
     * @return the usingMount
     */
    public boolean isUsingMount() {
        return usingMount;
    }

    /**
     * @param usingMount
     *            the usingMount to set
     */
    public void setUsingMount(boolean usingMount) {
        this.usingMount = usingMount;
    }

    /**
     * @return the mountId
     */
    public int getMountId() {
        return mountId;
    }

    /**
     * @param mountId
     *            the mountId to set
     */
    public void setMountId(int mountId) {
        this.mountId = mountId;
    }

    /**
     * @return the mountSprinting
     */
    public boolean isMountSprinting() {
        return mountSprinting;
    }

    /**
     * @param mountSprinting
     *            the mountSprinting to set
     */
    public void setMountSprinting(boolean mountSprinting) {
        this.mountSprinting = mountSprinting;
    }

    /**
     * @return the polymorphId
     */
    public int getPolymorphId() {
        return polymorphId;
    }

    /**
     * @param polymorphId
     *            the polymorphId to set
     */
    public void setPolymorphId(int polymorphId) {
        this.polymorphId = polymorphId;
    }

    /**
     * @return polymorphId != 0
     */
    public boolean isPolymorphed() {
        return polymorphId != 0;
    }

    /**
     * @return the polymorphState
     */
    public int getPolymorphState() {
        return polymorphState;
    }

    /**
     * @param polymorphState
     *            the polymorphState to set
     */
    public void setPolymorphState(int polymorphState) {
        this.polymorphState = polymorphState;
    }

    /**
     * @return state
     */
    public int getState() {
        if (isUsingMount()) {
            int state = 4;

            if (isInState(CreatureState.FLYING)) {
                state |= CreatureState.FLYING.getId();
                state |= 8;
            }
            if (isInState(CreatureState.POWERSHARD))
                state |= CreatureState.POWERSHARD.getId();
            if (isInState(CreatureState.GLIDING))
                state |= CreatureState.GLIDING.getId();

            return state;
        }
        return super.getState();
    }

    /**
     * @return the inSafeZone
     */
    public boolean isInSafeZone() {
        return inSafeZone;
    }

    /**
     * @param inSafeZone
     *            the inSafeZone to set
     */
    public void setInSafeZone(boolean inSafeZone) {
        this.inSafeZone = inSafeZone;
    }

    /**
     * @return the temporary
     */
    public boolean isTemporary() {
        return temporary;
    }

    /**
     * @param temporary
     *            the temporary to set
     */
    public void setTemporary(boolean temporary) {
        this.temporary = temporary;
    }

    /**
     * @return the serialKiller
     */
    public int getSerialKillerLevel() {
        return serialKiller;
    }

    /**
     * @param serialKiller
     *            the serialKiller to set
     */
    public void setSerialKiller(int serialKiller) {
        this.serialKiller = serialKiller;
    }

    /**
     * @return the inZergingZone
     */
    public boolean isInZergingZone() {
        return inZergingZone;
    }

    /**
     * @param inZergingZone
     *            the inZergingZone to set
     */
    public void setInZergingZone(boolean inZergingZone) {
        this.inZergingZone = inZergingZone;
    }

    /**
     * @return the robot
     */
    public int getRobot() {
        if (robot != 0
            && ((this.getBattleground() != null && !this.isSpectating()
                && !this.getBattleground().is1v1() && this.getBattleground().isAnonymous()) || ArenaService
                .getInstance().isInArena(this)))
            return Rnd.get(2500008, 2500042);

        return robot;
    }

    /**
     * @param robot
     *            the robot to set
     */
    public void setRobot(int robot) {
        this.robot = robot;
    }

    /**
     * @return isRidingRobot
     */
    public boolean isRidingRobot() {
        return robot != 0;
    }

    /**
     * @return the blockMovementTimer
     */
    public boolean isMovementBlocked() {
        return blockMovementTimer > System.currentTimeMillis();
    }

    /**
     * @param blockMovementTimer
     *            the blockMovementTimer to set
     */
    public void setBlockMovementTimer(long blockMovementTimer) {
        this.blockMovementTimer = System.currentTimeMillis() + blockMovementTimer;
    }

    /**
     * @return the activityCounter
     */
    public int getActivityCounter() {
        return activityCounter;
    }

    /**
     * @param activityCounter
     *            the activityCounter to set
     */
    public void setActivityCounter(int activityCounter) {
        this.activityCounter = activityCounter;
    }

    /**
     * @param amount
     *            the amount to increment
     */
    public void incrementActivityCounter(int amount) {
        if (this.battleground == null)
            return;

        this.activityCounter += amount;
    }

    /**
     * @return the bonusIcon
     */
    public int getBonusIcon() {
        return bonusIcon;
    }

    /**
     * @param bonusIcon
     *            the bonusIcon to set
     */
    public void setBonusIcon(int bonusIcon) {
        this.bonusIcon = bonusIcon;
    }

    /**
     * @return the currentPackage
     */
    public List<PackageItem> getCurrentPackage() {
        return currentPackage;
    }

    /**
     * @param currentPackage
     *            the currentPackage to set
     */
    public void setCurrentPackage(List<PackageItem> currentPackage) {
        this.currentPackage = currentPackage;
    }

    public int getDeltaMight() {
        return deltaMight.get();
    }

    public void addMight(int might) {
        deltaMight.addAndGet(might);
        cachedMight.addAndGet(might);
        MightUpdater.getInstance().startTask(this);
    }

    public int getAndClearMight() {
        return deltaMight.getAndSet(0);
    }

    public int getCachedMight() {
        return cachedMight.get();
    }

    public void setCachedMight(int cachedMight) {
        this.cachedMight.set(cachedMight);
    }

    public int getMight() {
        if (System.currentTimeMillis() > this.cachedMightTime + 2000) {
            this.cachedMightTime = System.currentTimeMillis();
            this.cachedMight.set(DAOManager.getDAO(MightDAO.class).getMight(this));
        }

        return cachedMight.get();
    }

    public long getNextSwitchHands() {
        return nextSwitchHands;
    }

    public void setNextSwitchHands(long nextSwitchHands) {
        this.nextSwitchHands = nextSwitchHands;
    }

    public boolean isInFearGap() {
        return isInFearGap;
    }

    public void setInFearGap(boolean isInFearGap) {
        this.isInFearGap = isInFearGap;
    }

    public Player getDiffLoggingTarget() {
        return diffLoggingTarget;
    }

    public void setDiffLoggingTarget(Player diffLoggingTarget) {
        this.diffLoggingTarget = diffLoggingTarget;
    }

    public boolean isDiffLogging() {
        return diffLogging;
    }

    public void setDiffLogging(boolean diffLogging) {
        this.diffLogging = diffLogging;
    }

    public String getTagName() {
        return getTagName(false);
    }

    public String getTagName(boolean alwaysTag) {
        if (getAccessLevel() <= 0)
            return getName();
        
        String playerName = "";

        if (CustomConfig.GMTAG_DISPLAY && (alwaysTag || showTag())) {
            if (getAccessLevel() == 1) {
                playerName += CustomConfig.GM_LEVEL1.trim();
            }
            else if (getAccessLevel() == 2) {
                playerName += CustomConfig.GM_LEVEL2.trim();
            }
            else if (getAccessLevel() == 3) {
                playerName += CustomConfig.GM_LEVEL3.trim();
            }
            else if (getAccessLevel() == 4) {
                playerName += CustomConfig.GM_LEVEL4.trim();
            }
            else if (getAccessLevel() == 5) {
                playerName += CustomConfig.GM_LEVEL5.trim();
            }
            else if (getAccessLevel() == 6) {
                playerName += CustomConfig.GM_LEVEL6.trim();
            }
            else if (getAccessLevel() == 7) {
                playerName += CustomConfig.GM_LEVEL7.trim();
            }
            else if (getAccessLevel() == 8) {
                playerName += CustomConfig.GM_LEVEL8.trim();
            }
            else if (getAccessLevel() == 9) {
                playerName += CustomConfig.GM_LEVEL9.trim();
            }
            else if (getAccessLevel() == 10) {
                playerName += CustomConfig.GM_LEVEL10.trim();
            }
        }

        playerName += getName();

        return playerName;
    }

    public boolean isInMorphCancel() {
        return inMorphCancel;
    }

    public void setInMorphCancel(boolean inMorphCancel) {
        this.inMorphCancel = inMorphCancel;
    }

    /**
     * @return the serialGuard
     */
    public int getSerialGuardLevel() {
        return serialGuard;
    }

    /**
     * @param serialGuard
     *            the serialGuard to set
     */
    public void setSerialGuard(int serialGuard) {
        this.serialGuard = serialGuard;
    }

    /**
     * @return the speedOverride
     */
    public float getSpeedOverride() {
        return speedOverride;
    }

    /**
     * @param speedOverride
     *            the speedOverride to set
     */
    public void setSpeedOverride(float speedOverride) {
        this.speedOverride = speedOverride;
    }

    /**
     * @return the nextRadarScan
     */
    public long getNextRadarScan() {
        return nextRadarScan;
    }

    /**
     * @param nextRadarScan
     *            the nextRadarScan to set
     */
    public void setNextRadarScan(long nextRadarScan) {
        this.nextRadarScan = nextRadarScan;
    }

    public TeleportAnimation getTeleportAnimation() {
        return teleportAnimation;
    }

    public void setTeleportAnimation(TeleportAnimation teleportAnimation) {
        this.teleportAnimation = teleportAnimation;
    }

    public boolean isInCombatLong() {
        return combatLong;
    }

    @Override
    public void setCombatState(int time) {
        super.setCombatState(time);

        setInCombatLong(true);

        Future<?> task = ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (isInCombatLong())
                    setInCombatLong(false);
            }
        }, 20 * 1000);

        this.getController().addTask(TaskId.PLAYER_COMBAT_LONG, task);
    }

    public void setInCombatLong(boolean isInCombat) {
        if (isInCombat) {
            this.getController().cancelTask(TaskId.PLAYER_COMBAT_LONG);
            this.combatLong = isInCombat;
        }
        else
            this.combatLong = false;
    }

    /**
     * @return the nextPotUse
     */
    public long getNextPotUse() {
        return nextPotUse;
    }

    /**
     * @param nextPotUse the nextPotUse to set
     */
    public void setNextPotUse(long nextPotUse) {
        this.nextPotUse = nextPotUse;
    }
}
