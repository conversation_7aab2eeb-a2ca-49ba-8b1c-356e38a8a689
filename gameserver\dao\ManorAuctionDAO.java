/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.dao;

import gameserver.model.Race;

import java.util.Collection;
import java.util.List;

import com.aionemu.commons.database.dao.DAO;

/**
 * 
 * <AUTHOR>
 */

public abstract class ManorAuctionDAO implements DAO {

    @Override
    public final String getClassName() {
        return ManorAuctionDAO.class.getName();
    }
    
    public abstract Collection<ManorAuctionEntry> getAllEntries();
    public abstract Collection<ManorAuctionEntry> getEntries(Race race);
    public abstract List<ManorAuctionEntry> getTopEntries(Race race, int limit);
    public abstract boolean clearAllEntries();
    public abstract boolean clearEntries(Race race);
    
    public class ManorAuctionEntry {
        private int legionId;
        private Race race;
        private int might;
        
        public ManorAuctionEntry(int legionId, Race race, int might) {
            this.legionId = legionId;
            this.race = race;
            this.might = might;
        }

        public int getLegionId() {
            return legionId;
        }

        public Race getRace() {
            return race;
        }

        public int getMight() {
            return might;
        }
    }
    
}