/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 *
 */
public class SpriggHabitatEvent extends MobEvent {
    public SpriggHabitatEvent() {
        super.mapId = 220020000;
        super.center = new SpawnPosition(1106, 2058, 213);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("An event at Sprigg Habitat in Morheim will commence in 2 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at Sprigg Habitat starts in 1 minute", 1 * 60 * 1000);
        announceAll("The event at Sprigg Habitat starts in 30 seconds", 30 * 1000 + 1
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 2 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters at Sprigg Habitat in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217937, 1080, 2138, 214);
        spawnMob(217937, 1067, 2123, 214);
        spawnMob(217937, 1053, 2115, 214);
        spawnMob(217937, 1087, 2121, 214);
        spawnMob(217937, 1068, 2105, 214);
        spawnMob(217937, 1064, 2093, 214);
        spawnMob(217937, 1082, 2097, 214);
        spawnMob(217937, 1096, 2092, 214);
        spawnMob(217937, 1075, 2079, 214);
        spawnMob(217937, 1094, 2076, 214);
        spawnMob(217937, 1113, 2076, 214);
        spawnMob(217937, 1090, 2047, 214);
        spawnMob(217937, 1107, 1984, 214);
        spawnMob(217937, 1096, 2000, 214);
        spawnMob(217937, 1110, 2011, 214);
        spawnMob(217937, 1114, 2052, 214);
        spawnMob(217937, 1115, 2036, 214);
        spawnMob(217937, 1132, 2029, 214);
        spawnMob(217937, 1138, 2043, 214);
        spawnMob(217937, 1129, 2060, 214);
        spawnMob(217937, 1129, 2060, 214);
    }
}
