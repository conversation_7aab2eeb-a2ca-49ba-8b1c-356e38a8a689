/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.utils.ThreadPoolManager;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class TwoTeamBg extends Battleground {
    private Map<Integer, Integer> roundResults = new HashMap<Integer, Integer>();
    private int maxRounds = 3;
    private int roundsDone = 0;
    private boolean endCalled = false;

    public TwoTeamBg() {
        super.name = "2-Team Normal";
        super.description = "You are on a team and must kill the enemy team. There are "
            + maxRounds
            + " rounds. The team which has won the most rounds by the end of the match is the winner.";
        super.minSize = 3;
        super.maxSize = 6;
        super.teamCount = 2;
        super.matchLength = 115;

        BattlegroundMap map1 = new BattlegroundMap(300090000);
        map1.addSpawn(new SpawnPosition(263.0f, 351.0f, 104.7f));
        map1.addSpawn(new SpawnPosition(277.0f, 133.0f, 103.2f));
        map1.setKillZ(100f);

        BattlegroundMap map2 = new BattlegroundMap(300350000);
        map2.addSpawn(new SpawnPosition(689.6f, 239.6f, 514.2f));
        map2.addSpawn(new SpawnPosition(688.8f, 287.6f, 514.5f));
        map2.addStaticDoor(201);
        map2.addStaticDoor(202);
        map2.addStaticDoor(203);
        map2.addStaticDoor(204);
        map2.addStaticDoor(205);
        map2.addStaticDoor(206);
        map2.addStaticDoor(207);
        map2.addStaticDoor(208);
        map2.addStaticDoor(209);
        map2.addStaticDoor(210);
        map2.addStaticDoor(211);
        map2.addStaticDoor(212);
        map2.setKillZ(500f);

        BattlegroundMap map3 = new BattlegroundMap(320110000);
        map3.addSpawn(new SpawnPosition(519.2f, 554.6f, 202.2f));
        map3.addSpawn(new SpawnPosition(524.7f, 498.0f, 199.8f));
        map3.setKillZ(198f);

        BattlegroundMap map4 = new BattlegroundMap(300450000);
        map4.addSpawn(new SpawnPosition(535.0f, 1174.0f, 444.75f));
        map4.addSpawn(new SpawnPosition(459.0f, 1097.0f, 451.76f));
        map4.addStaticDoor(124);
        map4.addStaticDoor(118);
        map4.addStaticDoor(116);
        map4.addStaticDoor(126);
        map4.addStaticDoor(125);
        map4.addStaticDoor(110);
        map4.addStaticDoor(123);
        map4.addStaticDoor(115);
        map4.addStaticDoor(113);
        map4.addStaticDoor(109);
        map4.addStaticDoor(106);
        map4.addStaticDoor(107);
        map4.setKillZ(425f);

        BattlegroundMap map5 = new BattlegroundMap(301140000); // 4.0
        map5.addSpawn(new SpawnPosition(1056.0f, 577.0f, 280.0f));
        map5.addSpawn(new SpawnPosition(1056.0f, 782.0f, 280.0f));
        map5.setKillZ(278f);

        BattlegroundMap map6 = new BattlegroundMap(301190000); // 4.0
        map6.addSpawn(new SpawnPosition(259.0f, 210.0f, 189.0f));
        map6.addSpawn(new SpawnPosition(121.0f, 210.0f, 189.0f));
        map6.setKillZ(185f);

        BattlegroundMap map7 = new BattlegroundMap(301170000); // 4.0
        map7.addSpawn(new SpawnPosition(640.0f, 455.0f, 103.0f));
        map7.addSpawn(new SpawnPosition(516.0f, 480.0f, 103.0f));
        map7.setKillZ(94f);

        BattlegroundMap map8 = new BattlegroundMap(600070000); // 4.0
        map8.addSpawn(new SpawnPosition(230.0f, 937.0f, 553.0f));
        map8.addSpawn(new SpawnPosition(328.0f, 1022.0f, 552.0f));
        map8.setKillZ(549f);

        BattlegroundMap map9 = new BattlegroundMap(300280000); // Rentus
        map9.addSpawn(new SpawnPosition(770f, 636f, 156f));
        map9.addSpawn(new SpawnPosition(892f, 638f, 156f));
        map9.setKillZ(152f);

        BattlegroundMap map10 = new BattlegroundMap(301130000);
        map10.addSpawn(446f, 446f, 182f);
        map10.addSpawn(512.5f, 529.2f, 182f);
        map10.setKillZ(180f);
        map10.setHighestZ(190f);
        
        BattlegroundMap map11 = new BattlegroundMap(400010000);
        map11.addSpawn(2098.5f, 754.6f, 1569f);
        map11.addSpawn(2100.5f, 592.8f, 1568f);
        map11.setKillZ(1558f);
        map11.setRestrictFlight(true);

        super.maps.add(map1);
        // super.maps.add(map2);
        super.maps.add(map3);
        super.maps.add(map4);
        super.maps.add(map5);
        super.maps.add(map9);
        super.maps.add(map10);
        super.maps.add(map11);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 25000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 25000);

        startBattleground(25000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups(0) <= 1) {
                    roundsDone = maxRounds;
                    endTwoTeamMatch(true);
                }
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamMatch(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in TwoTeamBg!");
            }
            else {
                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 2);
            }
        }

        if (player.getPlayerGroup() == null) {
            log.error("PlayerGroup == null in TwoTeamBg!");
        }
        else {
            int deadCounter = 0;
            for (Player pl : player.getPlayerGroup().getMembers()) {
                if (pl.getLifeStats().isAlreadyDead())
                    deadCounter++;
            }

            if (deadCounter == player.getPlayerGroup().size())
                endTwoTeamMatch(false);
        }
    }

    @Override
    public boolean createTournament(List<List<Player>> teams) {
        if (!super.createGroups(teams))
            return false;

        this.maxRounds = 5;
        this.description = "You are on a team and must kill the enemy team. There are "
            + maxRounds
            + " rounds. The team which has won the most rounds by the end of the match is the winner.";
        this.matchLength = 185;
        startMatch();

        return true;
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups(0) <= 1) {
            roundsDone = maxRounds;
            endTwoTeamMatch(false);
        }
    }

    private void startNewRound() {
        super.setStartStamp(System.currentTimeMillis());
        super.killSummonedCreatures();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                int delay = 0;

                synchronized (getGroups()) {
                    for (PlayerGroup group : getGroups()) {
                        for (Player pl : group.getMembers()) {
                            preparePlayer(pl, 7000, false);

                            SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                            if (pos != null)
                                TeleportService.teleportTo(pl, getMapId(), pos.getX(), pos.getY(),
                                    pos.getZ(), delay += 250);
                        }
                    }
                }
            }
        }, 3000);

        for (Player pl : super.getSpectators())
            super.createTimer(pl, getMatchLength());

        super.specAnnounce("The round has begun!!!", 10000);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamMatch(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endCalled = false;
                for (PlayerGroup group : getGroups()) {
                    if (group.size() < 1) {
                        roundsDone = maxRounds;
                        endTwoTeamMatch(false);
                        break;
                    }
                }
            }
        }, 15000);
    }

    private void addRoundWin(PlayerGroup group) {
        if (group == null)
            return;

        Integer result = roundResults.get(group.getBgIndex());
        if (result != null)
            roundResults.put(group.getBgIndex(), result + 1);
        else
            roundResults.put(group.getBgIndex(), 1);
    }

    private PlayerGroup getRoundWinner() {
        PlayerGroup winner = null;

        Map<PlayerGroup, Integer> deadCounts = new HashMap<PlayerGroup, Integer>();
        for (PlayerGroup group : super.getGroups()) {
            int deadCounter = 0;

            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead())
                    deadCounter++;
            }

            deadCounts.put(group, deadCounter);
        }

        int winDeadCount = Collections.min(deadCounts.values());
        int winnerCount = 0;
        for (Integer deadCount : deadCounts.values())
            if (deadCount == winDeadCount)
                winnerCount++;

        if (winnerCount == 1)
            for (Map.Entry<PlayerGroup, Integer> entry : deadCounts.entrySet())
                if (entry.getValue() == winDeadCount)
                    winner = entry.getKey();

        return winner;
    }

    private PlayerGroup getWinner() {
        PlayerGroup winner = null;
        int drawPoints = 0;

        for (Map.Entry<Integer, Integer> entry : roundResults.entrySet()) {
            if (winner == null && entry.getValue() > drawPoints) {
                winner = super.getGroups().get(entry.getKey());
            }
            else if (winner == null) {
                continue;
            }
            else if (roundResults.get(winner.getBgIndex()) < entry.getValue()) {
                winner = super.getGroups().get(entry.getKey());
            }
            else if (roundResults.get(winner.getBgIndex()) == entry.getValue()) {
                drawPoints = roundResults.get(winner.getBgIndex());
                winner = null;
            }
        }

        return winner;
    }

    private void endTwoTeamMatch(boolean isDraw) {
        if (endCalled)
            return;

        endCalled = true;

        for (PlayerGroup group : super.getGroups())
            for (Player pl : group.getMembers())
                super.freezeNoEnd(pl);

        super.onEndFirstDefault();

        PlayerGroup roundWinner = getRoundWinner();
        if (roundWinner != null)
            isDraw = false;

        if (roundsDone < maxRounds) {
            roundsDone++;

            if (!isDraw)
                addRoundWin(roundWinner);
        }

        if (roundsDone < maxRounds) {
            if (!isDraw) {
                if (roundWinner != null) {
                    for (PlayerGroup group : super.getGroups())
                        for (Player pl : group.getMembers())
                            super.scheduleAnnouncement(pl, LadderService.getInstance()
                                .getNameByIndex(roundWinner.getBgIndex())
                                + " has won round "
                                + roundsDone + "!", 0);
                    super.specAnnounce(LadderService.getInstance().getNameByIndex(
                        roundWinner.getBgIndex())
                        + " has won round " + roundsDone + "!");
                }
            }
            else {
                for (PlayerGroup group : super.getGroups())
                    for (Player pl : group.getMembers())
                        super.scheduleAnnouncement(pl, "Round " + roundsDone + " was a draw!", 0);
                super.specAnnounce("Round " + roundsDone + " was a draw!");
            }

            startNewRound();
            return;
        }

        PlayerGroup winner = getWinner();
        if (winner == null) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.scheduleAnnouncement(pl, "The match was a draw! Better luck next time.",
                        0);
                    super.rewardPlayer(pl, 15, false);
                }
            }
            super.specAnnounce("The match was a draw!");
        }
        else {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == winner.getGroupId()) {
                    for (Player pl : group.getMembers()) {
                        int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2 : 1;

                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE / 2);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.scheduleAnnouncement(pl, "Your team has won the match!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                        super.rewardPlayer(pl, 20, true);
                    }
                }
                else {
                    for (Player pl : group.getMembers()) {
                        super.playerLoseMatch(pl, -super.K_VALUE / 3);

                        super.scheduleAnnouncement(pl, "Your team has lost the match!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);
                        super.rewardPlayer(pl, 15, false);
                    }
                }
            }
            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}