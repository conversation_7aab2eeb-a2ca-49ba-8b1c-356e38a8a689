/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects;

import gameserver.ai.AI;
import gameserver.controllers.CreatureController;
import gameserver.controllers.MoveController;
import gameserver.controllers.ObserveController;
import gameserver.controllers.attack.AggroList;
import gameserver.controllers.effect.EffectController;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.state.CreatureSeeState;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.gameobjects.state.CreatureVisualState;
import gameserver.model.gameobjects.stats.CreatureGameStats;
import gameserver.model.gameobjects.stats.CreatureLifeStats;
import gameserver.model.templates.NpcTemplate;
import gameserver.model.templates.VisibleObjectTemplate;
import gameserver.model.templates.item.EAttackType;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_NAME_CHANGE;
import gameserver.skillengine.effect.EffectId;
import gameserver.skillengine.effect.EffectTemplate;
import gameserver.skillengine.effect.FearEffect;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.Skill;
import gameserver.taskmanager.tasks.PacketBroadcaster;
import gameserver.taskmanager.tasks.PacketBroadcaster.BroadcastMode;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.WorldPosition;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Future;

import org.apache.commons.lang.StringUtils;

/**
 * This class is representing movable objects, its base class for all in game objects that may move
 * 
 * <AUTHOR>
 */
public abstract class Creature extends VisibleObject {
    /**
     * Reference to AI
     */
    protected AI<? extends Creature> ai;

    private CreatureLifeStats<? extends Creature> lifeStats;
    private CreatureGameStats<? extends Creature> gameStats;

    private EffectController effectController;
    private MoveController moveController;

    private int state = CreatureState.ACTIVE.getId();
    private int visualState = CreatureVisualState.VISIBLE.getId();
    private int seeState = CreatureSeeState.NORMAL.getId();
    private boolean isInCombat = false;

    private Skill castingSkill;
    protected Map<Integer, Long> skillCoolDowns;
    private int transformedModelId;
    private ObserveController observeController;

    private AggroList aggroList;

    private long nextAttack = 0;
    private EAttackType attackType = EAttackType.PHYSICAL;

    private int isAdminNeutral = 0;
    private int isAdminEnmity = 0;

    private int stance = 0;

    private boolean outpostFlag = false;

    private long nextSkill = 0;

    private List<Creature> followers;
    private FormationType formationType = FormationType.TRIANGLE;
    private float formationDistance = 2.5f;

    private long blockTimer = 0;
    private long dodgeTimer = 0;
    private long parryTimer = 0;
    private long resistTimer = 0;

    private final int TIMER_DURATION = 6000;

    private int sleepCounter = 0;
    private long sleepTimer = 0;

    private int SLEEP_RESET_DURATION = 12000;

    private int fearCounter = 0;
    private long fearTimer = 0;

    private int FEAR_RESET_DURATION = 12000;

    private boolean dummy = false;

    private long hideTimer = 0;

    private int HIDE_THRESHOLD = 1000;

    private String customTag;

    private String customName;

    private long skillAllowMove = 0;

    private ConcurrentLinkedQueue<Trap> traps = null;

    private int titleIdOverride = 0;

    private boolean toBePulled = false;
    private long toBePulledTimer = 0;

    /**
     * @param objId
     * @param controller
     * @param spawnTemplate
     * @param objectTemplate
     * @param position
     */
    public Creature(int objId, CreatureController<? extends Creature> controller,
        SpawnTemplate spawnTemplate, VisibleObjectTemplate objectTemplate, WorldPosition position) {
        super(objId, controller, spawnTemplate, objectTemplate, position);
        initializeAi();
        this.moveController = new MoveController(this);
        this.observeController = new ObserveController();

        this.aggroList = new AggroList(this);
        this.followers = Collections.synchronizedList(new ArrayList<Creature>());
    }

    /**
     * Return CreatureController of this Creature object.
     * 
     * @return CreatureController.
     */
    @SuppressWarnings("unchecked")
    @Override
    public CreatureController getController() {
        return (CreatureController) super.getController();
    }

    /**
     * @return the lifeStats
     */
    public CreatureLifeStats<? extends Creature> getLifeStats() {
        return lifeStats;
    }

    /**
     * @param lifeStats
     *            the lifeStats to set
     */
    public void setLifeStats(CreatureLifeStats<? extends Creature> lifeStats) {
        this.lifeStats = lifeStats;
    }

    /**
     * @return the gameStats
     */
    public CreatureGameStats<? extends Creature> getGameStats() {
        return gameStats;
    }

    /**
     * @param gameStats
     *            the gameStats to set
     */
    public void setGameStats(CreatureGameStats<? extends Creature> gameStats) {
        this.gameStats = gameStats;
    }

    public abstract byte getLevel();

    public abstract void initializeAi();

    /**
     * @return the effectController
     */
    public EffectController getEffectController() {
        return effectController;
    }

    /**
     * @param effectController
     *            the effectController to set
     */
    public void setEffectController(EffectController effectController) {
        this.effectController = effectController;
    }

    /**
     * @return the npcAi
     */
    public AI<? extends Creature> getAi() {
        return ai != null ? ai : AI.dummyAi();
    }

    /**
     * @param ai
     *            the ai to set
     */
    public void setAi(AI<? extends Creature> ai) {
        this.ai = ai;
    }

    /**
     * Is creature casting some skill
     * 
     * @return
     */
    public boolean isCasting() {
        return castingSkill != null;
    }

    /**
     * Set current casting skill or null when skill ends
     * 
     * @param castingSkill
     */
    public void setCasting(Skill castingSkill) {
        this.castingSkill = castingSkill;
    }

    /**
     * Current casting skill id
     * 
     * @return
     */
    public int getCastingSkillId() {
        return castingSkill != null ? castingSkill.getSkillTemplate().getSkillId() : 0;
    }

    /**
     * Current casting skill
     * 
     * @return
     */
    public Skill getCastingSkill() {
        return castingSkill;
    }

    /**
     * All abnormal effects are checked that disable movements
     * 
     * @return
     */
    public boolean canPerformMove() {
        for (Effect effect : getEffectController().getAbnormalEffects()) {
            for (EffectTemplate template : effect.getEffectTemplates()) {
                if (template instanceof FearEffect)
                    return false;
            }
        }

        return !(getEffectController().hasCantMoveAbnormal() || !isSpawned());
    }

    /**
     * All abnormal effects are checked that disable attack
     * 
     * @return
     */
    public boolean canAttack() {
        return !(getEffectController().isAbnormalState(EffectId.CANT_ATTACK_STATE) || isCasting()
            || isInState(CreatureState.RESTING) || isInState(CreatureState.PRIVATE_SHOP));
    }

    /**
     * @return state
     */
    public int getState() {
        return state;
    }

    /**
     * @param state
     *            the state to set
     */
    public void setState(CreatureState state) {
        this.state |= state.getId();
        observeController.notifyStateChangeObservers(state, true);
    }

    /**
     * @param state
     *            taken usually from templates
     */
    public void setState(int state) {
        this.state = state;
    }

    public void unsetState(CreatureState state) {
        this.state &= ~state.getId();
        observeController.notifyStateChangeObservers(state, false);
    }

    public boolean isInState(CreatureState state) {
        int isState = this.state & state.getId();

        if (isState == state.getId())
            return true;

        return false;
    }

    /**
     * @return visualState
     */
    public int getVisualState() {
        return visualState;
    }

    /**
     * @param visualState
     *            the visualState to set
     */
    public void setVisualState(CreatureVisualState visualState) {
        this.visualState |= visualState.getId();
    }

    public void unsetVisualState(CreatureVisualState visualState) {
        this.visualState &= ~visualState.getId();
    }

    public boolean isInVisualState(CreatureVisualState visualState) {
        int isVisualState = this.visualState & visualState.getId();

        if (isVisualState == visualState.getId())
            return true;

        return false;
    }

    /**
     * @return seeState
     */
    public int getSeeState() {
        return seeState;
    }

    /**
     * @param seeState
     *            the seeState to set
     */
    public void setSeeState(CreatureSeeState seeState) {
        this.seeState |= seeState.getId();
    }

    public void unsetSeeState(CreatureSeeState seeState) {
        this.seeState &= ~seeState.getId();
    }

    /**
     * @return the transformedModelId
     */
    public int getTransformedModelId() {
        return transformedModelId;
    }

    /**
     * @param transformedModelId
     *            the transformedModelId to set
     */
    public void setTransformedModelId(int transformedModelId) {
        this.transformedModelId = transformedModelId;
    }

    /**
     * @return the moveController
     */
    public MoveController getMoveController() {
        return moveController;
    }

    /**
     * @param moveController
     */
    public void setMoveController(MoveController moveController) {
        this.moveController = moveController;
    }

    /**
     * @return the aggroList
     */
    public AggroList getAggroList() {
        return aggroList;
    }

    /**
     * @param aggroList
     */
    public void setAggroList(AggroList aggroList) {
        this.aggroList = aggroList;
    }

    /**
     * PacketBroadcasterMask
     */
    private volatile byte packetBroadcastMask;

    /**
     * This is adding broadcast to player.
     */
    public final void addPacketBroadcastMask(BroadcastMode mode) {
        packetBroadcastMask |= mode.mask();

        PacketBroadcaster.getInstance().add(this);
    }

    /**
     * This is removing broadcast from player.
     */
    public final void removePacketBroadcastMask(BroadcastMode mode) {
        packetBroadcastMask &= ~mode.mask();
    }

    /**
     * Broadcast getter.
     */
    public final byte getPacketBroadcastMask() {
        return packetBroadcastMask;
    }

    /**
     * @return the observeController
     */
    public ObserveController getObserveController() {
        return observeController;
    }

    /**
     * @param observeController
     */
    public void setObserveController(ObserveController observeController) {
        this.observeController = observeController;
    }

    /**
     * @param visibleObject
     * @return
     */
    public boolean isEnemy(VisibleObject visibleObject) {
        if (visibleObject instanceof Npc)
            return isEnemyNpc((Npc) visibleObject);
        else if (visibleObject instanceof Player)
            return isEnemyPlayer((Player) visibleObject);
        else if (visibleObject instanceof Summon)
            return isEnemySummon((Summon) visibleObject);

        return false;
    }

    /**
     * @param summon
     * @return
     */
    protected boolean isEnemySummon(Summon summon) {
        return false;
    }

    /**
     * @param player
     * @return
     */
    protected boolean isEnemyPlayer(Player player) {
        return false;
    }

    /**
     * @param npc
     * @return
     */
    protected boolean isEnemyNpc(Npc npc) {
        return false;
    }

    public String getTribe() {
        return StringUtils.EMPTY;
    }

    /**
     * @param creature
     * @return
     */
    public boolean isAggressiveTo(Creature creature) {
        if (creature.getObjectTemplate() instanceof NpcTemplate
            && this.getObjectTemplate() instanceof NpcTemplate) {
            if (((NpcTemplate) creature.getObjectTemplate()).getRace() != ((NpcTemplate) this
                .getObjectTemplate()).getRace())
                return true;
        }
        return false;
    }

    /**
     * @param npc
     * @return
     */
    public boolean isAggroFrom(Npc npc) {
        return false;
    }

    /**
     * @param npc
     * @return
     */
    public boolean isHostileFrom(Npc npc) {
        return false;
    }

    public boolean isSupportFrom(Npc npc) {
        return false;
    }

    /**
     * @param player
     * @return
     */
    public boolean isAggroFrom(Player player) {
        return false;
    }

    /**
     * @param summon
     * @return
     */
    public boolean isAggroFrom(Summon summon) {
        return isAggroFrom(summon.getMaster());
    }

    /**
     * @param visibleObject
     * @return
     */
    public boolean canSee(VisibleObject visibleObject) {
        if (visibleObject instanceof Npc)
            return canSeeNpc((Npc) visibleObject);
        else if (visibleObject instanceof Player)
            return canSeePlayer((Player) visibleObject);

        return true;
    }

    /**
     * @param visibleObject
     * @return
     */
    protected boolean canSeePlayer(Player visibleObject) {
        return true;
    }

    /**
     * @param visibleObject
     * @return
     */
    protected boolean canSeeNpc(Npc visibleObject) {
        return true;
    }

    /**
     * @return NpcObjectType.NORMAL
     */
    public NpcObjectType getNpcObjectType() {
        return NpcObjectType.NORMAL;
    }

    /**
     * For summons and different kind of servants<br>
     * it will return currently acting player.<br>
     * <p/>
     * This method is used for duel and enemy relations,<br>
     * rewards<br>
     * 
     * @return Master of this creature or self
     */
    public Creature getMaster() {
        return this;
    }

    /**
     * For summons it will return summon object and for <br>
     * servants - player object.<br>
     * <p/>
     * Used to find attackable target for npcs.<br>
     * 
     * @return acting master - player in case of servants
     */
    public Creature getActingCreature() {
        return this;
    }

    /**
     * @param skillId
     * @return
     */
    public boolean isSkillDisabled(int delayId) {
        if (skillCoolDowns == null)
            return false;

        Long coolDown = skillCoolDowns.get(delayId);
        if (coolDown == null)
            return false;

        if (coolDown < System.currentTimeMillis()) {
            skillCoolDowns.remove(delayId);
            return false;
        }

        return true;
    }

    /**
     * @param skillId
     * @return
     */
    public long getSkillCoolDown(int skillId) {
        if (skillCoolDowns == null || !skillCoolDowns.containsKey(skillId))
            return 0;

        return skillCoolDowns.get(skillId);
    }

    /**
     * @param skillId
     * @param time
     */
    public void setSkillCoolDown(int skillId, long time) {
        if (skillCoolDowns == null)
            skillCoolDowns = new ConcurrentHashMap<Integer, Long>();

        skillCoolDowns.put(skillId, time);
    }

    /**
     * @return the skillCoolDowns
     */
    public Map<Integer, Long> getSkillCoolDowns() {
        return skillCoolDowns;
    }

    /**
     * @param skillId
     */
    public void removeSkillCoolDown(int skillId) {
        if (skillCoolDowns == null)
            return;
        skillCoolDowns.remove(skillId);
    }

    /**
     * Resets skill cooldowns
     */
    public void resetSkillCoolDowns() {
        if (skillCoolDowns != null)
            skillCoolDowns.clear();
        else
            skillCoolDowns = new ConcurrentHashMap<Integer, Long>();
    }

    /**
     * @return isAdminNeutral value
     */
    public int getAdminNeutral() {
        return isAdminNeutral;
    }

    /**
     * @param newValue
     */
    public void setAdminNeutral(int newValue) {
        isAdminNeutral = newValue;
    }

    /**
     * @return isAdminEnmity value
     */
    public int getAdminEnmity() {
        return isAdminEnmity;
    }

    /**
     * @param newValue
     */
    public void setAdminEnmity(int newValue) {
        isAdminEnmity = newValue;
    }

    /**
     * @param lastAttack
     *            the lastAttack to set
     */
    public void setNextAttack(long lastAttack) {
        this.nextAttack = lastAttack;
    }

    /**
     * @return the lastAttack
     */
    public long getNextAttack() {
        return nextAttack;
    }

    /**
     * Set or unset Creature in combat state
     * 
     * @param isInCombat
     */
    public void setInCombat(boolean isInCombat) {
        if (isInCombat) {
            this.getController().cancelTask(TaskId.CREATURE_COMBAT);
            this.isInCombat = isInCombat;
        }
        else
            this.isInCombat = false;
    }

    /**
     * Is the creature in combat state
     * 
     * @return isInCombat
     */
    public boolean isInCombat() {
        return isInCombat;
    }

    /**
     * Sets combat state to true for time in seconds
     * 
     * @param creature
     * @param time
     */
    @SuppressWarnings({ "unchecked" })
    public void setCombatState(int time) {
        setInCombat(true);
        Future<?> task = ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (isInCombat())
                    setInCombat(false);
            }
        }, time * 1000);

        this.getController().addTask(TaskId.CREATURE_COMBAT, task);
    }

    // attack type
    public EAttackType getAttackType() {
        return attackType;
    }

    public void setAttackType(EAttackType attackType) {
        this.attackType = attackType;
    }

    public void unsetAttackType() {
        this.attackType = EAttackType.PHYSICAL;
    }

    /**
     * @param stance
     *            the stance to set
     */
    public void setStance(int stance) {
        this.stance = stance;
    }

    /**
     * @return the stance
     */
    public int getStance() {
        return stance;
    }

    public boolean isFlying() {
        return (isInState(CreatureState.FLYING) && !isInState(CreatureState.RESTING))
            || isInState(CreatureState.GLIDING);
    }

    /**
     * @return the outpostFlag
     */
    public boolean isOutpostFlag() {
        return outpostFlag;
    }

    /**
     * @param outpostFlag
     *            the outpostFlag to set
     */
    public void setOutpostFlag(boolean outpostFlag) {
        this.outpostFlag = outpostFlag;
    }

    /**
     * @return the nextSkill
     */
    public long getNextSkill() {
        return nextSkill;
    }

    /**
     * @param nextSkill
     *            the nextSkill to set
     */
    public void setNextSkill(long nextSkill) {
        this.nextSkill = nextSkill;
    }

    /**
     * @return followers
     */
    public List<Creature> getFollowers() {
        return followers;
    }

    /**
     * @param follower
     */
    public void addFollower(Creature follower) {
        this.followers.add(follower);
        this.getMoveController().updateFormation();
    }

    /**
     * @param follower
     */
    public void removeFollower(Creature follower) {
        this.followers.remove(follower);
        this.getMoveController().updateFormation();

        follower.getMoveController().followHere(null, 0, 0, 0);
    }

    /**
     * @return formationType
     */
    public FormationType getFormationType() {
        return formationType;
    }

    /**
     * @param formationType
     */
    public void setFormationType(FormationType formationType) {
        this.formationType = formationType;
    }

    /**
     * @return formationDistance
     */
    public float getFormationDistance() {
        return formationDistance;
    }

    /**
     * @param formationDistance
     */
    public void setFormationDistance(float formationDistance) {
        this.formationDistance = formationDistance;
    }

    public enum FormationType {
        COLUMN,
        TRIANGLE
    }

    public boolean hasBlocked() {
        return blockTimer > System.currentTimeMillis();
    }

    public void setBlockTimer() {
        this.blockTimer = System.currentTimeMillis() + TIMER_DURATION;
    }

    public boolean hasDodged() {
        return dodgeTimer > System.currentTimeMillis();
    }

    public void setDodgeTimer() {
        this.dodgeTimer = System.currentTimeMillis() + TIMER_DURATION;
    }

    public boolean hasParried() {
        return parryTimer > System.currentTimeMillis();
    }

    public void setParryTimer() {
        this.parryTimer = System.currentTimeMillis() + TIMER_DURATION;
    }

    public boolean hasResisted() {
        return resistTimer > System.currentTimeMillis();
    }

    public void setResistTimer() {
        this.resistTimer = System.currentTimeMillis() + TIMER_DURATION;
    }

    public int getSleepCounter() {
        return sleepCounter;
    }

    public int incrementAndGetSleepCounter() {
        if (System.currentTimeMillis() > sleepTimer)
            this.sleepCounter = 0;

        this.sleepCounter += 1;
        this.sleepTimer = System.currentTimeMillis() + SLEEP_RESET_DURATION;

        return this.sleepCounter;
    }

    public int getFearCounter() {
        return fearCounter;
    }

    public int incrementAndGetFearCounter() {
        if (System.currentTimeMillis() > fearTimer)
            this.fearCounter = 0;

        this.fearCounter += 1;
        this.fearTimer = System.currentTimeMillis() + FEAR_RESET_DURATION;

        return this.fearCounter;
    }

    public void setDummy(boolean dummy) {
        this.dummy = dummy;
    }

    public boolean isDummy() {
        return dummy;
    }

    public void setHideTimer() {
        this.hideTimer = System.currentTimeMillis() + HIDE_THRESHOLD;
    }

    public boolean hasEnteredHide() {
        return System.currentTimeMillis() > hideTimer;
    }

    public String getCustomTag() {
        return customTag;
    }

    public Creature setCustomTag(String customTag) {
        this.customTag = customTag;
        return this;
    }

    public String getCustomName() {
        return customName;
    }

    public Creature setCustomName(String customName) {
        this.customName = customName;
        return this;
    }

    public Creature broadcastCustomName() {
        if (customName == null || customName.isEmpty())
            return this;

        PacketSendUtility.broadcastPacketAndReceive(this, new SM_NAME_CHANGE(getObjectId(),
            getName(), getCustomName()));

        return this;
    }

    public long getSkillAllowMove() {
        return skillAllowMove;
    }

    public void setSkillAllowMove(long skillAllowMove) {
        this.skillAllowMove = skillAllowMove;
    }

    public ConcurrentLinkedQueue<Trap> getTraps() {
        if (traps == null)
            traps = new ConcurrentLinkedQueue<Trap>();

        return traps;
    }

    public int getTitleIdOverride() {
        return titleIdOverride;
    }

    public void setTitleIdOverride(int titleIdOverride) {
        this.titleIdOverride = titleIdOverride;
    }

    public boolean isToBePulled() {
        return toBePulled && (toBePulledTimer + 1000 > System.currentTimeMillis());
    }

    public void setToBePulled(boolean toBePulled) {
        this.toBePulled = toBePulled;
        this.toBePulledTimer = System.currentTimeMillis();
    }
}
