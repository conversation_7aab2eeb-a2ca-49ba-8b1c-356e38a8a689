/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dataholders;

import gameserver.model.templates.tribe.AggroRelations;
import gameserver.model.templates.tribe.HostileRelations;
import gameserver.model.templates.tribe.Tribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.Unmarshaller;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 */
@XmlRootElement(name = "tribe_relations")
@XmlAccessorType(XmlAccessType.FIELD)
public class TribeRelationsData {
    @XmlElement(name = "tribe", required = true)
    protected List<Tribe> tribeList;

    protected Map<String, Tribe> tribeNameMap = new HashMap<String, Tribe>();

    void afterUnmarshal(Unmarshaller u, Object parent) {
        for (Tribe tribe : tribeList) {
            tribeNameMap.put(tribe.getName(), tribe);
        }
        tribeList = null;
    }

    /**
     * @return tribeNameMap.size()
     */
    public int size() {
        return tribeNameMap.size();
    }

    /**
     * @param tribeName
     * @return
     */
    public boolean hasAggressiveRelations(String tribeName) {
        Tribe tribe = tribeNameMap.get(tribeName);
        if (tribe == null)
            return false;

        AggroRelations aggroRelations = tribe.getAggroRelations();
        if (aggroRelations == null && tribe.getBase() != null)
            return hasAggressiveRelations(tribe.getBase());

        return aggroRelations != null && !aggroRelations.getTo().isEmpty();
    }

    /**
     * @param tribeName
     * @return
     */
    public boolean hasHostileRelations(String tribeName) {
        Tribe tribe = tribeNameMap.get(tribeName);
        if (tribe == null)
            return false;

        HostileRelations hostileRelations = tribe.getHostileRelations();
        if (hostileRelations == null && tribe.getBase() != null)
            return hasHostileRelations(tribe.getBase());

        return hostileRelations != null && !hostileRelations.getTo().isEmpty();
    }

    /**
     * @param tribeName1
     * @param tribeName2
     * @return
     */
    public boolean isAggressiveRelation(String tribeName1, String tribeName2) {
        Tribe tribe1 = tribeNameMap.get(tribeName1);
        Tribe tribe2 = tribeNameMap.get(tribeName2);
        if (tribe1 == null || tribe2 == null)
            return false;

        List<String> aggroRelations = new ArrayList<String>();
        if (tribe1.getAggroRelations() != null)
            aggroRelations.addAll(tribe1.getAggroRelations().getTo());

        if (tribe1.getBase() != null) {
            Tribe tribe1Base = tribeNameMap.get(tribe1.getBase());

            if (tribe1Base != null && tribe1Base.getAggroRelations() != null)
                aggroRelations.addAll(tribe1Base.getAggroRelations().getTo());
        }

        if (!aggroRelations.contains(tribeName2) && tribe2.getBase() != null)
            return aggroRelations.contains(tribe2.getBase());

        return aggroRelations.contains(tribeName2);
    }

    /**
     * @param tribeName1
     * @param tribeName2
     * @return
     */
    public boolean isSupportRelation(String tribeName1, String tribeName2) {
        Tribe tribe1 = tribeNameMap.get(tribeName1);
        Tribe tribe2 = tribeNameMap.get(tribeName2);
        if (tribe1 == null || tribe2 == null)
            return false;

        List<String> supportRelations = new ArrayList<String>();
        if (tribe1.getSupportRelations() != null)
            supportRelations.addAll(tribe1.getSupportRelations().getTo());

        if (tribe1.getBase() != null) {
            Tribe tribe1Base = tribeNameMap.get(tribe1.getBase());

            if (tribe1Base != null && tribe1Base.getSupportRelations() != null)
                supportRelations.addAll(tribe1Base.getSupportRelations().getTo());
        }
        
        if (!supportRelations.contains(tribeName2) && tribe2.getBase() != null)
            return supportRelations.contains(tribe2.getBase());

        return supportRelations.contains(tribeName2);
    }

    /**
     * @param tribeName1
     * @param tribeName2
     * @return
     */
    public boolean isFriendlyRelation(String tribeName1, String tribeName2) {
        Tribe tribe1 = tribeNameMap.get(tribeName1);
        Tribe tribe2 = tribeNameMap.get(tribeName2);
        if (tribe1 == null || tribe2 == null)
            return false;

        List<String> friendlyRelations = new ArrayList<String>();
        if (tribe1.getFriendlyRelations() != null)
            friendlyRelations.addAll(tribe1.getFriendlyRelations().getTo());

        if (tribe1.getBase() != null) {
            Tribe tribe1Base = tribeNameMap.get(tribe1.getBase());

            if (tribe1Base != null && tribe1Base.getFriendlyRelations() != null)
                friendlyRelations.addAll(tribe1Base.getFriendlyRelations().getTo());
        }
        
        if (!friendlyRelations.contains(tribeName2) && tribe2.getBase() != null)
            return friendlyRelations.contains(tribe2.getBase());

        return friendlyRelations.contains(tribeName2);
    }

    /**
     * @param tribeName1
     * @param tribeName2
     * @return
     */
    public boolean isNeutralRelation(String tribeName1, String tribeName2) {
        Tribe tribe1 = tribeNameMap.get(tribeName1);
        Tribe tribe2 = tribeNameMap.get(tribeName2);
        if (tribe1 == null || tribe2 == null)
            return false;

        List<String> neutralRelations = new ArrayList<String>();
        if (tribe1.getNeutralRelations() != null)
            neutralRelations.addAll(tribe1.getNeutralRelations().getTo());

        if (tribe1.getBase() != null) {
            Tribe tribe1Base = tribeNameMap.get(tribe1.getBase());

            if (tribe1Base != null && tribe1Base.getNeutralRelations() != null)
                neutralRelations.addAll(tribe1Base.getNeutralRelations().getTo());
        }
        
        if (!neutralRelations.contains(tribeName2) && tribe2.getBase() != null)
            return neutralRelations.contains(tribe2.getBase());

        return neutralRelations.contains(tribeName2);
    }

    /**
     * @param tribeName1
     * @param tribeName2
     * @return
     */
    public boolean isHostileRelation(String tribeName1, String tribeName2) {
        Tribe tribe1 = tribeNameMap.get(tribeName1);
        Tribe tribe2 = tribeNameMap.get(tribeName2);
        if (tribe1 == null || tribe2 == null)
            return false;

        List<String> hostileRelations = new ArrayList<String>();
        if (tribe1.getHostileRelations() != null)
            hostileRelations.addAll(tribe1.getHostileRelations().getTo());

        if (tribe1.getBase() != null) {
            Tribe tribe1Base = tribeNameMap.get(tribe1.getBase());

            if (tribe1Base != null && tribe1Base.getHostileRelations() != null)
                hostileRelations.addAll(tribe1Base.getHostileRelations().getTo());
        }
        
        if (!hostileRelations.contains(tribeName2) && tribe2.getBase() != null)
            return hostileRelations.contains(tribe2.getBase());

        return hostileRelations.contains(tribeName2);
    }

    /**
     * @param tribeName
     * @return
     */
    public boolean isGuardDark(String tribeName) {
        Tribe tribe = tribeNameMap.get(tribeName);
        if (tribe == null)
            return false;

        if (Tribe.GUARD_DARK.equals(tribe.getName()))
            return true;

        String baseTribe = tribe.getBase();
        if (baseTribe != null)
            return isGuardDark(baseTribe);

        return false;
    }

    /**
     * @param tribeName
     * @return
     */
    public boolean isGuardLight(String tribeName) {
        Tribe tribe = tribeNameMap.get(tribeName);
        if (tribe == null)
            return false;

        if (Tribe.GUARD_LIGHT.equals(tribe.getName()))
            return true;

        String baseTribe = tribe.getBase();
        if (baseTribe != null)
            return isGuardLight(baseTribe);

        return false;
    }

    public boolean isMonster(String tribeName) {
        Tribe tribe = tribeNameMap.get(tribeName);
        if (tribe == null)
            return false;

        if (Tribe.MONSTER.equals(tribe.getName()))
            return true;

        String baseTribe = tribe.getBase();
        if (baseTribe != null)
            return isMonster(baseTribe);

        return false;
    }
}
