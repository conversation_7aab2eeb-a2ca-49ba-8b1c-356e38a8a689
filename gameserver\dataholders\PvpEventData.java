package gameserver.dataholders;

import gameserver.model.templates.bossevent.BossTemplate;
import gameserver.model.templates.bossevent.BossesTemplate;
import gameserver.model.templates.bossevent.HelperTemplate;
import gameserver.model.templates.bossevent.HelpersTemplate;
import gameserver.model.templates.bossevent.Point3DGroup;
import gameserver.model.templates.bossevent.Position3D;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "bosses", "helpers", "spawnpoints" })
@XmlRootElement(name = "pvpevent_data")
public class PvpEventData {

    @XmlElement(name = "bosses", required = true)
    protected BossesTemplate bosses;
    @XmlElement(name = "helpers", required = true)
    protected HelpersTemplate helpers;
    @XmlElement(name = "spawnpoints", required = true)
    protected Point3DGroup spawnpoints;

    public List<BossTemplate> getBosses() {
        return bosses.getBosses();
    }

    public List<HelperTemplate> getHelpers() {
        return helpers.getHelpers();
    }

    public List<Position3D> getSpawnpoints() {
        return spawnpoints.getSpawnpoints();
    }

    public int size() {
        return spawnpoints.getSpawnpoints().size() + helpers.getHelpers().size()
            + bosses.getBosses().size();
    }

    public List<HelperTemplate> getHelpersByDifficulty(int difficulty) {
        List<HelperTemplate> theHelpers = new ArrayList<HelperTemplate>();
        for (HelperTemplate helper : helpers.getHelpers()) {
            if (helper.getDifficulty() == difficulty)
                theHelpers.add(helper);
        }
        return theHelpers;
    }
}
