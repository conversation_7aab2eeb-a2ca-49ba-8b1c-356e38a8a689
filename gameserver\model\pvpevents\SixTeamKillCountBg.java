/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.controllers.attack.AttackStatus;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.StaticDoor;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.List;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class SixTeamKillCountBg extends Battleground {
    private int extraCounter = 0;

    public SixTeamKillCountBg() {
        super.name = "6-Team Kill Count";
        super.description = "Your goal is to get the most points. Killing the leading team rewards you more points than others. You will respawn at your base every 30 seconds.";
        super.minSize = 2;
        super.maxSize = 3;
        super.teamCount = 6;
        super.matchLength = 390;

        BattlegroundMap map1 = new BattlegroundMap(320120000);
        map1.addSpawn(new SpawnPosition(368f, 474f, 243f));
        map1.addSpawn(new SpawnPosition(447f, 475f, 243f));
        map1.addSpawn(new SpawnPosition(511f, 593f, 223f));
        map1.addSpawn(new SpawnPosition(512f, 514f, 223f));
        map1.addSpawn(new SpawnPosition(631f, 450f, 203f));
        map1.addSpawn(new SpawnPosition(551f, 450f, 203f));
        map1.addStaticDoor(4);
        map1.addStaticDoor(5);
        map1.addStaticDoor(10);
        map1.addStaticDoor(54);
        map1.addStaticDoor(60);
        map1.addStaticDoor(55);
        map1.addStaticDoor(2);
        map1.addStaticDoor(27);
        map1.addStaticDoor(28);
        map1.setKillZ(200);

        super.maps.add(map1);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 30000);

                    scheduleAnnouncement(pl, "The doors to the other arenas have opened!",
                        isTournament() ? 170 * 1000 : 110 * 1000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 30000);

        startBattleground(30000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups() <= 2)
                    endSixTeamMatch();
            }
        });

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                openStaticDoors();
            }
        }, 30 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                //getInstance().openDoor(53, 0);
                //getInstance().openDoor(1, 0);
                openStaticDoor(53);
                openStaticDoor(1);

                specAnnounce("The doors to the other arenas have opened!");
            }
        }, isTournament() ? 170 * 1000 : 110 * 1000);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endSixTeamMatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    @Override
                    public void run() {
                        extraCounter++;

                        spawnProtection();

                        if ((extraCounter % 15) == 0)
                            showTeamPositions();

                        if ((extraCounter % 30) == 0) {
                            extraCounter = 0;
                            autoResurrection();
                        }
                    }
                }, 1 * 1000, 1 * 1000));
            }
        }, 30 * 1000);
    }

    private void openStaticDoor(int doorId) {
        StaticDoor door = null;

        for (AionObject ao : getInstance().getObjects()) {
            if (!(ao instanceof StaticDoor))
                continue;

            if (((StaticDoor) ao).getObjectTemplate().getId() == doorId) {
                door = (StaticDoor) ao;
                break;
            }
        }

        if (door == null) {
            getInstance().openDoor(doorId, 0);
        }
        else {
            door.setOpen(true);
        }
    }

    @Override
    public boolean createTournament(List<List<Player>> teams) {
        if (!super.createGroups(teams))
            return false;

        this.matchLength = 570;
        startMatch();

        return true;
    }

    private void spawnProtection() {
        int teamIndex = 0;
        for (SpawnPosition spawn : getSpawnPositions()) {
            for (PlayerGroup group : super.getGroups())
                if (group.getBgIndex() != teamIndex)
                    for (Player pl : group.getMembers())
                        if (MathUtil.getDistance(pl, spawn.getX(), spawn.getY(), spawn.getZ()) <= 10)
                            pl.getController().onAttack(pl.getAggroList().getMostPlayerDamage(),
                                1337, AttackStatus.NORMALHIT, true);

            teamIndex++;
        }
    }

    private void showTeamPositions() {
        PlayerGroup leading = null;
        for (PlayerGroup group : super.getGroups()) {
            if (leading == null && group.getKillCount() > 0)
                leading = group;
            else if (leading != null && group.getKillCount() > leading.getKillCount())
                leading = group;
        }

        if (leading != null) {
            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == leading.getGroupId()) {
                    for (Player pl : group.getMembers())
                        super
                            .scheduleAnnouncement(pl,
                                "Your team is in the lead with " + group.getKillCount()
                                    + " points!", 0);
                }
                else {
                    int pointDiff = leading.getKillCount() - group.getKillCount();
                    for (Player pl : group.getMembers())
                        super.scheduleAnnouncement(pl,
                            "Your team is " + pointDiff + " points behind the "
                                + LadderService.getInstance().getNameByIndex(leading.getBgIndex())
                                + "!", 0);
                }
            }
            super.specAnnounce(LadderService.getInstance().getNameByIndex(leading.getBgIndex())
                + " is in the lead with " + leading.getKillCount() + " points!");
        }
    }

    private void autoResurrection() {
        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead()) {
                    pl.getReviveController().fullRevive();
                    super.spawnProtection(pl);
                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    TeleportService.teleportTo(pl, getMapId(), super.getInstanceId(), pos.getX(),
                        pos.getY(), pos.getZ(), 0);
                    super
                        .scheduleAnnouncement(pl, "You have been automatically resurrected!", 1500);
                }
            }
        }
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super.scheduleAnnouncement(player, "You will automatically resurrect every 30 seconds.", 0);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in ThreeTeamKillCountBg!");
            }
            else {
                PlayerGroup leading = null;
                for (PlayerGroup group : super.getGroups()) {
                    if (leading == null && group.getKillCount() > 0)
                        leading = group;
                    else if (leading != null && group.getKillCount() > leading.getKillCount())
                        leading = group;
                }

                if (player.getPlayerGroup() == leading) { // add 2 more "kills"
                    killer.getPlayerGroup()
                        .setKillCount(killer.getPlayerGroup().getKillCount() + 2);

                    for (Player pl : killer.getPlayerGroup().getMembers())
                        PacketSendUtility.sendMessage(pl,
                            "You have earned 3 points for killing someone in the lead!");
                }
                else {
                    for (Player pl : killer.getPlayerGroup().getMembers())
                        PacketSendUtility.sendMessage(pl, "You have earned 1 point for the kill.");
                }

                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 2);
            }
        }
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups() <= 2)
            endSixTeamMatch();
    }

    private void endSixTeamMatch() {
        super.onEndFirstDefault();

        PlayerGroup winner = null;
        for (PlayerGroup group : super.getGroups()) {
            if (winner == null)
                winner = group;
            else if (winner != null && group.getKillCount() > winner.getKillCount())
                winner = group;
        }

        if (winner == null) {
            log.error("No winner in a Six Team Kill Count match!??!");
            winner = super.getGroups().get(Rnd.get(super.getGroups().size()));
        }

        propagateWin(winner);

        logWinner(winner.getBgIndex() + 1);

        for (PlayerGroup group : super.getGroups()) {
            int averageRating = super.computeAverageGroupRating(super.getGroups());

            if (group.getGroupId() == winner.getGroupId()) {
                for (Player pl : group.getMembers()) {
                    if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                        .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                        super.playerWinMatch(pl, super.K_VALUE);
                    else
                        super
                            .message(pl,
                                "You have not been credited with the win due to the match being heavily unfair.");

                    super.scheduleAnnouncement(pl,
                        "Your team has won the match with " + group.getKillCount() + " points!", 0);
                    super
                        .scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                    super.rewardPlayer(pl, 30, true);
                }
            }
            else {
                for (Player pl : group.getMembers()) {
                    super.playerLoseMatch(pl, -super.K_VALUE / 6);

                    super.scheduleAnnouncement(pl,
                        "Your team has lost the match with " + group.getKillCount()
                            + " points against " + winner.getKillCount() + "!", 0);
                    super.scheduleAnnouncement(pl, "You have received some might but lost rating.",
                        3000);
                    super.rewardPlayer(pl, 15, false);
                }
            }
        }

        super.propagateDone();

        super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
            + " has won the match with " + winner.getKillCount() + " points!");
        super.onEndDefault();
    }
}