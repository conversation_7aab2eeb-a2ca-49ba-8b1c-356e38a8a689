/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is private software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.stats.modifiers;

import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.StatModifierPriority;

/**
 * <AUTHOR>
 */
public class AddModifier extends SimpleModifier {
    @Override
    public int apply(int stat, int currentStat) {
        int chkValue;
        boolean applyLimit = true;

        switch (getStat()) {
            case FIRE_RESISTANCE:
            case EARTH_RESISTANCE:
            case WIND_RESISTANCE:
            case WATER_RESISTANCE:
            case BLEED_RESISTANCE:
            case BLIND_RESISTANCE:
            case CHARM_RESISTANCE:
            case CONFUSE_RESISTANCE:
            case CURSE_RESISTANCE:
            case DISEASE_RESISTANCE:
            case FEAR_RESISTANCE:
            case OPENAREIAL_RESISTANCE:
            case PARALYZE_RESISTANCE:
            case PERIFICATION_RESISTANCE:
            case POISON_RESISTANCE:
            case ROOT_RESISTANCE:
            case SILENCE_RESISTANCE:
            case SLEEP_RESISTANCE:
            case SLOW_RESISTANCE:
            case SNARE_RESISTANCE:
            case SPIN_RESISTANCE:
            case STAGGER_RESISTANCE:
            case STUMBLE_RESISTANCE:
            case STUN_RESISTANCE:
            case TRANSFORM_RESISTANCE:
            case PULL_RESISTANCE:
            case BIND_RESISTANCE:
            case ALLRESIST:
            case STUNLIKE_RESISTANCE:
            case ELEMENTAL_RESISTANCE_DARK:
            case ELEMENTAL_RESISTANCE_LIGHT:
            case ABNORMAL_RESISTANCE_ALL:
                applyLimit = false;
                break;
        }

        if (isBonus()) {
            chkValue = Math.round(value);
            if (applyLimit) {
                if (chkValue + currentStat < 0)
                    return -currentStat;
                else
                    return chkValue;
            }
            else
                return chkValue;
        }
        else {
            chkValue = Math.round(stat + value);
            if (applyLimit) {
                if (chkValue < 0)
                    return 0;
                else
                    return chkValue;
            }
            else
                return chkValue;
        }
    }

    @Override
    public StatModifierPriority getPriority() {
        return StatModifierPriority.MEDIUM;
    }

    public static AddModifier newInstance(StatEnum stat, int value, boolean isBonus) {
        AddModifier m = new AddModifier();
        m.setStat(stat);
        m.setValue(value);
        m.setBonus(isBonus);
        m.nextId();
        return m;
    }
}
