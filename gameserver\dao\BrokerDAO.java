/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dao;

import gameserver.model.gameobjects.BrokerItem;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.StorageType;

import java.util.List;

public abstract class BrokerDAO implements IDFactoryAwareDAO {
    public abstract List<BrokerItem> loadBroker();
    
    public abstract List<Item> getItems(StorageType storageType);

    public abstract boolean store(BrokerItem brokerItem);

    @Override
    public final String getClassName() {
        return BrokerDAO.class.getName();
    }
}
