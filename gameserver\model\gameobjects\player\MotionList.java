/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.player;

import gameserver.network.aion.serverpackets.SM_MOTION;
import gameserver.utils.PacketSendUtility;

import java.util.Collection;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */
public class MotionList {
    private LinkedHashMap<Integer, PlayerMotion> motions;
    private Player owner;

    public static class PlayerMotion {
        private Motion motion;
        private long date;
        private long expireTime;

        private PlayerMotion(Motion motion, long date, long expireTime) {
            this.motion = motion;
            this.date = date;
            this.expireTime = expireTime;
        }

        public Motion getMotion() {
            return motion;
        }

        public int getMotionId() {
            return motion != null ? motion.getMotionId() : 0;
        }

        public long getDate() {
            return date;
        }

        public long getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(long expireTime) {
            this.expireTime = expireTime;
        }
    }

    public MotionList() {
        this.motions = new LinkedHashMap<Integer, PlayerMotion>();
        this.owner = null;
    }

    public void setOwner(Player owner) {
        this.owner = owner;
    }

    public Player getOwner() {
        return owner;
    }

    public boolean addMotion(int motionId, long motion_date, long motion_expires_time) {
        if (!motions.containsKey(motionId)) {
            motions.put(motionId, new PlayerMotion(Motion.getMotionById(motionId), motion_date,
                motion_expires_time));
        }
        else {
            return false;
        }

        if (owner != null) {
            PacketSendUtility.sendPacket(owner, new SM_MOTION(owner));
        }
        return true;
    }

    public boolean canAddMotion(int motionId) {
        return !motions.containsKey(motionId);
    }

    public void delMotion(int motionId) {
        if (motions.containsKey(motionId)) {
            motions.remove(motionId);
        }
    }

    public int size() {
        return motions.size();
    }

    public Collection<PlayerMotion> getMotions() {
        return motions.values();
    }
}
