/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.controllers.attack.AttackStatus;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.Battleground;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class EggController extends MonsterController {
    private boolean cracked = false;
    private int hitChance;
    private Battleground bg;

    public EggController(Battleground bg, int hitChance) {
        this.bg = bg;
        this.hitChance = hitChance;
    }

    @Override
    public void onDie(Creature lastAttacker) {
        int playerObjId = -1;

        if (lastAttacker instanceof Player)
            playerObjId = ((Player) lastAttacker).getObjectId();

        bg.onArtifactDie(playerObjId);
    }

    @Override
    public void onAttack(Creature creature, int skillId, TYPE type, int damage, int logId,
        AttackStatus status, boolean notifyAttackedObservers, boolean sendPacket) {
        damage = 0;

        super.onAttack(creature, skillId, type, damage, logId, status, notifyAttackedObservers,
            sendPacket);

        Creature master = creature;
        if (!(creature instanceof Player)) {
            master = creature.getMaster();

            if (master == null || !(master instanceof Player))
                return;
        }

        Player player = (Player) master;
        if (Rnd.get(100) < hitChance)
            eggDie(player.getObjectId());
    }

    private void eggDie(int playerObjId) {
        if (!cracked) {
            cracked = true;
            bg.onArtifactDie(playerObjId);
        }
        super.onDelete();
    }
}