/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.controllers.attack.AttackStatus;
import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.ActionObserver.ObserverType;
import gameserver.controllers.movement.StartMovingListener;
import gameserver.model.EmotionType;
import gameserver.model.Race;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.RequestResponseHandler;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_QUESTION_WINDOW;
import gameserver.network.aion.serverpackets.SM_RIFT_ANNOUNCE;
import gameserver.network.aion.serverpackets.SM_USE_OBJECT;
import gameserver.services.LadderService;
import gameserver.services.RespawnService;
import gameserver.services.TeleportService;
import gameserver.spawnengine.RiftSpawnManager.RiftEnum;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;
import gameserver.world.WorldMapInstance;

import java.util.List;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class RiftController extends NpcController {
    private boolean isMaster = false;
    private Npc slave;

    private Race destination;
    private Integer maxEntries;
    private Integer maxLevel;

    private int usedEntries;
    private boolean isAccepting;

    private long lifetime;

    private int exitMapId;
    private float exitX;
    private float exitY;
    private float exitZ;

    private boolean vortex = false;

    /**
     * For custom rifts
     */
    private List<SpawnPosition> destinations = null;
    private int mode = 0;

    /**
     * Used to create master rifts or slave rifts (slave == null)
     * 
     * @param slaveSpawnTemplate
     */

    public RiftController(Npc slave, RiftEnum riftTemplate, int lifetime) {
        this.lifetime = System.currentTimeMillis() + lifetime;

        if (slave != null) { // master rift should be created
            this.slave = slave;
            this.maxEntries = riftTemplate.getEntries();
            this.maxLevel = riftTemplate.getMaxLevel();
            this.destination = riftTemplate.getDestination();

            this.exitMapId = slave.getSpawn().getWorldId();
            this.exitX = slave.getSpawn().getX();
            this.exitY = slave.getSpawn().getY();
            this.exitZ = slave.getSpawn().getZ();

            isMaster = true;
            isAccepting = true;
        }
    }

    public RiftController(int maxEntries, int maxLevel, Race destination, int lifetime,
        int exitMapId, float exitX, float exitY, float exitZ) {
        this.lifetime = System.currentTimeMillis() + lifetime;

        this.maxEntries = maxEntries;
        this.maxLevel = maxLevel;
        this.destination = destination;

        this.exitMapId = exitMapId;
        this.exitX = exitX;
        this.exitY = exitY;
        this.exitZ = exitZ;

        isMaster = true;
        isAccepting = true;
    }

    public RiftController(List<SpawnPosition> destinations) {
        this.destinations = destinations;
    }

    public RiftController(List<SpawnPosition> destinations, int mode) {
        this.destinations = destinations;
        this.mode = mode;
    }

    public RiftController(List<SpawnPosition> destinations, int lifetime, int maxEntries,
        int maxLevel) {
        this.destinations = destinations;
        this.lifetime = System.currentTimeMillis() + lifetime;

        this.maxEntries = maxEntries;
        this.maxLevel = maxLevel;

        this.vortex = true;

        isMaster = true;
        isAccepting = true;
    }

    @Override
    public void onDialogRequest(final Player player) {
        final int defaultUseTime;

        if (getOwner().getNpcId() == 701044)
            defaultUseTime = 8000;
        else
            defaultUseTime = 2000;

        PacketSendUtility.sendPacket(player, new SM_USE_OBJECT(player.getObjectId(), getOwner()
            .getObjectId(), defaultUseTime, 1));
        PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player,
            EmotionType.START_QUESTLOOT, 0, getOwner().getObjectId()), true);

        player.getController().cancelTask(TaskId.PORTAL_USE);

        final ActionObserver observer = new StartMovingListener() {
            @Override
            public void moved() {
                if (player.getController().hasActiveTask(TaskId.PORTAL_USE)) {
                    player.getController().cancelTask(TaskId.PORTAL_USE);
                    PacketSendUtility.sendPacket(player, new SM_USE_OBJECT(player.getObjectId(),
                        getOwner().getObjectId(), defaultUseTime, 0));
                    PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player,
                        EmotionType.END_QUESTLOOT, 0, getOwner().getObjectId()), true);
                }
            }
        };
        player.getObserveController().attach(observer);

        player.getObserveController().attach(new ActionObserver(ObserverType.ATTACKED) {
            @Override
            public void attacked(Creature creature) {
                if (player.getController().hasActiveTask(TaskId.PORTAL_USE)) {
                    player.getController().cancelTask(TaskId.PORTAL_USE);
                    PacketSendUtility.sendPacket(player, new SM_USE_OBJECT(player.getObjectId(),
                        getOwner().getObjectId(), defaultUseTime, 0));
                    PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player,
                        EmotionType.END_QUESTLOOT, 0, getOwner().getObjectId()), true);
                }
            }
        });

        player.getController().addNewTask(TaskId.PORTAL_USE,
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    player.getObserveController().removeObserver(observer);

                    PacketSendUtility.sendPacket(player, new SM_USE_OBJECT(player.getObjectId(),
                        getOwner().getObjectId(), defaultUseTime, 0));
                    PacketSendUtility.broadcastPacket(player, new SM_EMOTION(player,
                        EmotionType.END_QUESTLOOT, 0, getOwner().getObjectId()), true);

                    handleRequest(player);
                }
            }, defaultUseTime));
    }

    private void handleRequest(final Player player) {
        if (LadderService.getInstance().isInQueue(player)
            || (player.isInGroup() && LadderService.getInstance()
                .isInQueue(player.getPlayerGroup())) || player.getBattleground() != null) {
            PacketSendUtility.sendMessage(player,
                "You cannot use this while in queue for battlegrounds or similar.");
            return;
        }

        if (isCustom()) {
            if (getOwner().getNpcId() != 700137) {
                if (vortex && !isAccepting) {
                    PacketSendUtility.sendMessage(player, "The vortex can no longer be entered.");
                    return;
                }

                SpawnPosition pos = destinations.get(Rnd.get(destinations.size()));
                TeleportService.teleportTo(player, pos.getMapId(), pos.getX(), pos.getY(),
                    pos.getZ(), TeleportService.TELEPORT_PORTAL_DELAY);

                if (mode == 1) {
                    ThreadPoolManager.getInstance().schedule(new Runnable() {
                        @Override
                        public void run() {
                            player.setLawless(true);
                        }
                    }, TeleportService.TELEPORT_PORTAL_DELAY + 500);
                }
                else if (mode == 2) {
                    ThreadPoolManager.getInstance().schedule(new Runnable() {
                        @Override
                        public void run() {
                            player.setBandit(true);
                        }
                    }, TeleportService.TELEPORT_PORTAL_DELAY + 500);
                }

                if (vortex) {
                    usedEntries++;

                    if (usedEntries >= maxEntries) {
                        isAccepting = false;

                        RespawnService.scheduleDecayTask(getOwner());
                    }

                    getOwner().getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
                        public boolean run(Player player) {
                            if (player.isSpawned()) {
                                PacketSendUtility.sendPacket(player, new SM_RIFT_ANNOUNCE(
                                    getOwner(), isAccepting));
                            }
                            return true;
                        }
                    });
                }
            }
            else {
                RequestResponseHandler responseHandler = new RequestResponseHandler(getOwner()) {
                    @Override
                    public void acceptRequest(Creature requester, Player responder) {
                        if (destinations.size() == 0 || !requester.isSpawned())
                            return;

                        SpawnPosition pos = destinations.get(Rnd.get(destinations.size()));
                        TeleportService.teleportTo(responder, pos.getMapId(), pos.getX(),
                            pos.getY(), pos.getZ(), 0);

                        if (mode == 1) {
                            final Player _responder = responder;

                            ThreadPoolManager.getInstance().schedule(new Runnable() {
                                @Override
                                public void run() {
                                    _responder.setLawless(true);
                                }
                            }, 500);
                        }
                        else if (mode == 2) {
                            final Player _responder = responder;

                            ThreadPoolManager.getInstance().schedule(new Runnable() {
                                @Override
                                public void run() {
                                    _responder.setBandit(true);
                                }
                            }, 500);
                        }
                    }

                    @Override
                    public void denyRequest(Creature requester, Player responder) {
                    }
                };

                boolean requested = player.getResponseRequester().putRequest(
                    SM_QUESTION_WINDOW.STR_USE_RIFT, responseHandler);
                if (requested) {
                    PacketSendUtility.sendPacket(player, new SM_QUESTION_WINDOW(
                        SM_QUESTION_WINDOW.STR_USE_RIFT, 0));
                }
            }
        }
        else {
            if (!isMaster && !isAccepting)
                return;

            if (player.getCommonData().getRace() == destination)
                return;

            RequestResponseHandler responseHandler = new RequestResponseHandler(getOwner()) {
                @Override
                public void acceptRequest(Creature requester, Player responder) {
                    if (!isAccepting)
                        return;

                    int worldId = exitMapId;
                    float x = exitX;
                    float y = exitY;
                    float z = exitZ;

                    TeleportService.teleportTo(responder, worldId, x, y, z, 0);
                    usedEntries++;

                    if (usedEntries >= maxEntries) {
                        isAccepting = false;

                        RespawnService.scheduleDecayTask(getOwner());

                        if (slave != null)
                            RespawnService.scheduleDecayTask(slave);
                    }

                    // PacketSendUtility.broadcastPacket(getOwner(), new SM_RIFT_STATUS(getOwner()
                    // .getObjectId(), usedEntries, maxEntries, maxLevel));
                    if (getOwner().isSpawned()) {
                        getOwner().getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
                            public boolean run(Player player) {
                                if (player.isSpawned()
                                    && destination != player.getCommonData().getRace()) {
                                    PacketSendUtility.sendPacket(player, new SM_RIFT_ANNOUNCE(
                                        getOwner(), isAccepting));
                                }
                                return true;
                            }
                        });
                    }
                }

                @Override
                public void denyRequest(Creature requester, Player responder) {
                    // do nothing
                }
            };

            boolean requested = player.getResponseRequester().putRequest(
                SM_QUESTION_WINDOW.STR_USE_RIFT, responseHandler);
            if (requested) {
                PacketSendUtility.sendPacket(player, new SM_QUESTION_WINDOW(
                    SM_QUESTION_WINDOW.STR_USE_RIFT, 0));
            }
        }
    }

    @Override
    public void see(VisibleObject object) {
        if (!isMaster)
            return;

        if (object instanceof Player) {
            // PacketSendUtility.sendPacket((Player) object, new SM_RIFT_STATUS(getOwner()
            // .getObjectId(), usedEntries, maxEntries, maxLevel));
        }
    }

    @Override
    public void onAttack(final Creature creature, int skillId, TYPE type, int damage, int logId,
        AttackStatus status, boolean notifyAttackedObservers, boolean sendPacket) {
        return;
    }

    /**
     * @param activePlayer
     */
    public void sendMessage(Player activePlayer) {
        if (isMaster && getOwner().isSpawned()
            && activePlayer.getCommonData().getRace() != destination) {
            // if (!vortex)
            // PacketSendUtility.sendPacket(activePlayer, new SM_RIFT_ANNOUNCE(destination, true,
            // false));

            PacketSendUtility.sendPacket(activePlayer, new SM_RIFT_ANNOUNCE(getOwner()));

            if (vortex)
                PacketSendUtility.sendPacket(activePlayer, new SM_RIFT_ANNOUNCE(getOwner(),
                    isAccepting));
        }
    }

    /**
     *
     */
    public void sendAnnounce() {
        if (isMaster && getOwner().isSpawned()) {
            WorldMapInstance worldInstance = getOwner().getWorldMapInstance();

            worldInstance.doOnAllPlayers(new Executor<Player>() {
                @Override
                public boolean run(Player player) {
                    sendMessage(player);
                    return true;
                }
            });
        }
    }

    public boolean isCustom() {
        return destinations != null;
    }

    public int getRemainingEntries() {
        return maxEntries - usedEntries;
    }

    public int getRemainingLifeTime() {
        return (int) ((lifetime - System.currentTimeMillis()) / 1000);
    }

    public Integer getMaxLevel() {
        return maxLevel;
    }

    public Integer getMaxEntries() {
        return maxEntries;
    }

    public boolean isAccepting() {
        return isAccepting;
    }

    public boolean isVortex() {
        return vortex;
    }

    public void setVortex(boolean isVortex) {
        this.vortex = isVortex;
    }
}
