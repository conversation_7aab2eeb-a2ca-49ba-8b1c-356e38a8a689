/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.eventengine.Event;
import gameserver.utils.ThreadPoolManager;

import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * This event is meant to be used to time IterativeEvents. Its a do nothing event alternatively it can block other
 * events some time.
 * 
 * <AUTHOR>
 * 
 */
public class DelayEvent extends Event {

    private final long delay;
    private ScheduledFuture<?> last_future;

    public DelayEvent() {
        this(0, TimeUnit.MILLISECONDS);
    }

    public DelayEvent(int delay, TimeUnit unit) {
        this.delay = TimeUnit.MILLISECONDS.convert(delay, unit);
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#execute()
     */
    @Override
    protected void execute() {
        Runnable runnable = new Runnable() {

            @Override
            public void run() {
                finish();
            }
        };
        last_future = ThreadPoolManager.getInstance().schedule(runnable, delay);
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#onReset()
     */
    @Override
    protected void onReset() {
        last_future = null;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#cancel(boolean)
     */
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        if (last_future != null) {
            if (!last_future.isDone()) {
                last_future.cancel(mayInterruptIfRunning);
                finish();
                return true;
            }
        }
        return false;
    }

}
