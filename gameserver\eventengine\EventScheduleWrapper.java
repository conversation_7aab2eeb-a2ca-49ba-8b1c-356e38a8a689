/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine;

import gameserver.utils.ThreadPoolManager;

import java.util.concurrent.ScheduledFuture;

/**
 * <AUTHOR>
 * 
 */
class EventScheduleWrapper implements Runnable {
    private static final int RECHECK_DELAY = 2; // in min

    private final Event event;
    private boolean first = true;
    private ScheduledFuture<?> last_future;

    public EventScheduleWrapper(Event event) {
        this.event = event;
    }

    /*
     * (non-Javadoc)
     * @see java.lang.Runnable#run()
     */
    @Override
    public void run() {
        if (last_future != null) {
            if (!last_future.isDone())
                return;
        }
        if (!check()) {
            Runnable runnable = new Runnable() {

                @Override
                public void run() {
                    check();
                }
            };
            last_future = ThreadPoolManager.getInstance().schedule(runnable,
                RECHECK_DELAY * 60 * 1000);
        }

    }

    private boolean check() {
        if (event.isFinished() || first) {
            first = false;
            EventScheduler.getInstance().schedule(event, 10);
            return true;
        }
        else {
            event.cancel(true);
            return false;
        }
    }

}
