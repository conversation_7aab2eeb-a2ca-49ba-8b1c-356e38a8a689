/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.model.gameobjects.player;

/**
 * <AUTHOR>
 */
public enum Motion {
    NINJA_IDLE(1, 1, "anim_ninja_idle"),
    NINJA_RUN(2, 2, "anim_ninja_run"),
    NINJA_JUMP(3, 3, "anim_ninja_jump"),
    NINJA_REST(4, 4, "anim_ninja_rest"),

    HOVERI<PERSON>_IDLE(5, 1, "anim_hovering_idle"),
    HOVERING_RUN(6, 2, "anim_hovering_run"),
    HOVERING_JUMP(7, 3, "anim_hovering_jump"),
    HOVERING_REST(8, 4, "anim_hovering_rest"),
    
    SHOP_TEST01(9, 5, "anim_test01_shop"),
    SHOP_SPECIAL(10, 5, "anim_special_shop"),
    
    BRUCELEE_IDLE(11, 1, "anim_BruceLee_idle"),
    BRUCELEE_RUN(12, 2, "anim_BruceLee_run"),
    BRUCELEE_JUMP(13, 3, "anim_BruceLee_jump"),
    BRUCELEE_REST(14, 4, "anim_BruceLee_rest"),
    
    BRUCELEE_IDLE_2(15, 1, "anim_BruceLee_idle_01"),
    BRUCELEE_RUN_2(16, 2, "anim_BruceLee_run_01"),
    BRUCELEE_JUMP_2(17, 3, "anim_BruceLee_jump_01"),
    BRUCELEE_REST_2(18, 4, "anim_BruceLee_rest_01"),
    
    SHOP_SPECIAL_02(19, 5, "anim_special_shop_01"),
    CELLPHONE_IDLE(20, 1, "anim_cellphone_idle"),
    BOXING_IDLE(21, 1, "anim_boxing_idle"),
    BOXING_2_IDLE(22, 1, "anim_boxing2_idle"),
    
    CLOUDMASTER_IDLE(23, 1, "anim_cloudmaster_idle"),
    CLOUDMASTER_RUN(24, 2, "anim_cloudmaster_run"),
    CLOUDMASTER_REST(25, 4, "anim_cloudmaster_rest"),
    CLOUDMASTER_JUMP(26, 3, "anim_cloudmaster_jump");

    private int motionId;
    private int type;
    private String name;

    private Motion(int motionId, int type, String name) {
        this.motionId = motionId;
        this.type = type;
        this.name = name;
    }

    public int getMotionId() {
        return motionId;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static Motion getMotionById(int motionId) {
        for (Motion motion : values())
            if (motion.getMotionId() == motionId)
                return motion;
        return null;
    }

    public static Motion getMotionByName(String name) {
        for (Motion motion : values())
            if (motion.getName().equals(name))
                return motion;
        return null;
    }
}
