/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.network.aion.serverpackets.SM_LEVEL_UPDATE;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.World;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
public class ThreeTeamKingBg extends Battleground {
    private int extraCounter = 0;
    private int holdingTeamIndex = -1;
    private Map<Integer, Integer> teamPoints = new ConcurrentHashMap<Integer, Integer>();

    private int artifactHitLimit = 3;
    private int artifactNpcId = 275300;
    private SpawnPosition artifactSpawnPos = null;
    private Npc artifact = null;
    private ReentrantLock artifactLock = new ReentrantLock();
    private long kingTimer = 0;
    private ReentrantLock pointsLock = new ReentrantLock();

    public ThreeTeamKingBg() {
        super.name = "3-Team King of the Hill";
        super.description = "You are on a team and must capture and hold the Hill for as long as possible to win this match. In the center, there is an artifact you must hit a certain number of times to capture the Hill.";
        super.minSize = 2;
        super.maxSize = 4;
        super.teamCount = 3;
        super.matchLength = 330;

        BattlegroundMap map1 = new BattlegroundMap(220050000);
        map1.addSpawn(new SpawnPosition(1289.0f, 2528.0f, 78.75f));
        map1.addSpawn(new SpawnPosition(1210.0f, 2640.0f, 93.76f));
        map1.addSpawn(new SpawnPosition(1348.0f, 2626.0f, 93.44f));
        map1.setKillZ(65f);

        BattlegroundMap map2 = new BattlegroundMap(320080000);
        map2.addSpawn(new SpawnPosition(811.4f, 368.8f, 320.6f));
        map2.addSpawn(new SpawnPosition(738.9f, 370.6f, 320.4f));
        map2.addSpawn(new SpawnPosition(765.0f, 501.6f, 319.9f));
        map2.setKillZ(300f);

        BattlegroundMap map3 = new BattlegroundMap(300350000);
        map3.addSpawn(new SpawnPosition(665.7f, 1772.4f, 223.6f));
        map3.addSpawn(new SpawnPosition(679.7f, 1772.3f, 222.1f));
        map3.addSpawn(new SpawnPosition(680.7f, 1785.2f, 222.5f));
        map3.addStaticDoor(192);
        map3.addStaticDoor(190);
        map3.addStaticDoor(196);
        map3.addStaticDoor(191);
        map3.addStaticDoor(200);
        map3.addStaticDoor(189);
        map3.addStaticDoor(197);
        map3.addStaticDoor(195);
        map3.addStaticDoor(193);
        map3.addStaticDoor(194);
        map3.addStaticDoor(199);
        map3.addStaticDoor(198);
        map3.setKillZ(140f);

        BattlegroundMap map4 = new BattlegroundMap(600090000);
        map4.addSpawn(new SpawnPosition(787, 589, 152));
        map4.addSpawn(new SpawnPosition(877, 439, 146));
        map4.addSpawn(new SpawnPosition(709, 435, 144));
        map4.setKillZ(135f);

        BattlegroundMap map5 = new BattlegroundMap(210040000);
        map5.addSpawn(207.3f, 2444.8f, 124f);
        map5.addSpawn(224f, 2573.5f, 124f);
        map5.addSpawn(145.1f, 2517.7f, 129f);
        map5.setKillZ(113f);

        BattlegroundMap map6 = new BattlegroundMap(300160000);
        map6.addSpawn(564f, 1363.1f, 189f);
        map6.addSpawn(564f, 1234f, 188f);
        map6.addSpawn(662.5f, 1298.5f, 187f);
        map6.setKillZ(184f);

        BattlegroundMap map7 = new BattlegroundMap(220040000);
        map7.addSpawn(401.3f, 321.6f, 231f);
        map7.addSpawn(322.1f, 408.6f, 232f);
        map7.addSpawn(320.2f, 302.7f, 232.1f);
        map7.setKillZ(225f);
        map7.setRestrictFlight(true);
        
        BattlegroundMap map8 = new BattlegroundMap(600100000);
        map8.addSpawn(191.4f, 1633.2f, 230f);
        map8.addSpawn(200.1f, 1522.8f, 226f);
        map8.addSpawn(268.1f, 1517.6f, 226f);
        map8.setKillZ(222f);
        
        BattlegroundMap map9 = new BattlegroundMap(210070000);
        map9.addSpawn(439f, 1714f, 457f);
        map9.addSpawn(484.3f, 1641.3f, 465f);
        map9.addSpawn(368f, 1636.3f, 460f);
        map9.setKillZ(450f);
        
        BattlegroundMap map10 = new BattlegroundMap(301510000);
        map10.addSpawn(940f, 1042f, 71f);
        map10.addSpawn(948.6f, 1141.8f, 71f);
        map10.addSpawn(1026f, 1127.5f, 71f);
        map10.setKillZ(65f);

        super.maps.add(map1);
        super.maps.add(map2);
        // super.maps.add(map3);
        super.maps.add(map4);
        super.maps.add(map5);
        super.maps.add(map6);
        super.maps.add(map7);
        super.maps.add(map8);
        super.maps.add(map9);
        super.maps.add(map10);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        if (super.getMap().getMapId() == 220050000)
            artifactSpawnPos = new SpawnPosition(1280.93f, 2619.76f, 99.27355f);
        else if (super.getMap().getMapId() == 320080000)
            artifactSpawnPos = new SpawnPosition(776.97f, 431.27f, 321.7541f);
        else if (super.getMap().getMapId() == 300350000)
            artifactSpawnPos = new SpawnPosition(675.43f, 1779.69f, 149.101172f);
        else if (super.getMap().getMapId() == 600090000)
            artifactSpawnPos = new SpawnPosition(791, 489, 144);
        else if (super.getMap().getMapId() == 210040000)
            artifactSpawnPos = new SpawnPosition(210f, 2510f, 119.5f);
        else if (super.getMap().getMapId() == 300160000)
            artifactSpawnPos = new SpawnPosition(592f, 1298.5f, 187.81041f);
        else if (super.getMap().getMapId() == 220040000)
            artifactSpawnPos = new SpawnPosition(350.4f, 355.0f, 229.60413f);
        else if (super.getMap().getMapId() == 600100000)
            artifactSpawnPos = new SpawnPosition(238.6f, 1581.3f, 227.77995f);
        else if (super.getMap().getMapId() == 210070000)
            artifactSpawnPos = new SpawnPosition(426.1f, 1655.0f, 455.1447f);
        else if (super.getMap().getMapId() == 301510000)
            artifactSpawnPos = new SpawnPosition(979f, 1089f, 70.271454f);

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 30000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 30000);

        startBattleground(30000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups() <= 1)
                    endThreeTeamKingMatch();
            }
        });

        spawnArtifact(super.getGroups().get(0).size());

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    @Override
                    public void run() {
                        extraCounter++;

                        checkHoldingTeamDistance();

                        if ((extraCounter % 3) == 0) {
                            showHoldingTeam();
                        }

                        if ((extraCounter % 5) == 0) {
                            givePointToHoldingTeam();
                        }

                        if ((extraCounter % 15) == 0) {
                            showTeamPositions();
                        }

                        if ((extraCounter % 30) == 0) {
                            extraCounter = 0;
                            autoResurrection();
                        }
                    }
                }, 1 * 1000, 1 * 1000));
            }
        }, 30 * 1000);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endThreeTeamKingMatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public boolean createTournament(List<List<Player>> teams) {
        if (!super.createGroups(teams))
            return false;

        this.matchLength = 510;
        startMatch();

        return true;
    }

    @Override
    public synchronized void onArtifactDie(int teamIndex) {
        artifactLock.lock();

        try {
            if (artifact != null) {
                artifact.getController().delete();
                artifact = null;
            }
        }
        finally {
            artifactLock.unlock();
        }

        holdingTeamIndex = teamIndex;
        kingTimer = System.currentTimeMillis() + 45 * 1000; // 45 seconds

        String msg = LadderService.getInstance().getNameByIndex(teamIndex)
            + " has become the King of the Hill!";
        for (PlayerGroup group : super.getGroups())
            for (Player pl : group.getMembers())
                super.scheduleAnnouncement(pl, msg, 0);
        super.specAnnounce(msg);
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    private synchronized void checkHoldingTeamDistance() {
        if ((holdingTeamIndex < 0 && artifact == null)
            || (artifact != null && (!artifact.isSpawned() || artifact.getKnownList()
                .getPlayersCount() == 0))) {
            spawnArtifact(super.getGroups().get(0).size());

            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers())
                    scheduleAnnouncement(pl,
                        "The Hill wants a new King - the artifact has spawned!", 0);
            }
            super.specAnnounce("The Hill wants a new King - the artifact has spawned!");

            return;
        }

        if (artifact == null) {
            if (kingTimer != 0 && kingTimer < System.currentTimeMillis()) {
                spawnArtifact(super.getGroups().get(0).size());

                for (PlayerGroup group : super.getGroups()) {
                    for (Player pl : group.getMembers())
                        scheduleAnnouncement(pl,
                            "The Hill wants a new King - the artifact has spawned!", 0);
                }
                super.specAnnounce("The Hill wants a new King - the artifact has spawned!");
            }
        }

        if (holdingTeamIndex < 0 || artifact != null || holdingTeamIndex > super.getGroups().size())
            return;

        float distance = 100;

        PlayerGroup holdingTeam = super.getGroups().get(holdingTeamIndex);
        if (holdingTeam == null)
            return;

        if (artifact == null) {
            int deadCounter = 0;
            for (Player pl : holdingTeam.getMembers())
                if (pl.getLifeStats().isAlreadyDead() || pl.getLifeStats().getCurrentHp() <= 0)
                    deadCounter++;

            if (deadCounter >= holdingTeam.size()) {
                spawnArtifact(super.getGroups().get(0).size());

                for (PlayerGroup group : super.getGroups()) {
                    for (Player pl : group.getMembers())
                        super
                            .scheduleAnnouncement(
                                pl,
                                "The holding team is dead. Keep hitting the artifact until someone becomes the new King of the Hill!",
                                0);
                }
                super
                    .specAnnounce("The holding team is dead. Keep hitting the artifact until someone becomes the new King of the Hill!");
            }
        }

        if (artifact == null) {
            for (Player pl : holdingTeam.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead() || pl.getLifeStats().getCurrentHp() <= 0)
                    continue;

                float dist = (float) MathUtil.getDistance(pl, artifactSpawnPos.getX(),
                    artifactSpawnPos.getY(), artifactSpawnPos.getZ());
                if (dist < distance)
                    distance = dist;
            }

            if (distance > 18) {
                spawnArtifact(super.getGroups().get(0).size());

                for (PlayerGroup group : super.getGroups()) {
                    for (Player pl : group.getMembers())
                        scheduleAnnouncement(
                            pl,
                            "The holding team has moved too far away from the Hill and the artifact has spawned!",
                            0);
                }
                super
                    .specAnnounce("The holding team has moved too far away from the Hill and the artifact has spawned!");
            }
        }
    }

    private void showHoldingTeam() {
        if (holdingTeamIndex < 0 || holdingTeamIndex > super.getGroups().size() - 1)
            return;

        for (Player pl : super.getGroups().get(holdingTeamIndex).getMembers()) {
            PacketSendUtility.broadcastPacketAndReceive(pl, new SM_LEVEL_UPDATE(pl.getObjectId(),
                5, pl.getLevel()));
        }
    }

    private synchronized void givePointToHoldingTeam() {
        if (holdingTeamIndex < 0 || holdingTeamIndex > super.getGroups().size() - 1)
            return;

        addPoints(holdingTeamIndex, 1);
    }

    private synchronized void addPoints(int teamIndex, int points) {
        pointsLock.lock();

        try {
            Integer result = teamPoints.get(teamIndex);
            if (result != null)
                teamPoints.put(teamIndex, result + points);
            else
                teamPoints.put(teamIndex, points);
        }
        finally {
            pointsLock.unlock();
        }
    }

    private synchronized void showTeamPositions() {
        String msg;
        if (holdingTeamIndex == -1)
            msg = "No King of the Hill";
        else
            msg = LadderService.getInstance().getNameByIndex(holdingTeamIndex)
                + " is King of the Hill";

        msg += String.format("\nBlue: %d - Green: %d - Yellow: %d",
            teamPoints.containsKey((Integer) 0) ? teamPoints.get((Integer) 0) : 0,
            teamPoints.containsKey((Integer) 1) ? teamPoints.get((Integer) 1) : 0,
            teamPoints.containsKey((Integer) 2) ? teamPoints.get((Integer) 2) : 0);

        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers())
                super.scheduleAnnouncement(pl, msg, 0);
        }
        super.specAnnounce(msg);
    }

    private synchronized void autoResurrection() {
        for (PlayerGroup group : super.getGroups()) {
            if (group.getBgIndex() == holdingTeamIndex)
                continue; // Do not resurrect group members of the holding team

            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead()) {
                    pl.getReviveController().fullRevive();
                    super.spawnProtection(pl);
                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    TeleportService.teleportTo(pl, getMapId(), super.getInstanceId(), pos.getX(),
                        pos.getY(), pos.getZ(), 0);
                    super
                        .scheduleAnnouncement(pl, "You have been automatically resurrected!", 1500);
                }
            }
        }
    }

    private synchronized void spawnArtifact(int groupSize) {
        try {
            artifactLock.lock();
            if (artifact != null) {
                artifact.getController().onRespawn();
                World.getInstance().spawn(artifact);
            }
            else {
                artifact = SpawnEngine.getInstance().spawnKingOfTheHillArtifact(this,
                    artifactHitLimit * groupSize, getMapId(), artifactNpcId,
                    artifactSpawnPos.getX(), artifactSpawnPos.getY(), artifactSpawnPos.getZ(),
                    (byte) 0);
            }

            kingTimer = System.currentTimeMillis() + 45 * 1000;
        }
        finally {
            artifactLock.unlock();
        }
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super
            .scheduleAnnouncement(
                player,
                "You will automatically resurrect every 30 seconds unless you are King of the Hill.",
                0);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in ThreeTeamKingBg!");
            }
            else {
                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 1);

                if (player.getPlayerGroup().getBgIndex() == holdingTeamIndex) {
                    addPoints(killer.getPlayerGroup().getBgIndex(), 2);

                    super.announceAll(LadderService.getInstance().getNameByIndex(
                        killer.getPlayerGroup().getBgIndex())
                        + " has slain a player from the holding team for 2 points!");
                }
            }
        }
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups() <= 1)
            endThreeTeamKingMatch();
    }

    private PlayerGroup getWinner() {
        PlayerGroup winner = null;
        int winPoints = 0;
        int drawPoints = 0;

        for (PlayerGroup group : super.getGroups()) {
            int points = 0;

            if (teamPoints.containsKey(group.getBgIndex()))
                points = teamPoints.get(group.getBgIndex());

            if (winner == null && points > drawPoints) {
                winner = group;
                winPoints = points;
            }
            else if (winner == null) {
                continue;
            }
            else if (winPoints < points) {
                winner = group;
                winPoints = points;
            }
            else if (winPoints == points) {
                drawPoints = winPoints;
                winner = null;
            }
        }

        return winner;
    }

    private void endThreeTeamKingMatch() {
        super.onEndFirstDefault();

        if (artifact != null)
            artifact.getController().onDelete();

        PlayerGroup winner = getWinner();

        if (winner == null) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    scheduleAnnouncement(pl, "The match was a draw! Better luck next time.", 0);
                    super.rewardPlayer(pl, 20, false);
                }
            }

            super.specAnnounce("The match was a draw!");
        }
        else {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == winner.getGroupId()) {
                    for (Player pl : group.getMembers()) {
                        int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2 : 1;

                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE * 2 / 3);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.freezePlayer(pl, 5000);

                        super.scheduleAnnouncement(pl, "Your team has won the match with "
                            + teamPoints.get(group.getBgIndex()) + " points!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                        super.rewardPlayer(pl, 30, true);
                    }
                }
                else {
                    for (Player pl : group.getMembers()) {
                        super.playerLoseMatch(pl, -super.K_VALUE / 3);

                        super.scheduleAnnouncement(
                            pl,
                            "Your team has lost the match with "
                                + teamPoints.get(group.getBgIndex()) + " to "
                                + teamPoints.get(winner.getBgIndex()) + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);
                        super.rewardPlayer(pl, 20, false);
                    }
                }
            }
            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match with " + teamPoints.get(winner.getBgIndex()) + " points!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}