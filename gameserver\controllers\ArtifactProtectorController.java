/*
 * This file is part of aion-emu <aion-emu.com>.
 *
 *  aion-emu is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-emu is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-emu.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Servant;
import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.Trap;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.siege.ArtifactProtector;
import gameserver.services.PvpService;
import gameserver.services.SiegeService;
import gameserver.utils.MathUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 
 */
public class ArtifactProtectorController extends NpcController {

    @Override
    public void onDie(Creature lastAttacker) {
        if (lastAttacker instanceof Player || lastAttacker instanceof Summon
            || lastAttacker instanceof Trap || lastAttacker instanceof Servant) {
            Player taker;
            if (lastAttacker instanceof Player)
                taker = (Player) lastAttacker;
            else if (lastAttacker instanceof Summon)
                taker = ((Summon) lastAttacker).getMaster();
            else if (lastAttacker instanceof Trap)
                taker = (Player) ((Trap) lastAttacker).getCreator();
            else if (lastAttacker instanceof Servant)
                taker = (Player) ((Servant) lastAttacker).getMaster();
            else
                taker = null;

            if (taker != null) {
                SiegeService.getInstance().onArtifactCaptured(getOwner().getArtifact(), taker);

                List<Player> attackers = new ArrayList<Player>();
                for (Player pl : taker.getWorldMapInstance().getPlayers())
                    if (MathUtil.getDistance(getOwner(), pl) < 30
                        && pl.getCommonData().getRace() == taker.getCommonData().getRace())
                        attackers.add(pl);

                final int ARTIFACT_CAPTURE_REWARD_MIGHT = 40 / Math.max(attackers.size(), 2);
                final int ARTIFACT_CAPTURE_REWARD_AP = 4000 / Math.max(attackers.size(), 2);

                for (Player attacker : attackers) {
                    PvpService.getInstance().addMight(attacker, ARTIFACT_CAPTURE_REWARD_MIGHT);
                    attacker.getCommonData().addAp(ARTIFACT_CAPTURE_REWARD_AP);
                }
            }
            else {
                SiegeService.getInstance().onArtifactCaptured(getOwner().getArtifact());
            }
        }
        else {
            SiegeService.getInstance().onArtifactCaptured(getOwner().getArtifact());
        }

        super.onDie(lastAttacker);
    }

    @Override
    public ArtifactProtector getOwner() {
        return (ArtifactProtector) super.getOwner();
    }
}
