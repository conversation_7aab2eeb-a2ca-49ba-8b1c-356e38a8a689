/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.dao.MightDAO;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.utils.ThreadPoolManager;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class TwoTeamSmallBg extends Battleground {
    private Map<Integer, Integer> roundResults = new HashMap<Integer, Integer>();
    private int maxRounds = 3;
    private int roundsDone = 0;
    private boolean endCalled = false;

    public TwoTeamSmallBg() {
        super.name = "2-Team Small";
        super.description = "You are on a team and must kill the enemy team. There are "
            + maxRounds
            + " rounds. The team which has won the most rounds by the end of the match is the winner.";
        super.minSize = 2;
        super.maxSize = 4;
        super.teamCount = 2;
        super.mapId = 320030000;
        super.matchLength = 115;

        BattlegroundMap map1 = new BattlegroundMap(320030000);
        map1.addSpawn(new SpawnPosition(252.0f, 262.0f, 228.23f));
        map1.addSpawn(new SpawnPosition(275.0f, 168.0f, 204.35f));
        map1.setKillZ(193f);

        BattlegroundMap map2 = new BattlegroundMap(300360000);
        map2.addSpawn(new SpawnPosition(1887.1f, 1737.4f, 311.7f));
        map2.addSpawn(new SpawnPosition(1817.5f, 1737.3f, 311.7f));
        map2.addStaticDoor(148);
        map2.addStaticDoor(149);
        map2.addStaticDoor(150);
        map2.addStaticDoor(151);
        map2.setKillZ(300f);

        BattlegroundMap map3 = new BattlegroundMap(320080000);
        map3.addSpawn(new SpawnPosition(236.3f, 453.3f, 507.8f));
        map3.addSpawn(new SpawnPosition(255.9f, 530.0f, 504.7f));
        map3.setKillZ(495f);

        BattlegroundMap map4 = new BattlegroundMap(320130000);
        map4.addSpawn(new SpawnPosition(419.2f, 710.9f, 157.9f));
        map4.addSpawn(new SpawnPosition(486.8f, 739.9f, 158.0f));
        map4.setKillZ(154f);

        BattlegroundMap map5 = new BattlegroundMap(301140000); // 4.0
        map5.addSpawn(new SpawnPosition(1056.0f, 645.0f, 280.0f));
        map5.addSpawn(new SpawnPosition(1056.0f, 719.0f, 280.0f));
        map5.setKillZ(278f);

        BattlegroundMap map6 = new BattlegroundMap(301190000); // 4.0
        map6.addSpawn(new SpawnPosition(259.0f, 210.0f, 189.0f));
        map6.addSpawn(new SpawnPosition(121.0f, 210.0f, 189.0f));
        map6.setKillZ(185f);

        BattlegroundMap map7 = new BattlegroundMap(600070000); // 4.0
        map7.addSpawn(new SpawnPosition(1132.0f, 255.0f, 560.0f));
        map7.addSpawn(new SpawnPosition(1156.0f, 355.0f, 565.0f));
        map7.setKillZ(552f);

        BattlegroundMap map8 = new BattlegroundMap(600070000); // 4.0
        map8.addSpawn(new SpawnPosition(276.0f, 608.0f, 574.0f));
        map8.addSpawn(new SpawnPosition(179.0f, 651.0f, 574.0f));
        map8.setKillZ(564f);

        BattlegroundMap map9 = new BattlegroundMap(210040000);
        map9.addSpawn(new SpawnPosition(1959f, 2599f, 149f));
        map9.addSpawn(new SpawnPosition(1900f, 2629f, 153f));
        map9.setKillZ(134);

        BattlegroundMap map10 = new BattlegroundMap(300200000); // Haramel
        map10.addSpawn(new SpawnPosition(150f, 33f, 145f));
        map10.addSpawn(new SpawnPosition(150f, 132f, 145f));
        map10.setKillZ(141f);

        BattlegroundMap map11 = new BattlegroundMap(301130000);
        map11.addSpawn(new SpawnPosition(342f, 293f, 160f));
        map11.addSpawn(new SpawnPosition(423f, 292f, 160f));
        map11.setKillZ(156f);
        map11.setHighestZ(168f);
        
        BattlegroundMap map12 = new BattlegroundMap(301510000);
        map12.addSpawn(821f, 1074f, 56f);
        map12.addSpawn(821f, 1136f, 55f);
        map12.setKillZ(50f);
        
        BattlegroundMap map13 = new BattlegroundMap(300170000);
        map13.addSpawn(1202.5f, 1233f, 286f);
        map13.addSpawn(1158.5f, 1180.5f, 286f);
        map13.setKillZ(280f);

        super.maps.add(map1);
        super.maps.add(map2);
        super.maps.add(map3);
        super.maps.add(map4);
        super.maps.add(map5);
        super.maps.add(map9);
        super.maps.add(map10);
        super.maps.add(map11);
        super.maps.add(map12);
        super.maps.add(map13);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 25000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 25000);

        startBattleground(25000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups(0) <= 1) {
                    roundsDone = maxRounds;
                    endTwoTeamMatch(true);
                }
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamMatch(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in TwoTeamSmallBg!");
            }
            else {
                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, is2v2() ? 2 : 3);
            }
        }

        if (player.getPlayerGroup() == null) {
            log.error("PlayerGroup == null in TwoTeamSmallBg!");
            endTwoTeamMatch(false);
        }
        else {
            int deadCounter = 0;
            for (Player pl : player.getPlayerGroup().getMembers()) {
                if (pl.getLifeStats().isAlreadyDead())
                    deadCounter++;
            }

            if (deadCounter == player.getPlayerGroup().size())
                endTwoTeamMatch(false);
        }
    }

    @Override
    public boolean createTournament(List<List<Player>> teams) {
        if (!super.createGroups(teams))
            return false;

        this.maxRounds = 5;
        this.description = "You are on a team and must kill the enemy team. There are "
            + maxRounds
            + " rounds. The team which has won the most rounds by the end of the match is the winner.";
        this.matchLength = 165;
        startMatch();

        return true;
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (is2v2() && !player.getController().isInShutdownProgress())
            DAOManager.getDAO(MightDAO.class).addMight(player, -25);

        if (isStarted() && getRemainingGroups(0) <= 1) {
            roundsDone = maxRounds;
            endTwoTeamMatch(false);
        }
    }

    private void startNewRound() {
        super.setStartStamp(System.currentTimeMillis());
        super.killSummonedCreatures();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                int delay = 0;

                synchronized (getGroups()) {
                    for (PlayerGroup group : getGroups()) {
                        for (Player pl : group.getMembers()) {
                            preparePlayer(pl, 7000, false);

                            SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                            if (pos != null)
                                TeleportService.teleportTo(pl, getMapId(), pos.getX(), pos.getY(),
                                    pos.getZ(), delay += 250);
                        }
                    }
                }
            }
        }, 3000);

        for (Player pl : super.getSpectators())
            super.createTimer(pl, getMatchLength());

        super.specAnnounce("The round has begun!!!", 10000);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamMatch(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endCalled = false;
                for (PlayerGroup group : getGroups()) {
                    if (group.size() < 1) {
                        roundsDone = maxRounds;
                        endTwoTeamMatch(false);
                    }
                }
            }
        }, 15000);
    }

    private void addRoundWin(PlayerGroup group) {
        if (group == null)
            return;

        Integer result = roundResults.get(group.getBgIndex());
        if (result != null)
            roundResults.put(group.getBgIndex(), result + 1);
        else
            roundResults.put(group.getBgIndex(), 1);
    }

    private PlayerGroup getRoundWinner() {
        PlayerGroup winner = null;

        Map<PlayerGroup, Integer> deadCounts = new HashMap<PlayerGroup, Integer>();
        for (PlayerGroup group : super.getGroups()) {
            int deadCounter = 0;

            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead())
                    deadCounter++;
            }

            deadCounts.put(group, deadCounter);
        }

        int winDeadCount = Collections.min(deadCounts.values());
        int winnerCount = 0;
        for (Integer deadCount : deadCounts.values())
            if (deadCount == winDeadCount)
                winnerCount++;

        if (winnerCount == 1)
            for (Map.Entry<PlayerGroup, Integer> entry : deadCounts.entrySet())
                if (entry.getValue() == winDeadCount)
                    winner = entry.getKey();

        return winner;
    }

    private PlayerGroup getWinner() {
        PlayerGroup winner = null;
        int drawPoints = 0;

        for (Map.Entry<Integer, Integer> entry : roundResults.entrySet()) {
            if (winner == null && entry.getValue() > drawPoints) {
                winner = super.getGroups().get(entry.getKey());
            }
            else if (winner == null) {
                continue;
            }
            else if (roundResults.get(winner.getBgIndex()) < entry.getValue()) {
                winner = super.getGroups().get(entry.getKey());
            }
            else if (roundResults.get(winner.getBgIndex()) == entry.getValue()) {
                drawPoints = roundResults.get(winner.getBgIndex());
                winner = null;
            }
        }

        return winner;
    }

    private void endTwoTeamMatch(boolean isDraw) {
        if (endCalled)
            return;

        endCalled = true;

        for (PlayerGroup group : super.getGroups())
            for (Player pl : group.getMembers())
                super.freezeNoEnd(pl);

        super.onEndFirstDefault();

        PlayerGroup roundWinner = getRoundWinner();
        if (roundWinner != null)
            isDraw = false;

        if (roundsDone < maxRounds) {
            roundsDone++;

            if (!isDraw)
                addRoundWin(roundWinner);
        }

        if (roundsDone < maxRounds) {
            if (!isDraw) {
                if (roundWinner != null) {
                    for (PlayerGroup group : super.getGroups())
                        for (Player pl : group.getMembers())
                            super.scheduleAnnouncement(pl, LadderService.getInstance()
                                .getNameByIndex(roundWinner.getBgIndex())
                                + " has won round "
                                + roundsDone + "!", 0);
                    super.specAnnounce(LadderService.getInstance().getNameByIndex(
                        roundWinner.getBgIndex())
                        + " has won round " + roundsDone + "!");
                }
            }
            else {
                for (PlayerGroup group : super.getGroups())
                    for (Player pl : group.getMembers())
                        super.scheduleAnnouncement(pl, "Round " + roundsDone + " was a draw!", 0);
                super.specAnnounce("Round " + roundsDone + " was a draw!");
            }

            startNewRound();
            return;
        }

        PlayerGroup winner = getWinner();
        if (winner == null) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.scheduleAnnouncement(pl, "The match was a draw! Better luck next time.",
                        0);

                    if (is2v2()) {
                        if (super.isEligibleForRewards(pl)) {
                            PvpService.getInstance().addMight(pl, 5);
                        }
                    }
                    else {
                        super.rewardPlayer(pl, 15, false);
                    }
                }
            }
            super.specAnnounce("The match was a draw!");
        }
        else {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == winner.getGroupId()) {
                    for (Player pl : group.getMembers()) {
                        if (!is2v2()) {
                            int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2
                                : 1;

                            if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                                .getMaxRatingDiff()
                                * averageRating
                                / (float) group.getAverageRating())
                                super.playerWinMatch(pl, premadeCheck * super.K_VALUE / 2);
                            else
                                super
                                    .message(pl,
                                        "You have not been credited with the win due to the match being heavily unfair.");
                        }

                        super.scheduleAnnouncement(pl, "Your team has won the match!", 0);
                        super.scheduleAnnouncement(pl, "You have been rewarded with might "
                            + (is2v2() ? "for your effort!" : "and rating for your great effort!"),
                            3000);

                        if (is2v2()) {
                            if (super.isEligibleForRewards(pl)) {
                                PvpService.getInstance().addMight(pl, 10);
                            }
                        }
                        else {
                            super.rewardPlayer(pl, 20, true);
                        }
                    }
                }
                else {
                    for (Player pl : group.getMembers()) {
                        if (!is2v2()) {
                            super.playerLoseMatch(pl, -super.K_VALUE / 3);
                        }

                        super.scheduleAnnouncement(pl, "Your team has lost the match!", 0);
                        super.scheduleAnnouncement(pl, "You have received some might"
                            + (is2v2() ? "." : " but lost rating."), 3000);

                        if (is2v2()) {
                            if (super.isEligibleForRewards(pl)) {
                                PvpService.getInstance().addMight(pl, 5);
                            }
                        }
                        else {
                            super.rewardPlayer(pl, 15, false);
                        }
                    }
                }
            }
            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}