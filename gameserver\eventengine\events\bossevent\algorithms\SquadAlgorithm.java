/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events.bossevent.algorithms;

import gameserver.eventengine.events.bossevent.PointAlgorithm;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.MathUtil;
import gameserver.world.World;

import java.awt.Point;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class SquadAlgorithm implements PointAlgorithm {
    private static final Logger log = Logger.getLogger(SquadAlgorithm.class);

    private static final int SQUARE_SIZE = 50;
    private static final Point START_POINT = new Point(0, 0);
    private static final Point END_POINT = new Point(1500, 1500);

    private Map<Integer, SquareMap> maps = new HashMap<Integer, SquareMap>();

    public SquadAlgorithm() {
        createMap(600010000); // Pre-create Silentera map
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.events.bossevent.PointAlgorithm#getBiggestCluster(int)
     */
    @Override
    public Point getBiggestCluster(int mapid) {
        createMap(mapid);

        SquareMap squareMap = maps.get(mapid);
        Collection<Player> worldPlayers = World.getInstance().getWorldMap(mapid)
            .getWorldMapInstance().getPlayers();
        List<Player> players = new ArrayList<Player>(worldPlayers);

        for (Square square : squareMap) {
            square.resetFactor();
            for (Player pl : players) {
                if (pl == null)
                    continue;
                if (square.isInside(pl.getX(), pl.getY())) {
                    square.increaseFactor(3);
                    for (Integer sqId : square.getNearAdjacentList()) {
                        squareMap.get(sqId).increaseFactor(2);
                    }
                    for (Integer sqId : square.getFarAdjacentList()) {
                        squareMap.get(sqId).increaseFactor(1);
                    }
                }
            }
        }

        List<Square> squareList = new ArrayList<Square>();
        int biggestFactor = 0;

        for (Square sq : squareMap) {
            if (sq.getFactor() > biggestFactor) {
                squareList.clear();
                squareList.add(sq);
                biggestFactor = sq.getFactor();
            }
            else if (sq.getFactor() == biggestFactor)
                squareList.add(sq);
        }

        return squareList.get(Rnd.get(squareList.size())).centerPoint;
    }

    @Override
    public int getEfficiency(int mapid) {
        return PointAlgorithm.MOST_EFFICIENT;
    }

    private void createMap(int mapid) {
        SquareMap squareMap;

        if (!maps.containsKey(mapid)) {
            maps.put(mapid, new SquareMap(900));
            squareMap = maps.get(mapid);

            for (int iX = START_POINT.x; iX < END_POINT.x; iX += SQUARE_SIZE) {
                for (int iY = START_POINT.y; iY < END_POINT.y; iY += SQUARE_SIZE) {
                    Square square = new Square(iX, iY, iX + SQUARE_SIZE, iY + SQUARE_SIZE);
                    square.setId(squareMap.size());
                    squareMap.add(square.getId(), square);
                }
            }

            float squaredSize = 1 + (float) Math.sqrt(squareMap.size() * squareMap.size() * 2);
            for (Square square : squareMap) {
                for (Square sq : squareMap) {
                    if (square.getId() == sq.getId())
                        continue;
                    double dist = MathUtil.getDistance(square.getCenter(), sq.getCenter());
                    if (dist <= squaredSize)
                        square.addNearAdjacentSquare(sq.getId());
                    else if (dist <= (squaredSize * 2))
                        square.addFarAdjacentSquare(sq.getId());
                }
            }

            log.debug("Created a SquareMap for map [" + mapid + "] with " + squareMap.size()
                + " squares.");
        }
    }

    private static class Square {
        private Point upperLeftCorner;
        private Point lowerRightCorner;
        private Point centerPoint;
        private List<Integer> nearAdjacentList = new ArrayList<Integer>();
        private List<Integer> farAdjacentList = new ArrayList<Integer>();

        private int uniqueId;
        private int factor = 0;

        public Square(int x1, int y1, int x2, int y2) {
            upperLeftCorner = new Point(x1, y1);
            lowerRightCorner = new Point(x2, y2);
            centerPoint = new Point((x1 + x2) / 2, (y1 + y2) / 2);
        }

        @SuppressWarnings("unused")
        public Square() {
            upperLeftCorner = new Point();
            lowerRightCorner = new Point();
        }

        public void setId(int id) {
            uniqueId = id;
        }

        public int getId() {
            return uniqueId;
        }

        public void addNearAdjacentSquare(int id) {
            if (!nearAdjacentList.contains(id))
                nearAdjacentList.add(id);
        }

        public List<Integer> getNearAdjacentList() {
            return nearAdjacentList;
        }

        public void addFarAdjacentSquare(int id) {
            if (!farAdjacentList.contains(id))
                farAdjacentList.add(id);
        }

        public List<Integer> getFarAdjacentList() {
            return farAdjacentList;
        }

        public boolean isInside(float x, float y) {
            if (x >= upperLeftCorner.x && x <= lowerRightCorner.x && y >= upperLeftCorner.y
                && y <= lowerRightCorner.y)
                return true;
            return false;
        }

        public void resetFactor() {
            this.factor = 0;
        }

        public void increaseFactor(int factor) {
            this.factor += factor;
        }

        public int getFactor() {
            return this.factor;
        }

        public Point getCenter() {
            return centerPoint;
        }
    }

    @SuppressWarnings("serial")
    private static class SquareMap extends ArrayList<Square> {
        public SquareMap(int i) {
            super(i);
        }
    }
}
