/*
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, 
 * MA  02110-1301, USA.
 *
 * http://www.gnu.org/copyleft/gpl.html
 */
package gameserver.geoEngine2;

import gameserver.configs.main.GeoDataConfig;
import gameserver.geoEngine2.collision.CollisionResult;
import gameserver.geoEngine2.collision.CollisionResults;
import gameserver.geoEngine2.math.Vector3f;
import gameserver.geoEngine2.scene.DoorGeometry;
import gameserver.geoEngine2.scene.Spatial;
import gameserver.model.BoundRadius;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.MathUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FilenameUtils;
import org.apache.log4j.Logger;

/**
 * 
 * <AUTHOR>
 */
public class GeoEngine2 {
    private static Logger log = Logger.getLogger(GeoEngine2.class);

    private Map<Integer, GeoMap> geoMaps = new HashMap<Integer, GeoMap>();

    public GeoEngine2() {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.isEmpty()) {
            Map<String, Spatial> models = GeoWorldLoader.loadMeshs();

            List<String> results = new ArrayList<String>();
            File[] files = new File(GeoWorldLoader.GEO_DIR).listFiles();

            for (File file : files) {
                if (file.isFile() && !file.getName().equalsIgnoreCase("meshs.geo"))
                    results.add(FilenameUtils.removeExtension(file.getName()));
            }

            for (String worldId : results) {
                GeoMap geoMap = new GeoMap(worldId);

                if (GeoWorldLoader.loadWorld(Integer.parseInt(worldId), models, geoMap))
                    geoMaps.put(Integer.parseInt(worldId), geoMap);
            }

            models.clear();
            models = null;

            log.info("Geodata engine: " + geoMaps.size() + " geoMaps loaded!");
        }
        else if (!GeoDataConfig.GEO_ENABLE) {
            log.info("Geodata engine disabled.");
        }
    }

    public boolean isInBounds(Creature creature) {
        return isInBounds(creature.getWorldId(), creature.getX(), creature.getY(), creature.getZ());
    }

    public boolean isInBounds(int worldId, float x, float y, float z) {
        if (x < 0 || y < 0 || z < 0)
            return false;

        if (Float.isNaN(x) || Float.isNaN(y) || Float.isNaN(z))
            return false;

        int worldSize = getWorldSize(worldId);
        if (x > worldSize || y > worldSize || z > 4000)
            return false;

        return true;
    }

    public int getWorldSize(int worldId) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            return geoMaps.get(worldId).getWorldSize();
        else
            return 3072;
    }

    public float getZ(Creature creature) {
        return getZ(creature.getWorldId(), creature.getX(), creature.getY(), creature.getZ());
    }

    public float getZ(int worldId, float x, float y, float z) {
        return getZ(worldId, x, y, z, 2F);
    }

    public float getZ(int worldId, float x, float y, float z, float zFix) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            return geoMaps.get(worldId).getZ(x, y, z, zFix);
        else
            return z;
    }

    public boolean canSee(VisibleObject object, VisibleObject target) {
        float x1, x2, y1, y2, z1, z2;

        if (object.getObjectId() == target.getObjectId())
            return true;

        BoundRadius targetBound = target.getBoundRadius();
        BoundRadius objectBound = object.getBoundRadius();

        float dist = (float) (MathUtil.getDistance(object, target) - targetBound.getCollision() - objectBound
            .getCollision());

        // dist -= 1f;

        if (dist <= 0)
            return true;

        if (object instanceof Player) {
            Player pl1 = (Player) object;
            x1 = pl1.getMoveController().getOriginX();
            y1 = pl1.getMoveController().getOriginY();
            z1 = pl1.getMoveController().getOriginZ();
        }
        else {
            x1 = object.getX();
            y1 = object.getY();
            z1 = object.getZ();
        }

        if (target instanceof Player) {
            Player pl2 = (Player) target;
            x2 = pl2.getMoveController().getOriginX();
            y2 = pl2.getMoveController().getOriginY();
            z2 = pl2.getMoveController().getOriginZ();
        }
        else {
            x2 = target.getX();
            y2 = target.getY();
            z2 = target.getZ();
        }

        z1 += objectBound.getUpper() / 4;
        z2 += targetBound.getUpper() / 4;

        return this.canSee(object.getWorldId(), x1, y1, z1, x2, y2, z2);
    }

    public boolean canSee(int worldId, float x, float y, float z, float targetX, float targetY,
        float targetZ) {
        return this.canSee(worldId, x, y, z, targetX, targetY, targetZ, true);
    }

    public boolean canSee(int worldId, float x, float y, float z, float targetX, float targetY,
        float targetZ, boolean terrainCheck) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            return geoMaps.get(worldId).canSee(x, y, z, targetX, targetY, targetZ, terrainCheck);
        else
            return true;
    }

    public float getLowestZ(int worldId, float x, float y) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            return geoMaps.get(worldId).getLowestZ(x, y);
        else
            return 0;
    }

    public float getHighestZ(int worldId, float x, float y) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            return geoMaps.get(worldId).getHighestZ(x, y);
        else
            return 0;
    }

    public CollisionResults getCollisions(Creature object, float x, float y, float z) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(object.getWorldId()))
            return geoMaps.get(object.getWorldId()).getCollisions(object.getX(), object.getY(),
                object.getZ(), x, y, z);
        else
            return new CollisionResults();
    }

    public Vector3f getClosestCollision(Creature object, float x, float y, float z) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(object.getWorldId()))
            return geoMaps.get(object.getWorldId()).getClosestCollisionPoint(object.getX(),
                object.getY(), object.getZ(), x, y, z);
        else
            return new Vector3f(x, y, z);
    }

    public CollisionResult getClosestCollisionResult(Creature object, float x, float y, float z) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(object.getWorldId()))
            return geoMaps.get(object.getWorldId()).getClosestCollision(object.getX(),
                object.getY(), object.getZ(), x, y, z);
        else
            return new CollisionResult(new Vector3f(x, y, z), 0);
    }

    public float getBicubicZ(int worldId, float x, float y) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            return geoMaps.get(worldId).getBicubicZ(x, y);
        else
            return 0;
    }

    public float getBilinearZ(int worldId, float x, float y) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            return geoMaps.get(worldId).getBilinearZ(x, y);
        else
            return 0;
    }

    public int getTerrainInfo(int worldId, float x, float y) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            return geoMaps.get(worldId).getTerrainInfo(x, y);
        else
            return 0;
    }

    public GeoMap getGeoMapByWorldId(int worldId) {
        return geoMaps.get(worldId);
    }
    
    public void setDoorOpen(int worldId, int instanceId, int doorId, boolean open) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            geoMaps.get(worldId).setDoorOpen(instanceId, doorId, open);
    }
    
    public void attachDoor(int worldId, int doorId, DoorGeometry door) {
        if (GeoDataConfig.GEO_ENABLE && geoMaps.containsKey(worldId))
            geoMaps.get(worldId).attachDoor(doorId, door);
    }

    @SuppressWarnings("synthetic-access")
    private static class SingletonHolder {
        protected static final GeoEngine2 instance = new GeoEngine2();
    }

    public static final GeoEngine2 getInstance() {
        return SingletonHolder.instance;
    }
}
