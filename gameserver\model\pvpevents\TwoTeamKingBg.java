/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.dataholders.DataManager;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.MathUtil;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.World;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
public class TwoTeamKingBg extends Battleground {
    private int extraCounter = 0;
    private int holdingTeamIndex = -1;
    private Map<Integer, Integer> teamPoints = new ConcurrentHashMap<Integer, Integer>();

    private int artifactHitLimit = 3;
    private int artifactNpcId = 275300;
    private SpawnPosition artifactSpawnPos = null;
    private Npc artifact = null;
    private ReentrantLock artifactLock = new ReentrantLock();
    private long kingTimer = 0;
    private ReentrantLock pointsLock = new ReentrantLock();

    public TwoTeamKingBg() {
        super.name = "2-Team King of the Hill";
        super.description = "You are on a team and must capture and hold the Hill for as long as possible to win this match. In the center, there is an artifact you must hit a certain number of times to capture the Hill.";
        super.minSize = 2;
        super.maxSize = 4;
        super.teamCount = 2;
        super.matchLength = 300;

        BattlegroundMap map1 = new BattlegroundMap(301190000);
        map1.addSpawn(new SpawnPosition(259.0f, 210.0f, 189.0f));
        map1.addSpawn(new SpawnPosition(121.0f, 210.0f, 189.0f));
        map1.setKillZ(185f);

        BattlegroundMap map2 = new BattlegroundMap(210080000);
        map2.addSpawn(290f, 143f, 500f);
        map2.addSpawn(237f, 144f, 500f);
        map2.setKillZ(495f);

        BattlegroundMap map3 = new BattlegroundMap(300200000); // Haramel
        map3.addSpawn(new SpawnPosition(150f, 33f, 145f));
        map3.addSpawn(new SpawnPosition(150f, 132f, 145f));
        map3.setKillZ(141f);

        BattlegroundMap map6 = new BattlegroundMap(400030000);
        map6.addSpawn(new SpawnPosition(570.2f, 476.2f, 676f));
        map6.addSpawn(new SpawnPosition(449.2f, 549.8f, 676f));
        map6.setKillZ(672f);
        map6.setHighestZ(683f);

        BattlegroundMap map10 = new BattlegroundMap(301130000);
        map10.addSpawn(446f, 446f, 182f);
        map10.addSpawn(512.5f, 529.2f, 182f);
        map10.setKillZ(180f);
        map10.setHighestZ(190f);

        BattlegroundMap map11 = new BattlegroundMap(300150000);
        map11.addSpawn(617f, 503f, 137f);
        map11.addSpawn(657.6f, 502.8f, 137f);
        map11.setKillZ(132f);

        BattlegroundMap map12 = new BattlegroundMap(300160000);
        map12.addSpawn(1606.3f, 994.6f, 111f);
        map12.addSpawn(1658.9f, 962.8f, 110f);
        map12.setKillZ(101f);
        
        BattlegroundMap map13 = new BattlegroundMap(301310000);
        map13.addSpawn(222.6f, 259.1f, 90f);
        map13.addSpawn(306.4f, 259.1f, 90f);
        map13.setKillZ(83f);
        
        BattlegroundMap map14 = new BattlegroundMap(210070000);
        map14.addSpawn(795.5f, 1862f, 463f);
        map14.addSpawn(702, 1863f, 463f);
        map14.setKillZ(455f);
        
        BattlegroundMap map15 = new BattlegroundMap(301510000);
        map15.addSpawn(793.8f, 1389.1f, 195.2f);
        map15.addSpawn(844.8f, 1388.6f, 195.2f);
        map15.addStaticDoor(18);
        map15.addStaticDoor(11);
        map15.setKillZ(190f);
        
        BattlegroundMap map16 = new BattlegroundMap(300510000);
        map16.addSpawn(796f, 1101f, 502f);
        map16.addSpawn(796f, 1035f, 502f);
        map16.setKillZ(495f);
        
        BattlegroundMap map17 = new BattlegroundMap(300170000);
        map17.addSpawn(1589.5f, 1529.2f, 305f);
        map17.addSpawn(1545.4f, 1562.4f, 305f);
        map17.setKillZ(300f);

        super.maps.add(map2);
        super.maps.add(map3);
        super.maps.add(map6);
        super.maps.add(map10);
        super.maps.add(map11);
        super.maps.add(map12);
        //super.maps.add(map13);
        super.maps.add(map14);
        super.maps.add(map15);
        super.maps.add(map16);
        super.maps.add(map17);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        if (super.getMap().getMapId() == 301190000)
            artifactSpawnPos = new SpawnPosition(190.14f, 209.39f, 189.49937f);
        else if (super.getMap().getMapId() == 210080000)
            artifactSpawnPos = new SpawnPosition(263.48f, 229.67f, 502.2163f);
        else if (super.getMap().getMapId() == 300200000)
            artifactSpawnPos = new SpawnPosition(181.10f, 82.20f, 145.42949f);
        else if (super.getMap().getMapId() == 400030000)
            artifactSpawnPos = new SpawnPosition(509.6f, 512.5f, 675.09094f);
        else if (super.getMap().getMapId() == 301130000)
            artifactSpawnPos = new SpawnPosition(457.0f, 506.0f, 181.88536f);
        else if (super.getMap().getMapId() == 300150000)
            artifactSpawnPos = new SpawnPosition(636.5f, 435.4f, 137.30743f);
        else if (super.getMap().getMapId() == 300160000)
            artifactSpawnPos = new SpawnPosition(1664.7f, 1033.3f, 106.342705f);
        else if (super.getMap().getMapId() == 301310000)
            artifactSpawnPos = new SpawnPosition(264.8f, 259.0f, 85.81963f);
        else if (super.getMap().getMapId() == 210070000)
            artifactSpawnPos = new SpawnPosition(749f, 1882f, 462.4564f);
        else if (super.getMap().getMapId() == 301510000)
            artifactSpawnPos = new SpawnPosition(819.5f, 1420.5f, 195.0f);
        else if (super.getMap().getMapId() == 300510000)
            artifactSpawnPos = new SpawnPosition(745.4f, 1068.2f, 501.18976f);
        else if (super.getMap().getMapId() == 300170000)
            artifactSpawnPos = new SpawnPosition(1603.2f, 1593.1f, 307.0335f);

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 30000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 30000);

        startBattleground(30000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups(0) <= 1)
                    endTwoTeamKingMatch();
            }
        });

        spawnArtifact(super.getGroups().get(0).size());

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    @Override
                    public void run() {
                        extraCounter++;

                        checkHoldingTeamDistance();

                        if ((extraCounter % 5) == 0) {
                            givePointToHoldingTeam();
                            buffAttackingTeam();
                        }

                        if ((extraCounter % 15) == 0)
                            showTeamPositions();

                        if ((extraCounter % 15) == 0) {
                            extraCounter = 0;
                            autoResurrection();
                        }
                    }
                }, 1 * 1000, 1 * 1000));
            }
        }, 30 * 1000);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endTwoTeamKingMatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public boolean createTournament(List<List<Player>> teams) {
        if (!super.createGroups(teams))
            return false;

        this.matchLength = 510;
        startMatch();

        return true;
    }

    @Override
    public synchronized void onArtifactDie(int teamIndex) {
        artifactLock.lock();

        try {
            if (artifact != null) {
                artifact.getController().delete();
                artifact = null;
            }
        }
        finally {
            artifactLock.unlock();
        }

        holdingTeamIndex = teamIndex;
        kingTimer = System.currentTimeMillis() + 30 * 1000; // 30 seconds

        String msg = LadderService.getInstance().getNameByIndex(teamIndex)
            + " has become the King of the Hill!";
        for (PlayerGroup group : super.getGroups())
            for (Player pl : group.getMembers())
                super.scheduleAnnouncement(pl, msg, 0);
        super.specAnnounce(msg);
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    private void buffAttackingTeam() {
        if (holdingTeamIndex == -1)
            return;

        for (PlayerGroup group : super.getGroups()) {
            if (group.getBgIndex() == holdingTeamIndex)
                continue;

            for (Player player : group.getMembers()) {
                SkillTemplate template = DataManager.SKILL_DATA.getSkillTemplate(8408);
                Effect effect = new Effect(player, player, template, 1, 5500);
                player.getEffectController().addEffect(effect);
                effect.addAllEffectToSucess();
                effect.startEffect(true);
            }
        }
    }

    private synchronized void checkHoldingTeamDistance() {
        if ((holdingTeamIndex < 0 && artifact == null)
            || (artifact != null && (!artifact.isSpawned() || artifact.getKnownList()
                .getPlayersCount() == 0))) {
            spawnArtifact(super.getGroups().get(0).size());

            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers())
                    scheduleAnnouncement(pl,
                        "The Hill wants a new King - the artifact has spawned!", 0);
            }
            super.specAnnounce("The Hill wants a new King - the artifact has spawned!");

            return;
        }

        if (artifact == null) {
            if (kingTimer != 0 && kingTimer < System.currentTimeMillis()) {
                spawnArtifact(super.getGroups().get(0).size());

                for (PlayerGroup group : super.getGroups()) {
                    for (Player pl : group.getMembers())
                        scheduleAnnouncement(pl,
                            "The Hill wants a new King - the artifact has spawned!", 0);
                }
                super.specAnnounce("The Hill wants a new King - the artifact has spawned!");
            }
        }

        if (holdingTeamIndex < 0 || artifact != null || holdingTeamIndex > super.getGroups().size())
            return;

        float distance = 100;

        PlayerGroup holdingTeam = super.getGroups().get(holdingTeamIndex);
        if (holdingTeam == null)
            return;

        if (artifact == null) {
            int deadCounter = 0;
            for (Player pl : holdingTeam.getMembers())
                if (pl.getLifeStats().isAlreadyDead() || pl.getLifeStats().getCurrentHp() <= 0)
                    deadCounter++;

            if (deadCounter >= holdingTeam.size()) {
                spawnArtifact(super.getGroups().get(0).size());

                for (PlayerGroup group : super.getGroups()) {
                    for (Player pl : group.getMembers())
                        super
                            .scheduleAnnouncement(
                                pl,
                                "The holding team is dead. Keep hitting the artifact until someone becomes the new King of the Hill!",
                                0);
                }
                super
                    .specAnnounce("The holding team is dead. Keep hitting the artifact until someone becomes the new King of the Hill!");
            }
        }

        if (artifact == null) {
            for (Player pl : holdingTeam.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead() || pl.getLifeStats().getCurrentHp() <= 0)
                    continue;

                float dist = (float) MathUtil.getDistance(pl, artifactSpawnPos.getX(),
                    artifactSpawnPos.getY(), artifactSpawnPos.getZ());
                if (dist < distance)
                    distance = dist;
            }

            if (distance > 18) {
                spawnArtifact(super.getGroups().get(0).size());

                for (PlayerGroup group : super.getGroups()) {
                    for (Player pl : group.getMembers())
                        scheduleAnnouncement(
                            pl,
                            "The holding team has moved too far away from the Hill and the artifact has spawned!",
                            0);
                }
                super
                    .specAnnounce("The holding team has moved too far away from the Hill and the artifact has spawned!");
            }
        }
    }

    private synchronized void givePointToHoldingTeam() {
        if (holdingTeamIndex < 0 || holdingTeamIndex > super.getGroups().size() - 1)
            return;

        addPoints(holdingTeamIndex, 1);
    }

    private synchronized void addPoints(int teamIndex, int points) {
        pointsLock.lock();

        try {
            Integer result = teamPoints.get(teamIndex);
            if (result != null)
                teamPoints.put(teamIndex, result + points);
            else
                teamPoints.put(teamIndex, points);
        }
        finally {
            pointsLock.unlock();
        }
    }

    private synchronized void showTeamPositions() {
        String msg;
        if (holdingTeamIndex == -1)
            msg = "No King of the Hill";
        else
            msg = LadderService.getInstance().getNameByIndex(holdingTeamIndex)
                + " is King of the Hill";

        msg += String.format("\nBlue: %d - Green: %d",
            teamPoints.containsKey((Integer) 0) ? teamPoints.get((Integer) 0) : 0,
            teamPoints.containsKey((Integer) 1) ? teamPoints.get((Integer) 1) : 0);

        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers())
                super.scheduleAnnouncement(pl, msg, 0);
        }
        super.specAnnounce(msg);
    }

    private synchronized void autoResurrection() {
        for (PlayerGroup group : super.getGroups()) {
            if (group.getBgIndex() == holdingTeamIndex)
                continue; // Do not resurrect group members of the holding team

            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead()) {
                    pl.getReviveController().fullRevive();
                    super.spawnProtection(pl);
                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    TeleportService.teleportTo(pl, getMapId(), super.getInstanceId(), pos.getX(),
                        pos.getY(), pos.getZ(), 0);
                    super
                        .scheduleAnnouncement(pl, "You have been automatically resurrected!", 1500);
                }
            }
        }
    }

    private synchronized void spawnArtifact(int groupSize) {
        try {
            artifactLock.lock();
            if (artifact != null) {
                artifact.getController().onRespawn();
                World.getInstance().spawn(artifact);
            }
            else {
                artifact = SpawnEngine.getInstance().spawnKingOfTheHillArtifact(this,
                    artifactHitLimit * groupSize, getMapId(), artifactNpcId,
                    artifactSpawnPos.getX(), artifactSpawnPos.getY(), artifactSpawnPos.getZ(),
                    (byte) 0);
            }

            kingTimer = System.currentTimeMillis() + 30 * 1000; // 30 seconds
        }
        finally {
            artifactLock.unlock();
        }
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super
            .scheduleAnnouncement(
                player,
                "You will automatically resurrect every 15 seconds unless you are King of the Hill.",
                0);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in TwoTeamKingBg!");
            }
            else {
                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 1);

                addPoints(killer.getPlayerGroup().getBgIndex(), 1);

                super.announceAll(LadderService.getInstance().getNameByIndex(
                    killer.getPlayerGroup().getBgIndex())
                    + " has slain an enemy!");
            }
        }
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups(0) <= 1)
            endTwoTeamKingMatch();
    }

    private PlayerGroup getWinner() {
        PlayerGroup winner = null;
        int winPoints = 0;
        int drawPoints = 0;

        for (PlayerGroup group : super.getGroups()) {
            int points = 0;

            if (teamPoints.containsKey(group.getBgIndex()))
                points = teamPoints.get(group.getBgIndex());

            if (winner == null && points > drawPoints) {
                winner = group;
                winPoints = points;
            }
            else if (winner == null) {
                continue;
            }
            else if (winPoints < points) {
                winner = group;
                winPoints = points;
            }
            else if (winPoints == points) {
                drawPoints = winPoints;
                winner = null;
            }
        }

        return winner;
    }

    private void endTwoTeamKingMatch() {
        super.onEndFirstDefault();

        if (artifact != null)
            artifact.getController().onDelete();

        PlayerGroup winner = getWinner();

        if (winner == null) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    scheduleAnnouncement(pl, "The match was a draw! Better luck next time.", 0);
                    super.rewardPlayer(pl, 20, false);
                }
            }

            super.specAnnounce("The match was a draw!");
        }
        else {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == winner.getGroupId()) {
                    for (Player pl : group.getMembers()) {
                        int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2 : 1;

                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE / 2);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.freezePlayer(pl, 5000);

                        super.scheduleAnnouncement(pl, "Your team has won the match with "
                            + teamPoints.get(group.getBgIndex()) + " points!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                        super.rewardPlayer(pl, 30, true);
                    }
                }
                else {
                    for (Player pl : group.getMembers()) {
                        super.playerLoseMatch(pl, -super.K_VALUE / 3);

                        super.scheduleAnnouncement(
                            pl,
                            "Your team has lost the match with "
                                + teamPoints.get(group.getBgIndex()) + " to "
                                + teamPoints.get(winner.getBgIndex()) + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);
                        super.rewardPlayer(pl, 20, false);
                    }
                }
            }
            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match with " + teamPoints.get(winner.getBgIndex()) + " points!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}