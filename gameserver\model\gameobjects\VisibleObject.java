/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects;

import gameserver.controllers.VisibleObjectController;
import gameserver.model.BoundRadius;
import gameserver.model.templates.VisibleObjectTemplate;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.world.KnownList;
import gameserver.world.MapRegion;
import gameserver.world.World;
import gameserver.world.WorldMapInstance;
import gameserver.world.WorldPosition;
import gameserver.world.WorldType;

/**
 * This class is representing visible objects. It's a base class for all in-game objects that can be spawned in the
 * world at some particular position (such as players, npcs).<br>
 * <br>
 * Objects of this class, as can be spawned in game, can be seen by other visible objects. To keep track of which
 * objects are already "known" by this visible object and which are not, VisibleObject is containing {@link KnownList}
 * which is responsible for holding this information.
 * 
 * <AUTHOR>
 */
public abstract class VisibleObject extends AionObject {
    protected VisibleObjectTemplate objectTemplate;

    /**
     * Constructor.
     * 
     * @param objId
     * @param objectTemplate
     */
    public VisibleObject(int objId, VisibleObjectController<? extends VisibleObject> controller,
        SpawnTemplate spawnTemplate, VisibleObjectTemplate objectTemplate, WorldPosition position) {
        super(objId);
        this.controller = controller;
        this.position = position;
        this.spawn = spawnTemplate;
        this.objectTemplate = objectTemplate;
    }

    /**
     * Position of object in the world.
     */
    private WorldPosition position;

    /**
     * KnownList of this VisibleObject.
     */
    private KnownList knownlist;

    /**
     * Controller of this VisibleObject
     */
    private final VisibleObjectController<? extends VisibleObject> controller;

    /**
     * Visible object's target
     */
    private VisibleObject target;

    /**
     * Spawn template of this visibleObject. .
     */
    private SpawnTemplate spawn;

    private boolean custom = false;

    /**
     * Returns current WorldRegion AionObject is in.
     * 
     * @return mapRegion
     */
    public MapRegion getActiveRegion() {
        return position.getMapRegion();
    }

    public int getInstanceId() {
        return position.getInstanceId();
    }

    /**
     * Return World map id.
     * 
     * @return world map id
     */
    public int getWorldId() {
        return position.getMapId();
    }

    /**
     * Return WorldType of current location
     * 
     * @return WorldType of current location
     */
    public WorldType getWorldType() {
        return World.getInstance().getWorldMap(getWorldId()).getWorldType();
    }

    /**
     * Return World position x
     * 
     * @return x
     */
    public float getX() {
        return position.getX();
    }

    /**
     * Return World position y
     * 
     * @return y
     */
    public float getY() {
        return position.getY();
    }

    /**
     * Return World position z
     * 
     * @return z
     */
    public float getZ() {
        return position.getZ();
    }

    /**
     * Heading of the object. Values from <0,120)
     * 
     * @return heading
     */
    public byte getHeading() {
        return position.getHeading();
    }

    /**
     * Return object position
     * 
     * @return position.
     */
    public WorldPosition getPosition() {
        return position;
    }
    
    /**
     * Return world map of object
     * 
     * @return worldMapInstance
     */
    public WorldMapInstance getWorldMapInstance() {
        return position.getMapRegion().getParent();
    }

    /**
     * Check if object is spawned.
     * 
     * @return true if object is spawned.
     */
    public boolean isSpawned() {
        return position.isSpawned();
    }

    /**
     * @return
     */
    public boolean isInWorld() {
        return World.getInstance().findAionObject(getObjectId()) != null;
    }

    /**
     * Check if map is instance
     * 
     * @return true if object in one of the instance maps
     */
    public boolean isInInstance() {
        return position.isInstanceMap();
    }

    public void clearKnownlist() {
        if (getKnownList() == null)
            return;
        
        getKnownList().clear();
    }

    public void updateKnownlist() {
        if (getKnownList() == null)
            return;
        
        getKnownList().doUpdate();
    }
    
    public void fullUpdateKnownlist() {
        if (getKnownList() == null)
            return;
        
        getKnownList().doUpdate(true);
    }

    /**
     * Set KnownList to this VisibleObject
     * 
     * @param knownlist
     */
    public void setKnownlist(KnownList knownlist) {
        this.knownlist = knownlist;
    }

    /**
     * Returns KnownList of this VisibleObject.
     * 
     * @return knownList.
     */
    public KnownList getKnownList() {
        return knownlist;
    }

    /**
     * Return VisibleObjectController of this VisibleObject
     * 
     * @return VisibleObjectController.
     */
    public VisibleObjectController<? extends VisibleObject> getController() {
        return controller;
    }

    /**
     * @return VisibleObject
     */
    public VisibleObject getTarget() {
        return target;
    }

    /**
     * @param creature
     */
    public void setTarget(VisibleObject creature) {
        target = creature;
    }

    /**
     * @param objectId
     * @return target is object with id equal to objectId
     */
    public boolean isTargeting(int objectId) {
        return target != null && target.getObjectId() == objectId;
    }

    /**
     * Return spawn template of this VisibleObject
     * 
     * @return SpawnTemplate
     */
    public SpawnTemplate getSpawn() {
        return spawn;
    }

    /**
     * @param spawn
     *            the spawn to set
     */
    public void setSpawn(SpawnTemplate spawn) {
        this.spawn = spawn;
    }

    /**
     * @return the objectTemplate
     */
    public VisibleObjectTemplate getObjectTemplate() {
        return objectTemplate;
    }

    /**
     * @param objectTemplate
     *            the objectTemplate to set
     */
    public void setObjectTemplate(VisibleObjectTemplate objectTemplate) {
        this.objectTemplate = objectTemplate;
    }
    
    public BoundRadius getBoundRadius() {
        return objectTemplate != null ? objectTemplate.getBoundRadius() : BoundRadius.DEFAULT;
    }

    /**
     * @param custom
     */
    public void setCustom(boolean custom) {
        this.custom = custom;
    }

    public boolean isCustom() {
        return custom;
    }
}