/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.ChatType;
import gameserver.model.alliance.PlayerAllianceMember;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.RequestResponseHandler;
import gameserver.model.pvpevents.AnohaChallenge;
import gameserver.model.pvpevents.Battleground;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_QUESTION_WINDOW;
import gameserver.services.ArenaService;
import gameserver.services.EventService;
import gameserver.services.LadderService;
import gameserver.utils.PacketSendUtility;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 
 */
public class AnohaChallengeShugoController extends BossController {
    private long nextShout = 0;

    public AnohaChallengeShugoController() {
        super(832921, true);
    }

    protected void think() {
        //
    }

    @Override
    public void onCreation() {
        getOwner().setCustomTag("Anoha's Challenge");
    }

    @Override
    public void onDialogRequest(Player player) {
        if (!EventService.getInstance().areInstancesEnabled()) {
            message(player, "Anoha's Challenge is currently closed.");
            return;
        }

        if (!player.isInAlliance() || player.getPlayerAlliance().size() > 12) {
            message(player,
                "You can only enter the instance with an alliance with a maximum of 12 members.");
            return;
        }
        else if (player.getPlayerAlliance().getCaptainObjectId() != player.getObjectId()) {
            message(player, "Only the alliance captain can request entry to the instance.");
            return;
        }

        List<Player> participants = new ArrayList<Player>();

        if (player.isInAlliance()) {
            for (PlayerAllianceMember pla : player.getPlayerAlliance().getMembers()) {
                Player pl = pla.getPlayer();
                if (pl == null) {
                    message(player, pla.getName()
                        + " is disconnected. You cannot enter with disconnected members.");
                    return;
                }
                else if (pl.getBattleground() != null || ArenaService.getInstance().isInArena(pl)) {
                    message(pl, pl.getName()
                        + " is in a BG or FFA. You cannot enter with members in BG or FFA.");
                    return;
                }
                else if (pl.isInCombatLong()) {
                    message(pl, pl.getName()
                        + " is in combat. You cannot enter with members in combat.");
                    return;
                }

                participants.add(pl);
            }

            if (participants.size() != player.getPlayerAlliance().size())
                return;
        }

        message(player, "Click Yes to enter the instance.");
        request(player, participants);
    }

    private void request(Player player, final List<Player> participants) {
        RequestResponseHandler responseHandler = new RequestResponseHandler(getOwner()) {
            @Override
            public void acceptRequest(Creature requester, Player responder) {
                Battleground bg = new AnohaChallenge();
                bg.setIsPvE(true);
                bg.createMatch(participants);

                if (bg.hasPlayers())
                    LadderService.getInstance().registerBg(bg);
            }

            @Override
            public void denyRequest(Creature requester, Player responder) {
            }
        };

        boolean requested = player.getResponseRequester().putRequest(SM_QUESTION_WINDOW.STR_CUSTOM,
            responseHandler);
        if (requested) {
            PacketSendUtility.sendPacket(player, new SM_QUESTION_WINDOW(
                SM_QUESTION_WINDOW.STR_CUSTOM, 0, "Do you wish to enter Anoha's Challenge?", ""));
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendPacket(player, new SM_MESSAGE(getOwner().getObjectId(),
            "Assistant Recordkeeper", msg, ChatType.ALLIANCE));
    }
}