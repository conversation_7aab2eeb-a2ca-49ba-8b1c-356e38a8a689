package gameserver.model.templates.bossevent;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "helper", propOrder = { "npcId" })
public class HelperTemplate {

    @XmlElement(name = "npc_id", required = true)
    @XmlSchemaType(name = "nonNegativeInteger")
    protected Integer npcId = 0;

    @XmlAttribute(name = "difficulty")
    protected int difficulty = 0;

    public int getNpcId() {
        return npcId.intValue();
    }

    public int getDifficulty() {
        return difficulty;
    }
}
