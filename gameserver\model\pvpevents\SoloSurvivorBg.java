/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.dao.MightDAO;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.GloryService;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.utils.ThreadPoolManager;

import java.util.List;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class SoloSurvivorBg extends Battleground {
    private int maxRounds = 3;
    private int roundsDone = 0;
    private boolean endCalled = false;

    public SoloSurvivorBg() {
        super.name = "Solo Survivor";
        super.description = "You are alone and must kill the enemies to win. There are "
            + maxRounds + " rounds and whoever ends up with the most kills in total wins.";
        super.minSize = 2;
        super.maxSize = 4;
        super.teamCount = 1;
        super.matchLength = 105;
        super.minMaxOnly = true;

        BattlegroundMap map1 = new BattlegroundMap(310080000);
        map1.addSpawn(new SpawnPosition(276.0f, 171.0f, 162.1f));
        map1.addSpawn(new SpawnPosition(276.0f, 292.0f, 163.0f));
        map1.addSpawn(new SpawnPosition(319.0f, 240.0f, 159.0f));
        map1.addSpawn(new SpawnPosition(233.0f, 240.0f, 159.0f));
        map1.addStaticDoor(1);
        map1.addStaticDoor(2);
        map1.addStaticDoor(10);
        map1.setKillZ(150f);

        BattlegroundMap map2 = new BattlegroundMap(300360000);
        map2.addSpawn(new SpawnPosition(346.0f, 1185.0f, 244.5f));
        map2.addSpawn(new SpawnPosition(251.0f, 1297.6f, 248.2f));
        map2.addSpawn(new SpawnPosition(278.0f, 1265.6f, 263.3f));
        map2.addSpawn(new SpawnPosition(316.0f, 1221.0f, 263.5f));
        map2.addStaticDoor(121);
        map2.addStaticDoor(135);
        map2.addStaticDoor(137);
        map2.addStaticDoor(138);
        map2.setKillZ(240f);

        BattlegroundMap map3 = new BattlegroundMap(300360000);
        map3.addSpawn(new SpawnPosition(1868.6f, 1068.6f, 337.5f));
        map3.addSpawn(new SpawnPosition(1869.2f, 1041.9f, 337.9f));
        map3.addSpawn(new SpawnPosition(1842.1f, 1069.2f, 338.1f));
        map3.addSpawn(new SpawnPosition(1841.2f, 1041.2f, 338.3f));
        map3.addStaticDoor(140);
        map3.addStaticDoor(141);
        map3.addStaticDoor(142);
        map3.addStaticDoor(139);
        map3.setKillZ(330f);

        BattlegroundMap map4 = new BattlegroundMap(300250000);
        map4.addSpawn(new SpawnPosition(1316.4f, 1196.9f, 52.0f));
        map4.addSpawn(new SpawnPosition(1316.6f, 1144.9f, 52.0f));
        map4.addSpawn(new SpawnPosition(1342.5f, 1171.0f, 52.0f));
        map4.addSpawn(new SpawnPosition(1290.4f, 1170.7f, 52.0f));
        map4.setKillZ(50f);

        BattlegroundMap map5 = new BattlegroundMap(320090000);
        map5.addSpawn(new SpawnPosition(319.0f, 240.0f, 159.0f));
        map5.addSpawn(new SpawnPosition(233.0f, 240.0f, 159.0f));
        map5.addSpawn(new SpawnPosition(276.0f, 171.0f, 162.1f));
        map5.addSpawn(new SpawnPosition(276.0f, 292.0f, 163.0f));
        map5.addStaticDoor(1);
        map5.addStaticDoor(2);
        map5.addStaticDoor(10);
        map5.setKillZ(150f);

        BattlegroundMap map6 = new BattlegroundMap(300330000);
        map6.addSpawn(new SpawnPosition(250.28f, 268.0f, 124.95f));
        map6.addSpawn(new SpawnPosition(250.54f, 221.92f, 124.95f));
        map6.addSpawn(new SpawnPosition(273.25f, 244.98f, 124.95f));
        map6.addSpawn(new SpawnPosition(224.74f, 245.61f, 124.95f));
        map6.setKillZ(120f);

        BattlegroundMap map7 = new BattlegroundMap(300450000);
        map7.addSpawn(new SpawnPosition(1539f, 377f, 205f));
        map7.addSpawn(new SpawnPosition(1587f, 354f, 199f));
        map7.addSpawn(new SpawnPosition(1561f, 389f, 203f));
        map7.addSpawn(new SpawnPosition(1553f, 341f, 201f));
        map7.addStaticDoor(149);
        map7.addStaticDoor(139);
        map7.addStaticDoor(143);
        map7.addStaticDoor(146);
        map7.addStaticDoor(140);
        map7.addStaticDoor(142);
        map7.addStaticDoor(144);
        map7.addStaticDoor(151);
        map7.addStaticDoor(141);
        map7.addStaticDoor(150);
        map7.addStaticDoor(148);
        map7.addStaticDoor(147);
        map7.setKillZ(190f);

        BattlegroundMap map8 = new BattlegroundMap(300490000);
        map8.addSpawn(new SpawnPosition(529f, 542f, 241f));
        map8.addSpawn(new SpawnPosition(477f, 491f, 241f));
        map8.addSpawn(new SpawnPosition(477f, 542f, 241f));
        map8.addSpawn(new SpawnPosition(529f, 490f, 241f));
        map8.setKillZ(230f);

        BattlegroundMap map9 = new BattlegroundMap(300800000); // 4.0
        map9.addSpawn(new SpawnPosition(110.0f, 125.0f, 124.0f));
        map9.addSpawn(new SpawnPosition(149.0f, 146.0f, 125.0f));
        map9.addSpawn(new SpawnPosition(104.0f, 138.0f, 113.0f));
        map9.addSpawn(new SpawnPosition(155.0f, 137.0f, 113.0f));
        map9.setKillZ(110f);

        BattlegroundMap map10 = new BattlegroundMap(301110000); // 4.0
        map10.addSpawn(new SpawnPosition(256.0f, 228.0f, 242.0f));
        map10.addSpawn(new SpawnPosition(255.0f, 288.0f, 242.0f));
        map10.addSpawn(new SpawnPosition(230.0f, 259.0f, 242.0f));
        map10.addSpawn(new SpawnPosition(282.0f, 266.0f, 242.0f));
        map10.setKillZ(240f);

        BattlegroundMap map11 = new BattlegroundMap(300590000); // 4.0 -- needs static spawns
        map11.addSpawn(new SpawnPosition(345.0f, 524.0f, 605.0f));
        map11.addSpawn(new SpawnPosition(348.0f, 456.0f, 605.0f));
        map11.addSpawn(new SpawnPosition(371.0f, 491.0f, 605.0f));
        map11.addSpawn(new SpawnPosition(323.0f, 488.0f, 609.0f));
        map11.setKillZ(603f);

        BattlegroundMap map12 = new BattlegroundMap(301130000); // 4.0
        map12.addSpawn(new SpawnPosition(605.0f, 213.0f, 192.0f));
        map12.addSpawn(new SpawnPosition(674.0f, 211.0f, 192.0f));
        map12.addSpawn(new SpawnPosition(638.0f, 188.0f, 195.0f));
        map12.addSpawn(new SpawnPosition(640.0f, 234.0f, 190.0f));
        map12.setKillZ(188f);

        BattlegroundMap map13 = new BattlegroundMap(301140000); // 4.0 - needs static spawns
        map13.addSpawn(new SpawnPosition(342.0f, 1183.0f, 55.0f));
        map13.addSpawn(new SpawnPosition(428.0f, 1184.0f, 50.0f));
        map13.addSpawn(new SpawnPosition(388.0f, 1200.0f, 55.0f));
        map13.addSpawn(new SpawnPosition(388.0f, 1139.0f, 62.0f));
        map13.setKillZ(47f);

        BattlegroundMap map14 = new BattlegroundMap(301140000); // 4.0
        map14.addSpawn(new SpawnPosition(1036.0f, 660.0f, 282.0f));
        map14.addSpawn(new SpawnPosition(1075.0f, 704.0f, 282.0f));
        map14.addSpawn(new SpawnPosition(1075.0f, 660.0f, 282.0f));
        map14.addSpawn(new SpawnPosition(1037.0f, 703.0f, 282.0f));
        map14.setKillZ(278f);

        BattlegroundMap map15 = new BattlegroundMap(301140000); // 4.0
        map15.addSpawn(new SpawnPosition(886.0f, 873.0f, 280.0f));
        map15.addSpawn(new SpawnPosition(927.0f, 848.0f, 280.0f));
        map15.addSpawn(new SpawnPosition(921.0f, 882.0f, 279.0f));
        map15.addSpawn(new SpawnPosition(892.0f, 839.0f, 279.0f));
        map15.setKillZ(276f);

        BattlegroundMap map16 = new BattlegroundMap(210040000);
        map16.addSpawn(new SpawnPosition(1959f, 2599f, 149f));
        map16.addSpawn(new SpawnPosition(1900f, 2629f, 153f));
        map16.addSpawn(new SpawnPosition(1911f, 2587f, 153f));
        map16.addSpawn(new SpawnPosition(1939f, 2644f, 149f));
        map16.setKillZ(134);

        BattlegroundMap map17 = new BattlegroundMap(400040000);
        map17.addSpawn(new SpawnPosition(991, 1057, 1530));
        map17.addSpawn(new SpawnPosition(1056, 1057, 1530));
        map17.addSpawn(new SpawnPosition(1057, 991, 1530));
        map17.addSpawn(new SpawnPosition(991, 991, 1530));
        map17.setKillZ(1525);
        
        BattlegroundMap map18 = new BattlegroundMap(300280000);
        map18.addSpawn(new SpawnPosition(218f, 406f, 261f));
        map18.addSpawn(new SpawnPosition(159f, 419f, 261f));
        map18.addSpawn(new SpawnPosition(195f, 443f, 261f));
        map18.addSpawn(new SpawnPosition(182f, 381f, 261f));
        map18.setKillZ(258f);

        super.maps.add(map1);
        // super.maps.add(map2);
        // super.maps.add(map3);
        super.maps.add(map4);
        super.maps.add(map5);
        // super.maps.add(map6);
        // super.maps.add(map7);
        //super.maps.add(map9);
        super.maps.add(map10);
        // super.maps.add(map11);
        super.maps.add(map12);
        // super.maps.add(map13);
        super.maps.add(map14);
        // super.maps.add(map15);
        super.maps.add(map16);
        super.maps.add(map17);
        super.maps.add(map18);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueSolo(players);

        if (super.getPlayers().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        synchronized (super.getPlayers()) {
            for (Player pl : super.getPlayers()) {
                super.preparePlayer(pl, 25000);

                SpawnPosition pos = getSpawnPositions().get(pl.getBgIndex());
                if (pos != null)
                    performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                else
                    log.error("pos == null!");
            }
        }

        super.specAnnounce("The match has begun!!!", 25000);

        startBattleground(25000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingPlayers() <= 1) {
                    roundsDone = maxRounds;
                    endSoloSurvivorMatch(true);
                }
            }
        });

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                openStaticDoors();
            }
        }, 25 * 1000);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endSoloSurvivorMatch(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public boolean createTournament(List<List<Player>> players) {
        if (!super.createPlayers(players))
            return false;

        this.maxRounds = 5;
        this.matchLength = 145;

        this.description = "You are alone and must kill the enemies to win. There are " + maxRounds
            + " rounds and whoever ends up with the most kills in total wins.";

        startMatch();

        return true;
    }

    @Override
    public boolean isStealthRestricted() {
        return true;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        if (is1v1() && getPlayers().size() == 2) { // always make sure someone gets the kill
            lastAttacker = getPlayers().get(player.getBgIndex() == 0 ? 1 : 0);
        }

        super.onDieDefault(player, lastAttacker);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;

            for (Player pl : super.getPlayers())
                scheduleAnnouncement(pl, LadderService.getInstance().getName(pl, killer)
                    + " has slain " + LadderService.getInstance().getName(pl, player), 0);
            super.specAnnounce(killer.getName() + " has slain " + player.getName());

            PvpService.getInstance().addMight(killer, is1v1() ? 2 : 3);
        }

        int deadCounter = 0;
        for (Player pl : super.getPlayers())
            if (pl.getLifeStats().isAlreadyDead())
                deadCounter++;

        if (deadCounter >= (super.getPlayers().size() - 1))
            endSoloSurvivorMatch(false);
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (is1v1() && !player.getController().isInShutdownProgress())
            DAOManager.getDAO(MightDAO.class).addMight(player, -25);

        if (isStarted() && getRemainingPlayers() <= 1) {
            roundsDone = maxRounds;
            endSoloSurvivorMatch(false);
        }
    }

    private void startNewRound() {
        super.setStartStamp(System.currentTimeMillis());
        super.killSummonedCreatures();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                int delay = 0;

                synchronized (getPlayers()) {
                    for (Player pl : getPlayers()) {
                        preparePlayer(pl, 7000, false);

                        SpawnPosition pos = getSpawnPositions().get(pl.getBgIndex());
                        if (pos != null)
                            TeleportService.teleportTo(pl, getMapId(), pos.getX(), pos.getY(),
                                pos.getZ(), delay += 500);
                    }
                }
            }
        }, 3000);

        for (Player pl : super.getSpectators())
            super.createTimer(pl, getMatchLength());

        super.specAnnounce("The round has begun!!!", 10000);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endSoloSurvivorMatch(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endCalled = false;
                if (getPlayers().size() <= 1) {
                    roundsDone = maxRounds;
                    endSoloSurvivorMatch(false);
                }
            }
        }, 12000);
    }

    private Player getRoundWinner() {
        Player winner = null;

        for (Player pl : super.getPlayers()) {
            if (!pl.getLifeStats().isAlreadyDead()) {
                winner = pl;
                break;
            }
        }

        return winner;
    }

    private Player getWinner() {
        Player winner = null;
        int drawPoints = 0;

        if (super.getPlayers().size() == 1)
            return super.getPlayers().get(0);

        for (Player pl : super.getPlayers()) {
            if (winner == null && pl.getTotalKills() > drawPoints) {
                winner = pl;
            }
            else if (winner == null) {
                continue;
            }
            else if (winner.getTotalKills() < pl.getTotalKills()) {
                winner = pl;
            }
            else if (winner.getTotalKills() == pl.getTotalKills()) {
                drawPoints = winner.getTotalKills();
                winner = null;
            }
        }

        return winner;
    }

    private void endSoloSurvivorMatch(boolean isDraw) {
        if (endCalled)
            return;

        endCalled = true;

        for (Player pl : super.getPlayers())
            super.freezeNoEnd(pl);

        super.onEndFirstDefault();

        Player roundWinner = getRoundWinner();
        if (roundsDone < maxRounds) {
            roundsDone++;
        }

        if (roundsDone < maxRounds) {
            if (!isDraw) {
                if (roundWinner != null) {
                    for (Player pl : super.getPlayers())
                        super.scheduleAnnouncement(pl, roundWinner.getName() + " has won round "
                            + roundsDone + "!", 0);
                    super
                        .specAnnounce(roundWinner.getName() + " has won round " + roundsDone + "!");
                }
            }
            else {
                for (Player pl : super.getPlayers())
                    super.scheduleAnnouncement(pl, "Round " + roundsDone + " was a draw!", 0);
                super.specAnnounce("Round " + roundsDone + " was a draw!");
            }

            startNewRound();
            return;
        }

        Player winner = getWinner();
        if (winner == null) {
            for (Player pl : super.getPlayers()) {
                scheduleAnnouncement(pl, "The match was a draw! Better luck next time.", 0);

                if (is1v1()) {
                    if (super.isEligibleForRewards(pl)) {
                        PvpService.getInstance().addMight(pl, 5);
                        GloryService.getInstance().rewardOneVsOne(pl, false);
                    }
                }
                else {
                    super.rewardPlayer(pl, 15, false);
                }
            }
            super.specAnnounce("The match was a draw!");
        }
        else {
            propagateWin(winner);
            
            logWinner(winner.getBgIndex() + 1);

            for (Player pl : super.getPlayers()) {
                if (pl.getObjectId() == winner.getObjectId()) {
                    super.playerWinMatch(pl, super.K_VALUE * 2 / 3);
                    super.scheduleAnnouncement(pl,
                        "You have won the match with " + winner.getTotalKills() + " kills!", 0);
                    super.scheduleAnnouncement(pl, "You have been rewarded with might "
                        + (is1v1() ? "for your effort!" : "and rating for your great effort!"),
                        3000);

                    if (is1v1()) {
                        PvpService.getInstance().addMight(pl, 10);
                        GloryService.getInstance().rewardOneVsOne(pl, true);
                    }
                    else {
                        super.rewardPlayer(pl, 20, true);
                    }
                }
                else {
                    super.playerLoseMatch(pl, -super.K_VALUE / 4);
                    super.scheduleAnnouncement(pl, winner.getName() + " has won the match with "
                        + winner.getTotalKills() + " kills!", 0);
                    super.scheduleAnnouncement(pl, "You have received some might"
                        + (is1v1() ? "." : " but lost rating."), 3000);

                    if (is1v1()) {
                        if (super.isEligibleForRewards(pl)) {
                            PvpService.getInstance().addMight(pl, 5);
                            GloryService.getInstance().rewardOneVsOne(pl, false);
                        }
                    }
                    else {
                        super.rewardPlayer(pl, 15, false);
                    }
                }
            }
            super.specAnnounce(winner.getName() + " has won the match with "
                + winner.getTotalKills() + " kills!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}