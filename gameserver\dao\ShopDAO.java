/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.dao;

import gameserver.model.ShopCategory;
import gameserver.model.ShopItem;
import gameserver.model.gameobjects.player.Player;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import com.aionemu.commons.database.dao.DAO;

/**
 * 
 * <AUTHOR>
 */
public abstract class ShopDAO implements DAO {

    @Override
    public final String getClassName() {
        return ShopDAO.class.getName();
    }
    
    public static class DonationEntry {
        private Timestamp timestamp;
        private int amount;
        
        public DonationEntry(Timestamp timestamp, int amount) {
            this.timestamp = timestamp;
            this.amount = amount;
        }
        
        public Timestamp getTimeStamp() {
            return timestamp;
        }
        
        public int getAmount() {
            return amount;
        }
    }
    
    public static class ShopPurchase {
        private int id;
        private int charId;
        private int itemId;
        private long quantity;
        private boolean gift;
        private String gifter;
        
        public ShopPurchase(int id, int charId, int itemId, long quantity, boolean gift, String gifter) {
            this.id = id;
            this.charId = charId;
            this.itemId = itemId;
            this.quantity = quantity;
            this.gift = gift;
            this.gifter = gifter;
        }
        
        public int getId() {
            return id;
        }
        
        public int getCharId() {
            return charId;
        }
        
        public int getItemId() {
            return itemId;
        }
        
        public long getQuantity() {
            return quantity;
        }
        
        public boolean isGift() {
            return gift;
        }
        
        public String getGifter() {
            return gifter;
        }
    }
    
    public static class ShopRemoval {
        private int id;
        private int itemUniqueId;
        private int itemOwner;
        private int amount;
        
        public ShopRemoval(int id, int itemUniqueId, int itemOwner, int amount) {
            this.id = id;
            this.itemUniqueId = itemUniqueId;
            this.itemOwner = itemOwner;
            this.amount = amount;
        }

        public int getId() {
            return id;
        }

        public int getItemUniqueId() {
            return itemUniqueId;
        }

        public int getItemOwner() {
            return itemOwner;
        }

        public int getAmount() {
            return amount;
        }
    }

    public abstract List<ShopCategory> getCategories();

    public abstract Map<Integer, ShopItem> getItems(int categoryId);

    public abstract void logPurchase(Player player, int itemId, int count, int price);
    
    public abstract List<DonationEntry> getDonationEntries(Player player);
    public abstract float getDonationTotal(Player player);
    public abstract float getDonationTotalMonths(Player player, int months);

    public abstract boolean addItem(int categoryId, ShopItem shopItem);

    public abstract List<ShopCategory> getCategoryList();
    
    public abstract boolean addShopPurchase(int charId, int itemId, long quantity, boolean gift, String gifter);
    public abstract List<ShopPurchase> getPendingPurchases();
    public abstract boolean flagPurchaseAsAdded(int id);
    
    public abstract List<ShopRemoval> getPendingRemovals();
    public abstract boolean flagRemovalAsDone(int id, int amount);
    public abstract boolean deleteRemoval(int id);
}