/*
 * Copyright (c) 2009-2012 jMonkeyEngine
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are
 * met:
 *
 * * Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * * Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in the
 *   documentation and/or other materials provided with the distribution.
 *
 * * Neither the name of 'jMonkeyEngine' nor the names of its contributors
 *   may be used to endorse or promote products derived from this software
 *   without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
 * TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 * PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package gameserver.geoEngine2.scene;

import gameserver.geoEngine2.bounding.BoundingVolume;
import gameserver.geoEngine2.collision.Collidable;
import gameserver.geoEngine2.collision.CollisionResult;
import gameserver.geoEngine2.collision.CollisionResults;
import gameserver.geoEngine2.math.Matrix3f;
import gameserver.geoEngine2.math.Matrix4f;
import gameserver.geoEngine2.math.Ray;
import gameserver.geoEngine2.math.Vector3f;
import gameserver.model.templates.gather.Material;

import org.apache.log4j.Logger;

/**
 * <code>Geometry</code> defines a leaf node of the scene graph. The leaf node contains the geometric data for rendering
 * objects. It manages all rendering information such as a {@link Material} object to define how the surface should be
 * shaded and the {@link Mesh} data to contain the actual geometry.
 * 
 * <AUTHOR> Vainer
 */
public class Geometry extends Spatial {

    // Version #1: removed shared meshes.
    // models loaded with shared mesh will be automatically fixed.
    protected Mesh mesh;
    protected transient Matrix4f cachedWorldMat = new Matrix4f();
    protected boolean fallbackToBound = false;

    /**
     * Serialization only. Do not use.
     */
    public Geometry() {
    }

    /**
     * Create a geometry node without any mesh data. Both the mesh and the material are null, the geometry cannot be
     * rendered until those are set.
     * 
     * @param name
     *            The name of this geometry
     */
    public Geometry(String name) {
        super(name);
    }

    /**
     * Create a geometry node with mesh data. The material of the geometry is null, it cannot be rendered until it is
     * set.
     * 
     * @param name
     *            The name of this geometry
     * @param mesh
     *            The mesh data for this geometry
     */
    public Geometry(String name, Mesh mesh) {
        this(name);
        if (mesh == null) {
            throw new NullPointerException();
        }

        this.mesh = mesh;
    }

    /**
     * Returns this geometry's mesh vertex count.
     * 
     * @return this geometry's mesh vertex count.
     * 
     * @see Mesh#getVertexCount()
     */
    public int getVertexCount() {
        return mesh.getVertexCount();
    }

    /**
     * Returns this geometry's mesh triangle count.
     * 
     * @return this geometry's mesh triangle count.
     * 
     * @see Mesh#getTriangleCount()
     */
    public int getTriangleCount() {
        return mesh.getTriangleCount();
    }

    /**
     * Sets the mesh to use for this geometry when rendering.
     * 
     * @param mesh
     *            the mesh to use for this geometry
     * 
     * @throws IllegalArgumentException
     *             If mesh is null
     */
    public void setMesh(Mesh mesh) {
        if (mesh == null) {
            throw new IllegalArgumentException();
        }

        this.mesh = mesh;
    }

    /**
     * Returns the mseh to use for this geometry
     * 
     * @return the mseh to use for this geometry
     * 
     * @see #setMesh(gameserver.geoEngine2.scene.Mesh)
     */
    public Mesh getMesh() {
        return mesh;
    }

    /**
     * @return The bounding volume of the mesh, in model space.
     */
    public BoundingVolume getModelBound() {
        return mesh.getBound();
    }

    /**
     * Updates the bounding volume of the mesh. Should be called when the mesh has been modified.
     */
    public void updateModelBound() {
        mesh.updateBound();
        worldBound = getModelBound().transform(cachedWorldMat, worldBound);
    }

    /**
     * A {@link Matrix4f matrix} that transforms the {@link Geometry#getMesh() mesh} from model space to world space.
     * This matrix is computed based on the {@link Geometry#getWorldTransform() world transform} of this geometry. In
     * order to receive updated values, you must call {@link Geometry#computeWorldMatrix() } before using this method.
     * 
     * @return Matrix to transform from local space to world space
     */
    public Matrix4f getWorldMatrix() {
        return cachedWorldMat;
    }

    /**
     * Sets the model bound to use for this geometry. This alters the bound used on the mesh as well via
     * {@link Mesh#setBound(com.jme3.bounding.BoundingVolume) } and forces the world bounding volume to be recomputed.
     * 
     * @param modelBound
     *            The model bound to set
     */
    @Override
    public void setModelBound(BoundingVolume modelBound) {
        mesh.setBound(modelBound);
    }

    public int collideWith(Collidable other, CollisionResults results) {
        if (other instanceof Ray) {
            if (worldBound == null || !worldBound.intersects(((Ray) other)))
                return 0;
        }

        if (mesh != null) {
            // NOTE: BIHTree in mesh already checks collision with the
            // mesh's bound
            int prevSize = results.size();
            int added = mesh.collideWith(other, cachedWorldMat, worldBound, results);

            if (added == 0 && fallbackToBound) {
                CollisionResults bound = new CollisionResults(results.isOnlyFirst());
                worldBound.collideWith(other, bound);

                for (CollisionResult result : bound)
                    if (!(other instanceof Ray) || ((Ray) other).getLimit() > result.getDistance())
                        results.addCollision(result);
            }

            int newSize = results.size();
            for (int i = prevSize; i < newSize; i++) {
                results.getCollisionDirect(i).setGeometry(this);
            }

            if (results.isDebug())
                Logger.getLogger(getClass()).info("collideWith: " + results.toString());

            return added;
        }

        return 0;
    }

    /**
     * This version of clone is a shallow clone, in other words, the same mesh is referenced as the original geometry.
     * Exception: if the mesh is marked as being a software animated mesh, (bind pose is set) then the positions and
     * normals are deep copied.
     * 
     * @throws CloneNotSupportedException
     */
    @Override
    public Geometry clone() throws CloneNotSupportedException {
        return (Geometry) super.clone();
    }

    /**
     * Creates a deep clone of the geometry, this creates an identical copy of the mesh with the vertexbuffer data
     * duplicated.
     * 
     * @throws CloneNotSupportedException
     */
    @Override
    public Spatial deepClone() throws CloneNotSupportedException {
        Geometry geomClone = clone();
        geomClone.mesh = mesh.deepClone();
        geomClone.fallbackToBound = fallbackToBound;
        return geomClone;
    }

    @Override
    public void setTransform(Matrix3f rotation, Vector3f loc, float scale) {
        cachedWorldMat.loadIdentity();
        cachedWorldMat.setRotationMatrix(rotation);
        cachedWorldMat.scale(scale);
        cachedWorldMat.setTranslation(loc);
    }

    public void setFallbackToBound(boolean fallbackToBound) {
        this.fallbackToBound = fallbackToBound;
    }
    
    public boolean isFallbackToBound() {
        return fallbackToBound;
    }
}
