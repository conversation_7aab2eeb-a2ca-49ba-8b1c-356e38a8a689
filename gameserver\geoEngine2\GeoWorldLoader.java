/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.geoEngine2;

import gameserver.configs.network.NetworkConfig;
import gameserver.geoEngine2.math.Matrix3f;
import gameserver.geoEngine2.math.Vector3f;
import gameserver.geoEngine2.scene.Geometry;
import gameserver.geoEngine2.scene.Mesh;
import gameserver.geoEngine2.scene.Node;
import gameserver.geoEngine2.scene.Spatial;
import gameserver.geoEngine2.scene.VertexBuffer;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.Buffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;
import java.nio.MappedByteBuffer;
import java.nio.ShortBuffer;
import java.nio.channels.FileChannel;
import java.util.HashMap;
import java.util.Map;

import sun.misc.Cleaner;
import sun.nio.ch.DirectBuffer;

/**
 * <AUTHOR>
 * 
 */
public class GeoWorldLoader {
    public static String GEO_DIR = "data/geo/";

    private static boolean DEBUG = false;

    @SuppressWarnings("resource")
    public static boolean loadWorld(int worldId, Map<String, Spatial> models, GeoMap map) {
        File geoFile = new File(GEO_DIR + worldId + ".geo");
        FileChannel roChannel = null;
        MappedByteBuffer geo = null;

        try {
            roChannel = new RandomAccessFile(geoFile, "r").getChannel();
            int size = (int) roChannel.size();
            geo = roChannel.map(FileChannel.MapMode.READ_ONLY, 0L, size).load();
        }
        catch (FileNotFoundException e) {
            e.printStackTrace();
            return false;
        }
        catch (IOException e) {
            e.printStackTrace();
            return false;
        }

        geo.order(ByteOrder.LITTLE_ENDIAN);

        if (geo.get() == 0) {
            map.setTerrainData(new short[] { geo.getShort() }, new byte[] { 0 });
        }
        else {
            int size = geo.getInt();
            short[] terrainData = new short[size];
            byte[] terrainInfo = new byte[size];

            for (int i = 0; i < size; ++i)
                terrainData[i] = geo.getShort();

            for (int i = 0; i < size; ++i)
                terrainInfo[i] = geo.get();

            map.setTerrainData(terrainData, terrainInfo);
        }

        while (geo.hasRemaining()) {
            int nameLenght = geo.getShort();
            byte[] nameByte = new byte[nameLenght];

            geo.get(nameByte);
            String name = new String(nameByte).toLowerCase().intern();

            Vector3f loc = new Vector3f(geo.getFloat(), geo.getFloat(), geo.getFloat());
            float[] matrix = new float[9];

            for (int i = 0; i < 9; ++i)
                matrix[i] = geo.getFloat();

            float scale = geo.getFloat();

            Matrix3f matrix3f = new Matrix3f();
            matrix3f.set(matrix);

            Spatial node = (Spatial) models.get(name);
            if (node != null) {
                attachChild(map, node, matrix3f, loc, scale);
            }
            else {
                // log.warn("Model " + name + " not found for world " + worldId);
            }
        }

        destroyDirectByteBuffer(geo);
        map.updateModelBound();

        return true;
    }

    @SuppressWarnings("resource")
    public static Map<String, Spatial> loadMeshs() {
        if (NetworkConfig.GAMESERVER_ID == 100)
            DEBUG = true;

        Map<String, Spatial> geoms = new HashMap<String, Spatial>();
        File geoFile = new File(GEO_DIR + "meshs.geo");
        FileChannel roChannel = null;
        MappedByteBuffer geo = null;

        try {
            roChannel = new RandomAccessFile(geoFile, "r").getChannel();
            int size = (int) roChannel.size();
            geo = roChannel.map(FileChannel.MapMode.READ_ONLY, 0L, size).load();
        }
        catch (FileNotFoundException e) {
            e.printStackTrace();
            return null;
        }
        catch (IOException e) {
            e.printStackTrace();
            return null;
        }

        geo.order(ByteOrder.LITTLE_ENDIAN);

        while (geo.hasRemaining()) {
            short namelength = geo.getShort();
            byte[] nameByte = new byte[namelength];

            geo.get(nameByte);
            String name = new String(nameByte).toLowerCase().intern();

            int modelCount = geo.getShort();
            Node node = new Node(DEBUG ? name : null);

            for (int c = 0; c < modelCount; ++c) {
                Mesh m = new Mesh();

                int vectorCount = geo.getInt() * 3;
                FloatBuffer vertices = FloatBuffer.allocate(vectorCount);

                for (int x = 0; x < vectorCount; x++)
                    vertices.put(geo.getFloat());

                int tringle = geo.getInt();
                ShortBuffer indexes = ShortBuffer.allocate(tringle);

                for (int x = 0; x < tringle; x++)
                    indexes.put(geo.getShort());

                m.setBuffer(VertexBuffer.Type.Position, 3, vertices);
                m.setBuffer(VertexBuffer.Type.Index, 3, indexes);
                m.createCollisionData();

                Geometry geom = new Geometry(null, m);
                if (DEBUG) {
                    if (modelCount == 1) {
                        geom.setName(name);
                    }
                    else {
                        geom.setName(("child_" + c + "_" + name).intern());
                    }
                }

                if (name.equals("levels\\common\\idzone\\idtiamat\\bu_id_tiamat_line02a.cgf")
                    || name
                        .equals("levels\\common\\idzone\\idseal\\4stage\\bossroom_stagefloor_01a.cgf")
                    || name
                        .equals("levels\\common\\idzone\\idseal\\4stage\\bossroom_stagebottom_04a.cgf")
                    || name
                        .equals("levels\\common\\idzone\\idseal\\4stage\\bossroom_stagebottom_02a.cgf")
                    || name
                        .equals("levels\\common\\idzone\\idseal\\4stage\\bossroom_stageside_01a.cgf")
                    || name
                        .equals("levels\\common\\idzone\\idseal\\4stage\\bossroom_stageside_02a.cgf"))
                    geom.setFallbackToBound(true);

                node.attachChild(geom);

                geoms.put(name, geom);
            }

            if (!node.getChildren().isEmpty())
                geoms.put(name, node);
        }

        destroyDirectByteBuffer(geo);

        return geoms;
    }

    private static Spatial attachChild(GeoMap map, Spatial node, Matrix3f matrix,
        Vector3f location, float scale) {
        Spatial nodeClone = node;

        try {
            nodeClone = node.clone();
        }
        catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }

        nodeClone.setTransform(matrix, location, scale);
        nodeClone.updateModelBound();
        map.attachChild(nodeClone);

        return nodeClone;
    }

    private static void destroyDirectByteBuffer(Buffer toBeDestroyed) {
        Cleaner cleaner = ((DirectBuffer) toBeDestroyed).cleaner();
        if (cleaner != null)
            cleaner.clean();
    }
}
