/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.controllers.attack.AttackResult;
import gameserver.controllers.attack.AttackStatus;
import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.AttackCalcObserver;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.skillengine.action.DamageType;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.Skill;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

import org.apache.commons.lang.ArrayUtils;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
public class ObserveController {
    // TODO revisit here later
    protected Queue<ActionObserver> moveObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> attackObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> attackedObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> attackUseObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> skillcastObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> skilluseObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> stateChangeObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> deathObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> jumpObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> dotObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> hittedObservers = new ConcurrentLinkedQueue<ActionObserver>();
    protected Queue<ActionObserver> godstoneObservers = new ConcurrentLinkedQueue<ActionObserver>();

    protected Queue<ActionObserver> observers = new ConcurrentLinkedQueue<ActionObserver>();

    protected ActionObserver[] equipObservers = new ActionObserver[0];
    protected AttackCalcObserver[] attackCalcObservers = new AttackCalcObserver[0];

    public void clear() {
        moveObservers.clear();
        attackObservers.clear();
        attackedObservers.clear();
        attackUseObservers.clear();
        skillcastObservers.clear();
        skilluseObservers.clear();
        stateChangeObservers.clear();
        deathObservers.clear();
        jumpObservers.clear();
        dotObservers.clear();
        hittedObservers.clear();
        godstoneObservers.clear();

        Arrays.fill(equipObservers, null);
        Arrays.fill(attackCalcObservers, null);

        observers.clear();
    }

    /**
     * @param observer
     */
    public void attach(ActionObserver observer) {
        switch (observer.getObserverType()) {
            case ATTACK:
                attackObservers.add(observer);
                break;
            case ATTACKED:
                attackedObservers.add(observer);
                break;
            case ATTACKUSE:
                attackUseObservers.add(observer);
                break;
            case MOVE:
                moveObservers.add(observer);
                break;
            case SKILLCAST:
                skillcastObservers.add(observer);
                break;
            case SKILLUSE:
                skilluseObservers.add(observer);
                break;
            case STATECHANGE:
                stateChangeObservers.add(observer);
                break;
            case DEATH:
                deathObservers.add(observer);
                break;
            case JUMP:
                jumpObservers.add(observer);
                break;
            case DOT:
                dotObservers.add(observer);
                break;
            case HITTED:
                hittedObservers.add(observer);
                break;
            case GODSTONE:
                godstoneObservers.add(observer);
                break;
        }
    }

    /**
     * notify that creature moved
     */
    protected void notifyMoveObservers() {
        synchronized (moveObservers) {
            while (!moveObservers.isEmpty()) {
                ActionObserver observer = moveObservers.poll();
                observer.moved();
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.moved();
    }

    /**
     * notify that creature attacking
     */
    public void notifyAttackObservers(Creature creature) {
        synchronized (attackObservers) {
            while (!attackObservers.isEmpty()) {
                ActionObserver observer = attackObservers.poll();
                observer.attack(creature);
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.attack(creature);
    }

    /**
     * notify that creature attacked
     */
    public void notifyAttackedObservers(Creature creature) {
        synchronized (attackedObservers) {
            while (!attackedObservers.isEmpty()) {
                ActionObserver observer = attackedObservers.poll();
                observer.attacked(creature);
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.attacked(creature);
    }

    /**
     * notify that creature started attack
     */
    public void notifyAttackUseObservers(Creature creature) {
        synchronized (attackUseObservers) {
            while (!attackUseObservers.isEmpty()) {
                ActionObserver observer = attackUseObservers.poll();
                observer.attackUse(creature);
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.attackUse(creature);
    }
    
    /**
     * notify that creature used a skill
     */
    public void notifySkillcastObservers(Skill skill) {
        synchronized (skillcastObservers) {
            while (!skillcastObservers.isEmpty()) {
                ActionObserver observer = skillcastObservers.poll();
                observer.skillcast(skill);
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.skillcast(skill);
    }

    /**
     * notify that creature used a skill
     */
    public void notifySkilluseObservers(Skill skill) {
        synchronized (skilluseObservers) {
            while (!skilluseObservers.isEmpty()) {
                ActionObserver observer = skilluseObservers.poll();
                observer.skilluse(skill);
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.skilluse(skill);
    }

    /**
     * notify that creature changed state
     */
    public void notifyStateChangeObservers(CreatureState state, boolean isSet) {
        synchronized (stateChangeObservers) {
            while (!stateChangeObservers.isEmpty()) {
                ActionObserver observer = stateChangeObservers.poll();
                observer.stateChanged(state, isSet);
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.stateChanged(state, isSet);
    }

    /**
     * @param item
     * @param owner
     */
    public void notifyItemEquip(Item item, Player owner) {
        synchronized (equipObservers) {
            for (ActionObserver observer : equipObservers) {
                observer.equip(item, owner);
            }
        }
    }

    /**
     * @param item
     * @param owner
     */
    public void notifyItemUnEquip(Item item, Player owner) {
        synchronized (equipObservers) {
            for (ActionObserver observer : equipObservers) {
                observer.unequip(item, owner);
            }
        }
    }

    /**
     * @param observer
     */
    public void addObserver(ActionObserver observer) {
        synchronized (observers) {
            observers.add(observer);
        }
    }

    /**
     * @param observer
     */
    public void addEquipObserver(ActionObserver observer) {
        synchronized (equipObservers) {
            equipObservers = (ActionObserver[]) ArrayUtils.add(equipObservers, observer);
        }
    }

    /**
     * @param observer
     */
    public void addAttackCalcObserver(AttackCalcObserver observer) {
        synchronized (attackCalcObservers) {
            attackCalcObservers = (AttackCalcObserver[]) ArrayUtils.add(attackCalcObservers,
                observer);
        }
    }

    /**
     * @param observer
     */
    public void removeObserver(ActionObserver observer) {
        synchronized (observers) {
            if (observers.contains(observer))
                observers.remove(observer);
        }
        synchronized (moveObservers) {
            if (moveObservers.contains(observer))
                moveObservers.remove(observer);
        }
    }

    public void removeDeathObserver(ActionObserver observer) {
        synchronized (deathObservers) {
            if (deathObservers.contains(observer))
                deathObservers.remove(observer);
        }
    }

    /**
     * @param observer
     */
    public void removeEquipObserver(ActionObserver observer) {
        synchronized (equipObservers) {
            equipObservers = (ActionObserver[]) ArrayUtils.removeElement(equipObservers, observer);
        }
    }

    /**
     * @param observer
     */
    public void removeAttackCalcObserver(AttackCalcObserver observer) {
        synchronized (attackCalcObservers) {
            attackCalcObservers = (AttackCalcObserver[]) ArrayUtils.removeElement(
                attackCalcObservers, observer);
        }
    }

    /**
     * @param status
     * @return true or false
     */
    public boolean checkAttackStatus(AttackStatus status) {
        List<AttackCalcObserver> _attackCalcObservers = new ArrayList<AttackCalcObserver>();
        synchronized (attackCalcObservers) {
            for (AttackCalcObserver observer : attackCalcObservers)
                _attackCalcObservers.add(observer);
        }

        for (AttackCalcObserver observer : _attackCalcObservers) {
            if (observer.checkStatus(status))
                return true;
        }
        return false;
    }

    /**
     * @param status
     * @return
     */
    public boolean checkAttackerStatus(AttackStatus status) {
        return checkAttackerStatus(status, false);
    }
    
    /**
     * @param status
     * @return
     */
    public boolean checkAttackerStatus(AttackStatus status, boolean skill) {
        List<AttackCalcObserver> _attackCalcObservers = new ArrayList<AttackCalcObserver>();
        synchronized (attackCalcObservers) {
            for (AttackCalcObserver observer : attackCalcObservers)
                _attackCalcObservers.add(observer);
        }

        for (AttackCalcObserver observer : _attackCalcObservers) {
            if (observer.checkAttackerStatus(status, skill))
                return true;
        }
        return false;
    }

    /**
     * @param attackList
     */
    public void checkShieldStatus(List<AttackResult> attackList, Creature attacker, Effect effect) {
        List<AttackCalcObserver> _attackCalcObservers = new ArrayList<AttackCalcObserver>();
        synchronized (attackCalcObservers) {
            for (AttackCalcObserver observer : attackCalcObservers)
                _attackCalcObservers.add(observer);
        }

        for (AttackCalcObserver observer : _attackCalcObservers)
            observer.checkShield(attackList, attacker, effect);
    }
    
    public void checkReflectStatus(Effect attackEffect) {
        List<AttackCalcObserver> _attackCalcObservers = new ArrayList<AttackCalcObserver>();
        synchronized (attackCalcObservers) {
            for (AttackCalcObserver observer : attackCalcObservers)
                _attackCalcObservers.add(observer);
        }

        for (AttackCalcObserver observer : _attackCalcObservers)
            observer.checkReflect(attackEffect);
    }

    public float getBasePhysicalDamageMultiplier(boolean skill) {
        float multiplier = 1;

        synchronized (attackCalcObservers) {
            for (AttackCalcObserver observer : attackCalcObservers) {
                multiplier *= observer.getBasePhysicalDamageMultiplier(skill);
            }
        }

        return multiplier;
    }

    public float getBaseMagicalDamageMultiplier(boolean skill) {
        float multiplier = 1;

        synchronized (attackCalcObservers) {
            for (AttackCalcObserver observer : attackCalcObservers) {
                multiplier *= observer.getBaseMagicalDamageMultiplier(skill);
            }
        }

        return multiplier;
    }

    public void notifyDeath(Creature creature) {
        synchronized (deathObservers) {
            while (!deathObservers.isEmpty()) {
                ActionObserver observer = deathObservers.poll();
                observer.died(creature);
            }
        }
    }

    /**
     * notify that player jumped
     */
    public void notifyJumpObservers() {
        synchronized (jumpObservers) {
            while (!jumpObservers.isEmpty()) {
                ActionObserver observer = jumpObservers.poll();
                observer.jump();
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.jump();
    }

    /**
     * notify that player received damage over time
     */
    public void notifyDotObservers(Creature creature) {
        synchronized (dotObservers) {
            while (!dotObservers.isEmpty()) {
                ActionObserver observer = dotObservers.poll();
                observer.onDot(creature);
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.onDot(creature);
    }

    /**
     * notify that player received damage over time
     */
    public void notifyHittedObservers(Creature creature, DamageType damageType) {
        while (!hittedObservers.isEmpty()) {
            ActionObserver observer = hittedObservers.poll();
            observer.hitted(creature, damageType);
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.hitted(creature, damageType);
    }

    /**
     * notify that player using godstone
     */
    public void notifyGodstoneObservers(Creature creature) {
        synchronized (godstoneObservers) {
            while (!godstoneObservers.isEmpty()) {
                ActionObserver observer = godstoneObservers.poll();
                observer.onGodstone(creature);
            }
        }

        List<ActionObserver> _observers = new ArrayList<ActionObserver>();
        synchronized (observers) {
            _observers.addAll(observers);
        }
        for (ActionObserver observer : _observers)
            observer.onGodstone(creature);
    }
}
