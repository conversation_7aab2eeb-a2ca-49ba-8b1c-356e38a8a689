/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.legion;

/**
 * <AUTHOR>
 */
public enum LegionRank {
    /**
     * All Legion Ranks *
     */
    BRIGADE_GENERAL(0),
    DEPUTY(1),
    CENTURION(2),
    LEGIONARY(3),
    VOLUNTEER(4);

    /**
     * Static Rights Information *
     */
    // Add to 0x60
    private static final int LP_WAREHOUSE_WITHDRAW = 0x04;
    private static final int LP_INVITE_TO_LEGION = 0x08;
    private static final int LP_KICK_FROM_LEGION = 0x10;

    // Add to 0x00
    private static final int LP_EDIT_ANNOUNCEMENT = 0x02;
    private static final int LP_ARTIFACT = 0x04;
    private static final int LP_GATE_GUARDIAN_STONE = 0x08;
    private static final int LP_WAREHOUSE_DEPOSIT = 0x10;

    private byte rank;

    private LegionRank(int rank) {
        this.rank = (byte) rank;
    }

    /**
     * Returns client-side id for this
     * 
     * @return byte
     */
    public byte getRankId() {
        return this.rank;
    }

    /**
     * @return true if legion member has enough rights for Use Gate Guardian Stone
     */
    public boolean canUseGateGuardianStone(final int permission2) {
        return (permission2 & LP_GATE_GUARDIAN_STONE) == LP_GATE_GUARDIAN_STONE;
    }

    /**
     * @return true if legion member has enough rights for Use Artifact
     */
    public boolean canUseArtifact(final int permission2) {
        return (permission2 & LP_ARTIFACT) == LP_ARTIFACT;
    }

    /**
     * @return true if legion member has enough rights for Edit Announcement
     */
    public boolean canEditAnnouncement(final int permission2) {
        return (permission2 & LP_EDIT_ANNOUNCEMENT) == LP_EDIT_ANNOUNCEMENT;
    }

    /**
     * @return true if legion member has enough rights for Use Legion Warehouse
     */
    public boolean canUseLegionWarehouse(final int permission1, final int permission2) {
        return (permission1 & LP_WAREHOUSE_WITHDRAW) == LP_WAREHOUSE_WITHDRAW
            && (permission2 & LP_WAREHOUSE_DEPOSIT) == LP_WAREHOUSE_DEPOSIT;
    }

    /**
     * @return true if legion member has enough rights for Kick from Legion
     */
    public boolean canKickFromLegion(final int permission1) {
        return (permission1 & LP_KICK_FROM_LEGION) == LP_KICK_FROM_LEGION;
    }

    /**
     * @return true if legion member has enough rights for Invite to Legion
     */
    public boolean canInviteToLegion(int permission1) {
        return (permission1 & LP_INVITE_TO_LEGION) == LP_INVITE_TO_LEGION;
    }
}
