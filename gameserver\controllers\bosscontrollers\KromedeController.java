/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.controllers.MoveController;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.gameobjects.stats.StatEffectType;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.utils.MathUtil;
import gameserver.world.WorldMapType;

import java.util.ArrayList;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * 
 */
public class KromedeController extends BossController {
    private static AtomicInteger spawnCounter = new AtomicInteger(0);

    public KromedeController() {
        super(212846, true);
    }

    @Override
    public void onCreation() {
        int counter = spawnCounter.incrementAndGet() % 2;

        getOwner().setNoHome(true);
        getOwner().setAi(null);
        
        waypoints = new WaypointList(WorldMapType.INGGISON.getId(), WaypointMode.RUN_IGNORE);

        switch (counter) {
            case 1:
                waypoints.add(3025, 772, 359);
                waypoints.add(3019, 797, 359);
                waypoints.add(3030, 799, 359);
                waypoints.add(3024, 802, 359);
                waypoints.add(3034, 795, 359);
                waypoints.add(3036, 814, 359);
                waypoints.add(3028, 798, 359);
                waypoints.add(3001, 798, 359);
                waypoints.add(3015, 795, 359);
                break;
            case 0:
                waypoints.add(2991, 810, 359);
                waypoints.add(2995, 815, 359);
                waypoints.add(2992, 810, 359);
                waypoints.add(2991, 810, 359);
                waypoints.add(2992, 810, 359);
                waypoints.add(2991, 810, 359);
                waypoints.add(2984, 809, 359);
                waypoints.add(2982, 813, 359);
                waypoints.add(2984, 809, 359);
                waypoints.add(2982, 813, 359);
                waypoints.add(2984, 809, 359);
                break;
        }

        waypoints.adjustZCoordinates();

        TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

        mods.add(AddModifier.newInstance(StatEnum.ABNORMAL_RESISTANCE_ALL, -2000, true));
        mods.add(AddModifier.newInstance(StatEnum.MAGICAL_RESIST, 320, true));

        getOwner().getGameStats().endEffect(
            StatEffectId.getInstance(getOwner().getObjectId(), StatEffectType.SUMMON_EFFECT));
        getOwner().getGameStats().addModifiers(
            StatEffectId.getInstance(getOwner().getObjectId(), StatEffectType.SUMMON_EFFECT), mods);
    }

    protected void think() {
        
    }
}
