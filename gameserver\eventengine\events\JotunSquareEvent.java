/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class JotunSquareEvent extends MobEvent {
    public JotunSquareEvent() {
        super.mapId = 600010000;
        super.center = new SpawnPosition(700, 767, 292);
        super.apBasePlayer = 500;
        super.apPoolPerPlayer = 500;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("Jotun Square in Silentera will house an event in 2 minutes");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at Jotun Square in Silentera starts in 1 minute", 1 * 60 * 1000);
        announceAll("The event at Jotun Square in Silentera starts in 30 seconds", 30 * 1000 + 1
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 2 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters in Jotun Square in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217079, 672, 745, 290);
        spawnMob(217079, 684, 744, 290);
        spawnMob(217079, 699, 735, 290);
        spawnMob(217079, 697, 724, 290);
        spawnMob(217079, 677, 728, 290);
        spawnMob(217079, 728, 723, 290);
        spawnMob(217079, 730, 735, 290);
        spawnMob(217079, 741, 750, 290);
        spawnMob(217079, 753, 752, 290);
        spawnMob(217079, 753, 726, 290);
        spawnMob(217079, 758, 779, 290);
        spawnMob(217079, 745, 781, 290);
        spawnMob(217079, 730, 793, 290);
        spawnMob(217079, 728, 809, 290);
        spawnMob(217079, 751, 806, 290);
        spawnMob(217079, 702, 810, 290);
        spawnMob(217079, 700, 796, 290);
        spawnMob(217079, 688, 783, 290);
        spawnMob(217079, 672, 780, 290);
        spawnMob(217079, 673, 805, 290);
    }
}
