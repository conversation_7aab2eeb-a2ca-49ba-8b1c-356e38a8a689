/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.PvpService;
import gameserver.utils.MathUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class InvasionMobController extends BossController {
    private final BossSkill KNOCKBACK = new BossSkill(16858, 1);
    private final BossSkill DISABLE = new BossSkill(16868, 1);

    private long nextMove = 0;

    public InvasionMobController() {
        super(Arrays.asList(234665, 234666, 234667, 234668, 234673, 234674, 234676, 234677), false);
    }

    protected void think() {
        if (getOwner().getAggroList().getMostHated() != null && getOwner().getTarget() != null) {
            getOwner().setNoHome(false);

            if (Rnd.get(1000) < 20 && KNOCKBACK.timeSinceUse() > 60)
                queueSkill(KNOCKBACK, getOwner());
            else if (Rnd.get(1000) < 15 && DISABLE.timeSinceUse() > 90)
                queueSkill(DISABLE, getOwner());

            return;
        }

        getOwner().setNoHome(true);

        if (nextMove == 0) {
            nextMove = System.currentTimeMillis() + Rnd.get(8000, 14000);
        }
        else if (System.currentTimeMillis() > nextMove) {
            nextMove = 0;

            randomWalk(8);
        }
    }

    @Override
    public void doReward() {
        Npc owner = getOwner();

        List<Player> players = new ArrayList<Player>();
        players.addAll(getOwner().getKnownList().getPlayers());

        for (Iterator<Player> it = players.iterator(); it.hasNext();) {
            Player pl = it.next();

            if (!MathUtil.isIn3dRange(pl, owner, 20) || pl.getLifeStats().isAlreadyDead())
                it.remove();
        }

        if (players.size() == 0) {
            Player winner = getOwner().getAggroList().getMostPlayerDamage();

            if (winner != null) {
                PvpService.getInstance().addMight(winner, 30);
            }

            return;
        }

        float mightReward = 30 / players.size();

        for (Player pl : players)
            PvpService.getInstance().addMight(pl, mightReward);
    }
}
