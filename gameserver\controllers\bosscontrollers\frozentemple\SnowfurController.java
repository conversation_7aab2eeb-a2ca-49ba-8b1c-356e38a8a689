/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.frozentemple;

import gameserver.ai.state.AIState;
import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.utils.MathUtil;
import gameserver.world.World;

/**
 * <AUTHOR>
 */
public class SnowfurController extends BossController {
    private final BossSkill BLEED = new BossSkill(18678, 1);
    private final BossSkill SLOW = new BossSkill(19004, 1);

    public SnowfurController() {
        super(213706, true);
    }

    @Override
    protected void think() {
        Npc owner = getOwner();
        SpawnTemplate spawn = owner.getSpawn();

        if (MathUtil.getDistance(owner, spawn.getX(), spawn.getY(), spawn.getZ()) > 15) {
            owner.getAi().clearDesires();
            owner.getAggroList().clearHate();

            owner.getMoveController().stop();
            World.getInstance().updatePosition(owner, spawn.getX(), spawn.getY(), spawn.getZ(),
                spawn.getHeading(), true);
            owner.getController().stopMoving();

            owner.setInCombat(false);

            owner.getLifeStats().increaseHp(TYPE.NATURAL_HP, owner.getLifeStats().getMaxHp());
            owner.getLifeStats().increaseMp(TYPE.NATURAL_MP, owner.getLifeStats().getMaxMp());

            owner.getAi().setAiState(AIState.ACTIVE);
        }
        else {
            Player priority = getPriorityTarget();
            if (priority == null || !MathUtil.isIn3dRange(owner, priority, 20))
                return;

            getOwner().getAggroList().addHate(priority, 10000);

            int cooldown1 = (getOwner().getBattleground() != null
                && getOwner().getBattleground().isSpecial()) ? 2 : 3;
            
            int cooldown2 = (getOwner().getBattleground() != null
                && getOwner().getBattleground().isSpecial()) ? 4 : 6;

            if (BLEED.timeSinceUse() > cooldown1)
                queueSkill(BLEED, owner);
            if (SLOW.timeSinceUse() > cooldown2)
                queueSkill(SLOW, priority);
        }
    }
}