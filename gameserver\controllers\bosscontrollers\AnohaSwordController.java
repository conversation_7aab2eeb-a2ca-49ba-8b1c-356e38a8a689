/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_DIALOG_WINDOW;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * 
 */
public class AnohaSwordController extends BossController {
    private AtomicBoolean done = new AtomicBoolean(false);

    private static final int SEALING_STONE_ID = 185000215;

    private static final int SPAWN_DELAY = 55 * 1000;

    public AnohaSwordController() {
        super(804576);
    }

    protected void think() {
        // Do nothing
    }

    @Override
    public void onDialogRequest(Player player) {
        if (player.getInventory().getItemCountByItemId(SEALING_STONE_ID) < 1) {
            PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(getOwner().getObjectId(),
                1011));
        }
        else {
            PacketSendUtility
                .sendPacket(player, new SM_DIALOG_WINDOW(getOwner().getObjectId(), 10));
        }
    }

    @Override
    public void onDialogSelect(int dialogId, Player player, int questId) {
        switch (dialogId) {
            case 10000:
                if (player.getInventory().getItemCountByItemId(SEALING_STONE_ID) < 1) {
                    PacketSendUtility.sendMessage(player, "You need an [item:" + SEALING_STONE_ID
                        + "] to summon Anoha.");
                    PacketSendUtility.sendPacket(player, new SM_DIALOG_WINDOW(getOwner()
                        .getObjectId(), 0));
                    break;
                }

                player.getInventory().removeFromBagByItemId(SEALING_STONE_ID, 1);

                if (!done.compareAndSet(false, true))
                    break;
                
                if (getOwner().getBattleground() != null) {
                    getOwner().getBattleground().onKill(getOwner(), player);
                    return;
                }

                PacketSendUtility.broadcastPacket(getOwner(),
                    SM_SYSTEM_MESSAGE.STR_MSG_LDF5_FORTRESS_NAMED_SPAWN());
                PacketSendUtility.sendPacket(player,
                    SM_SYSTEM_MESSAGE.STR_MSG_LDF5_FORTRESS_NAMED_SPAWN_ITEM());

                getOwner().getSpawn().getSpawnGroup().setInterval(6 * 60 * 60);

                this.scheduleBossAppearance(getOwner().getWorldId(), getOwner().getInstanceId(),
                    getOwner().getX(), getOwner().getY(), getOwner().getZ(), getOwner()
                        .getHeading());

                super.onDelete();
                super.scheduleRespawn();
                break;
        }
    }

    private void scheduleBossAppearance(final int worldId, final int instanceId, final float x,
        final float y, final float z, final byte h) {
        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                spawn(702644, worldId, instanceId, x, y, z, h, true).delete(SPAWN_DELAY - 1 * 1000);
            }
        }, 1 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                int bossId = 855263;

                Npc boss = spawn(bossId, worldId, instanceId, x, y, z, h, true).delete(
                    60 * 60 * 1000);
            }
        }, SPAWN_DELAY);
    }
}
