/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.PlayerGroup;
import gameserver.services.LadderService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.utils.ThreadPoolManager;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ThreeTeamKillCountBg extends Battleground {
    private int extraCounter = 0;

    public ThreeTeamKillCountBg() {
        super.name = "3-Team Kill Count";
        super.description = "You are on a team and must kill as many enemies as possible within the time limit. You will respawn at your base every 30 seconds.";
        super.minSize = 3;
        super.maxSize = 6;
        super.teamCount = 3;
        super.matchLength = 300;

        BattlegroundMap map1 = new BattlegroundMap(300100000);
        map1.addSpawn(new SpawnPosition(483.0f, 508.0f, 1032.84f));
        map1.addSpawn(new SpawnPosition(622.0f, 548.0f, 1031.05f));
        map1.addSpawn(new SpawnPosition(623.0f, 471.0f, 1031.05f));
        map1.setKillZ(1010f);

        BattlegroundMap map2 = new BattlegroundMap(220030000);
        map2.addSpawn(new SpawnPosition(2295.2f, 2105.0f, 279.2f));
        map2.addSpawn(new SpawnPosition(2409.2f, 2154.3f, 268.3f));
        map2.addSpawn(new SpawnPosition(2305.5f, 2252.4f, 279.9f));
        map2.setKillZ(260f);

        BattlegroundMap map3 = new BattlegroundMap(300350000);
        map3.addSpawn(new SpawnPosition(1860.4f, 1693.7f, 311.2f));
        map3.addSpawn(new SpawnPosition(1799.4f, 1750.2f, 311.2f));
        map3.addSpawn(new SpawnPosition(1860.0f, 1776.5f, 311.2f));
        map3.addStaticDoor(186);
        map3.addStaticDoor(180);
        map3.addStaticDoor(185);
        map3.addStaticDoor(182);
        map3.addStaticDoor(179);
        map3.addStaticDoor(177);
        map3.addStaticDoor(183);
        map3.addStaticDoor(176);
        map3.addStaticDoor(178);
        map3.addStaticDoor(181);
        map3.addStaticDoor(187);
        map3.addStaticDoor(184);
        map3.setKillZ(295f);

        BattlegroundMap map4 = new BattlegroundMap(300250000);
        map4.addSpawn(new SpawnPosition(1129.4f, 966.4f, 322.5f));
        map4.addSpawn(new SpawnPosition(1073.5f, 875.2f, 326.1f));
        map4.addSpawn(new SpawnPosition(1179.3f, 873.4f, 326.4f));
        map4.setKillZ(315f);

        BattlegroundMap map5 = new BattlegroundMap(300280000);
        map5.addSpawn(new SpawnPosition(179.6f, 373.5f, 260f));
        map5.addSpawn(new SpawnPosition(222.8f, 271.1f, 256f));
        map5.addSpawn(new SpawnPosition(87.9f, 307.3f, 256f));
        map5.setKillZ(250f);
        
        BattlegroundMap map6 = new BattlegroundMap(400010000);
        map6.addSpawn(1323.3f, 2988.7f, 3035f);
        map6.addSpawn(1364.6f, 3106.8f, 3036f);
        map6.addSpawn(1235.1f, 3077.2f, 3036f);
        map6.setKillZ(3030f);
        map6.setRestrictFlight(true);

        super.maps.add(map1);
        // super.maps.add(map2);
        // super.maps.add(map3);
        super.maps.add(map4);
        super.maps.add(map5);
        super.maps.add(map6);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayer(pl, 30000);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    if (pos != null)
                        performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                    else
                        log.error("pos == null!");
                }
            }
        }

        super.specAnnounce("The match has begun!!!", 30000);

        startBattleground(30000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups() <= 1)
                    endThreeTeamMatch();
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endThreeTeamMatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    @Override
                    public void run() {
                        extraCounter++;

                        showTeamPositions();

                        if ((extraCounter % 2) == 0) {
                            extraCounter = 0;
                            autoResurrection();
                        }
                    }
                }, 15 * 1000, 15 * 1000));
            }
        }, 30 * 1000);
    }

    @Override
    public boolean createTournament(List<List<Player>> teams) {
        if (!super.createGroups(teams))
            return false;

        this.matchLength = 510;
        startMatch();

        return true;
    }

    public void showTeamPositions() {
        PlayerGroup leading = null;
        for (PlayerGroup group : super.getGroups()) {
            if (leading == null && group.getKillCount() > 0)
                leading = group;
            else if (leading != null && group.getKillCount() > leading.getKillCount())
                leading = group;
        }

        if (leading != null) {
            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == leading.getGroupId()) {
                    for (Player pl : group.getMembers())
                        super.scheduleAnnouncement(pl, "Your team is in the lead on kills!", 0);
                }
                else {
                    int killDiff = leading.getKillCount() - group.getKillCount();
                    for (Player pl : group.getMembers())
                        super.scheduleAnnouncement(pl, "Your team is " + killDiff
                            + " kills behind the leading team!", 0);
                }
            }
            super.specAnnounce(LadderService.getInstance().getNameByIndex(leading.getBgIndex())
                + " is in the lead on kills!");
        }
    }

    public void autoResurrection() {
        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead()) {
                    pl.getReviveController().fullRevive();
                    super.spawnProtection(pl);
                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    TeleportService.teleportTo(pl, getMapId(), super.getInstanceId(), pos.getX(),
                        pos.getY(), pos.getZ(), 0);
                    super
                        .scheduleAnnouncement(pl, "You have been automatically resurrected!", 1500);
                }
            }
        }
    }

    private PlayerGroup getWinner() {
        PlayerGroup winner = null;
        int drawPoints = 0;

        for (PlayerGroup group : super.getGroups()) {
            if (winner == null && group.getKillCount() > drawPoints) {
                winner = group;
            }
            else if (winner == null) {
                continue;
            }
            else if (winner.getKillCount() < group.getKillCount()) {
                winner = group;
            }
            else if (winner.getKillCount() == group.getKillCount()) {
                drawPoints = winner.getKillCount();
                winner = null;
            }
        }

        return winner;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super.scheduleAnnouncement(player, "You will automatically resurrect every 30 seconds.", 0);

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            if (killer.getPlayerGroup() == null) {
                log.error("PlayerGroup == null in ThreeTeamKillCountBg!");
            }
            else {
                for (Player pl : killer.getPlayerGroup().getMembers())
                    PvpService.getInstance().addMight(pl, 2);
            }
        }
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingGroups() <= 1)
            endThreeTeamMatch();
    }

    private void endThreeTeamMatch() {
        super.onEndFirstDefault();

        PlayerGroup winner = getWinner();
        if (winner == null) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    scheduleAnnouncement(pl, "The match was a draw! Better luck next time.", 0);
                    super.rewardPlayer(pl, 10, false);
                }
            }
            super.specAnnounce("The match was a draw!");
        }
        else {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            int averageRating = super.computeAverageGroupRating(super.getGroups());

            for (PlayerGroup group : super.getGroups()) {
                if (group.getGroupId() == winner.getGroupId()) {
                    for (Player pl : group.getMembers()) {
                        int premadeCheck = super.premadeOpponentCheck(group.getBgIndex()) ? 2 : 1;

                        if (LadderService.getInstance().getCachedRating(pl) - averageRating <= super
                            .getMaxRatingDiff() * averageRating / (float) group.getAverageRating())
                            super.playerWinMatch(pl, premadeCheck * super.K_VALUE * 2 / 3);
                        else
                            super
                                .message(pl,
                                    "You have not been credited with the win due to the match being heavily unfair.");

                        super.scheduleAnnouncement(pl,
                            "Your team has won the match with " + group.getKillCount() + " kills!",
                            0);
                        super.scheduleAnnouncement(pl,
                            "You have been rewarded with might and rating for your great effort!",
                            3000);
                        super.rewardPlayer(pl, 20, true);
                    }
                }
                else {
                    for (Player pl : group.getMembers()) {
                        super.playerLoseMatch(pl, -super.K_VALUE / 3);

                        super.scheduleAnnouncement(pl,
                            "Your team has lost the match with " + group.getKillCount()
                                + " kills against " + winner.getKillCount() + "!", 0);
                        super.scheduleAnnouncement(pl,
                            "You have received some might but lost rating.", 3000);
                        super.rewardPlayer(pl, 10, false);
                    }
                }
            }

            super.specAnnounce(LadderService.getInstance().getNameByIndex(winner.getBgIndex())
                + " has won the match with " + winner.getKillCount() + " kills!");
        }

        super.propagateDone();

        super.onEndDefault();
    }
}