/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects;

import gameserver.configs.main.GSConfig;
import gameserver.dataholders.DataManager;
import gameserver.itemengine.actions.ItemActions;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.items.FusionStone;
import gameserver.model.items.GodStone;
import gameserver.model.items.Idian;
import gameserver.model.items.ItemStorage;
import gameserver.model.items.ManaStone;
import gameserver.model.templates.PolishGroup;
import gameserver.model.templates.PolishTemplate;
import gameserver.model.templates.RandomGroup;
import gameserver.model.templates.RandomOptionTemplate;
import gameserver.model.templates.item.EquipType;
import gameserver.model.templates.item.ItemCategory;
import gameserver.model.templates.item.ItemQuality;
import gameserver.model.templates.item.ItemTemplate;
import gameserver.model.templates.item.ItemType;
import gameserver.services.ItemService;
import gameserver.services.RentalService;

import java.sql.Timestamp;
import java.util.Comparator;
import java.util.Set;
import java.util.TreeSet;

import org.apache.log4j.Logger;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class Item extends AionObject {
    private static Logger log = Logger.getLogger(Item.class);

    private int itemId;

    private long itemCount = 1;

    private int itemColor = 0;

    private ItemTemplate itemTemplate;
    private ItemTemplate itemSkinTemplate;

    private boolean isEquipped = false;

    private long equipmentSlot = ItemStorage.FIRST_AVAILABLE_SLOT;

    private PersistentState persistentState;

    private Set<ManaStone> manaStones;

    private Set<FusionStone> fusionStones;

    private int optionalSocket;

    private int optionalFusionSocket;

    private GodStone godStone;

    private Idian idian;

    private boolean isSoulBound = false;

    private int itemLocation;

    private int enchantLevel;

    private String itemCreator;

    private int fusionedItemId = 0;

    private Timestamp expireTime = null;

    private int conditioning = 0;

    private int temperanceLevel;

    private int randomOption = 0;

    private int randomFusionOption = 0;

    private int bonusEnchant = 0;

    private boolean wrapped = false;

    private boolean amplified = false;

    private boolean glow = false;

    private PersistentState glowPersistentState = PersistentState.NOACTION;

    private int amplificationSkillId = 0;

    private PersistentState amplificationPersistentState = PersistentState.NOACTION;

    private boolean godstoneGlow = false;

    private PersistentState godstonePersistentState = PersistentState.NOACTION;

    private boolean temporary = false;

    /**
     * @param objId
     * @param itemId
     * @param itemTemplate
     * @param itemCount
     * @param itemCreator
     * @param isEquipped
     * @param equipmentSlot
     *            This constructor should be called from ItemService for newly created items and loadedFromDb
     */
    public Item(int objId, int itemId, ItemTemplate itemTemplate, long itemCount,
        String itemCreator, boolean isEquipped, long equipmentSlot) {
        super(objId);

        this.itemId = itemId;
        this.itemTemplate = itemTemplate;
        this.itemCount = itemCount;
        this.itemCreator = itemCreator;
        this.isEquipped = isEquipped;
        this.equipmentSlot = equipmentSlot;

        if (itemTemplate.getExpireMinutes() > 0)
            this.expireTime = new Timestamp(System.currentTimeMillis()
                + (long) itemTemplate.getExpireMinutes() * 60L * 1000L);

        if (itemTemplate.isPreAmplified())
            this.setAmplified(true);

        if (itemTemplate.isWeapon())
            this.setGodstoneGlow(true);

        this.persistentState = PersistentState.NEW;
    }

    /**
     * @param objId
     * @param itemId
     * @param itemCount
     * @param itemColor
     * @param itemCreator
     * @param isEquipped
     * @param isSoulBound
     * @param equipmentSlot
     * @param itemLocation
     * @param enchant
     * @param itemSkin
     * @param fusionedItem
     * @param optionalSocket
     * @param optionalFusionSocket
     * 
     *            This constructor should be called only from DAO while loading from DB.
     */
    public Item(int objId, int itemId, long itemCount, int itemColor, String itemCreator,
        boolean isEquipped, boolean isSoulBound, long equipmentSlot, int itemLocation, int enchant,
        int itemSkin, int fusionedItem, int optionalSocket, int optionalFusionSocket,
        int conditioning, int temperanceLevel, int randomOption, int bonusEnchant,
        int randomFusionOption, boolean wrapped, Timestamp expireTime) {
        super(objId);

        this.itemTemplate = DataManager.ITEM_DATA.getItemTemplate(itemId);
        this.itemId = itemId;
        this.itemCount = itemCount;
        this.itemColor = itemColor;
        this.itemCreator = itemCreator;
        this.isEquipped = isEquipped;
        this.isSoulBound = isSoulBound;
        this.equipmentSlot = equipmentSlot;
        this.itemLocation = itemLocation;
        this.enchantLevel = enchant;
        this.fusionedItemId = fusionedItem;
        this.itemSkinTemplate = DataManager.ITEM_DATA.getItemTemplate(itemSkin);
        this.optionalSocket = optionalSocket;
        this.optionalFusionSocket = optionalFusionSocket;
        this.conditioning = conditioning;
        this.temperanceLevel = temperanceLevel;
        this.randomOption = randomOption;
        this.bonusEnchant = bonusEnchant;
        this.randomFusionOption = randomFusionOption;
        this.wrapped = wrapped;
        this.expireTime = expireTime;
    }

    public void tune() {
        if (getOptionalSocket() == -1) {
            setOptionalSocket(getItemTemplate().getOptionSlotBonus());
            setBonusEnchant(getItemTemplate().getMaxEnchantBonus());
        }

        RandomOptionTemplate template = DataManager.RANDOM_OPTION_DATA
            .getRandomOptionTemplate(getItemTemplate().getRandomOption());

        if (template == null)
            return;

        int rnd = Rnd.get(1000);
        int probability = 0;
        int groupId = 1;

        for (RandomGroup group : template.getGroups()) {
            probability += group.getProb();

            if (rnd < probability) {
                groupId = group.getId();
                break;
            }
        }

        setRandomOption(groupId);
    }

    @Override
    public String getName() {
        // TODO
        // item description should return probably string and not id
        return String.valueOf(itemTemplate.getNameId());
    }

    public String getItemName() {
        return itemTemplate.getName();
    }

    public int getOptionalSocket() {
        return optionalSocket;
    }

    public boolean hasOptionalSocket() {
        return optionalSocket > 0;
    }

    public void setOptionalSocket(int optionalSocket) {
        this.optionalSocket = optionalSocket;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    public int getOptionalFusionSocket() {
        return optionalFusionSocket;
    }

    public boolean hasOptionalFusionSocket() {
        return optionalFusionSocket > 0;
    }

    public void setOptionalFusionSocket(int optionalFusionSocket) {
        this.optionalFusionSocket = optionalFusionSocket;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the itemTemplate
     */
    public ItemTemplate getItemTemplate() {
        if (itemTemplate == null)
            log.warn("Item was not populated correctly. Item template is missing"
                + ", ItemObjectId: " + getObjectId() + ", ItemId: " + itemId);
        return itemTemplate;
    }

    /**
     * @return the itemAppearanceTemplate
     */
    public ItemTemplate getItemSkinTemplate() {
        if (this.itemSkinTemplate == null)
            return this.itemTemplate;
        return this.itemSkinTemplate;
    }

    public void setItemSkinTemplate(ItemTemplate newTemplate, boolean save) {
        this.itemSkinTemplate = newTemplate;
        if (save)
            setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the itemColor
     */
    public int getItemColor() {
        return itemColor;
    }

    /**
     * @param itemColor
     *            the itemColor to set
     */
    public void setItemColor(int itemColor) {
        this.itemColor = itemColor;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @param itemTemplate
     *            the itemTemplate to set
     */
    public void setItemTemplate(ItemTemplate itemTemplate) {
        this.itemTemplate = itemTemplate;
    }

    /**
     * @return the itemCount Number of this item in stack. Should be not more than template maxstackcount ?
     */
    public long getItemCount() {
        return itemCount;
    }

    /**
     * @param itemCount
     *            the itemCount to set
     */
    public void setItemCount(long itemCount) {
        this.itemCount = itemCount;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * This method should be called ONLY from Storage class In all other ways it is not guaranteed to be updated in a
     * regular update service It is allowed to use this method for newly created items which are not yet in any storage
     * 
     * @param addCount
     */
    public void increaseItemCount(long addCount) {
        if (this.itemCount > 0 && this.itemCount + addCount < 0)
            this.itemCount = Long.MAX_VALUE;
        else
            this.itemCount += addCount;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * This method should be called ONLY from Storage class In all other ways it is not guaranteed to be updated in a
     * regular update service It is allowed to use this method for newly created items which are not yet in any storage
     * 
     * @param remCount
     */
    public boolean decreaseItemCount(long remCount) {
        if (this.itemCount - remCount >= 0) {
            this.itemCount -= remCount;
            if (itemCount == 0 && !this.itemTemplate.isKinah()) {
                setPersistentState(PersistentState.DELETED);

                if (RentalService.getInstance().isRentalItem(this))
                    RentalService.getInstance().removeRentalItem(this);
            }
            else {
                setPersistentState(PersistentState.UPDATE_REQUIRED);
            }
            return true;
        }

        return false;
    }

    public boolean isReadable() {
        ItemActions actions = this.getItemTemplate().getActions();
        return actions != null && actions.getReadActions().size() > 0;
    }

    /**
     * @return the isEquipped
     */
    public boolean isEquipped() {
        return isEquipped;
    }

    /**
     * @param isEquipped
     *            the isEquipped to set
     */
    public void setEquipped(boolean isEquipped) {
        this.isEquipped = isEquipped;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the equipmentSlot Equipment slot can be of 2 types - one is the ItemSlot enum type if equipped, second -
     *         is position in cube
     */
    public long getEquipmentSlot() {
        return equipmentSlot;
    }

    /**
     * @param equipmentSlot
     *            the equipmentSlot to set
     */
    public void setEquipmentSlot(long equipmentSlot) {
        this.equipmentSlot = equipmentSlot;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * This method should be used to lazy initialize empty manastone list
     * 
     * @return the itemStones
     */
    public Set<ManaStone> getItemStones() {
        if (manaStones == null)
            this.manaStones = new TreeSet<ManaStone>(new Comparator<ManaStone>() {

                @Override
                public int compare(ManaStone o1, ManaStone o2) {
                    if (o1.getSlot() == o2.getSlot())
                        return 0;
                    return o1.getSlot() > o2.getSlot() ? 1 : -1;
                }

            });
        return manaStones;
    }

    public int getItemStoneCount() {
        return getItemStones().size();
    }

    public int getItemStoneSpecialCount() {
        int count = 0;

        for (ManaStone stone : getItemStones()) {
            ItemTemplate template = DataManager.ITEM_DATA.getItemTemplate(stone.getItemId());

            if (template != null && template.isAncientStone())
                count++;
        }

        return count;
    }

    /**
     * This method should be used to lazy initialize empty manastone list
     * 
     * @return the itemStones
     */
    public Set<FusionStone> getFusionStones() {
        if (fusionStones == null)
            this.fusionStones = new TreeSet<FusionStone>(new Comparator<FusionStone>() {

                @Override
                public int compare(FusionStone o1, FusionStone o2) {
                    if (o1.getSlot() == o2.getSlot())
                        return 0;
                    return o1.getSlot() > o2.getSlot() ? 1 : -1;
                }

            });
        return fusionStones;
    }

    public int getFusionStoneCount() {
        return getFusionStones().size();
    }

    public int getFusionStoneSpecialCount() {
        int count = 0;

        for (FusionStone stone : getFusionStones()) {
            ItemTemplate template = DataManager.ITEM_DATA.getItemTemplate(stone.getItemId());

            if (template != null && template.isAncientStone())
                count++;
        }

        return count;
    }

    /**
     * Check manastones without initialization
     * 
     * @return
     */
    public boolean hasManaStones() {
        return manaStones != null && manaStones.size() > 0;
    }

    /**
     * Check manastones without initialization
     * 
     * @return
     */
    public boolean hasFusionStones() {
        return fusionStones != null && fusionStones.size() > 0;
    }

    /**
     * @return the goodStone
     */
    public GodStone getGodStone() {
        return godStone;
    }

    /**
     * 
     */
    public Idian getIdian() {
        return idian;
    }

    /**
     * @param itemId
     * @return
     */
    public GodStone addGodStone(int itemId) {
        PersistentState state = this.godStone != null
            && this.godStone.getPersistentState() != PersistentState.NEW ? PersistentState.UPDATE_REQUIRED
            : PersistentState.NEW;
        this.godStone = new GodStone(getObjectId(), itemId, state);
        return this.godStone;
    }

    public Idian addIdian(int itemId) {
        ItemTemplate it = ItemService.getItemTemplate(itemId);
        if (it == null)
            return null;

        PolishTemplate pt = DataManager.POLISH_DATA.getPolishTemplate(it.getIdianSetId());
        if (pt == null)
            return null;

        int rnd = Rnd.get(1000);
        int probability = 0;
        int groupId = 1;

        for (PolishGroup group : pt.getGroups()) {
            probability += group.getProb();

            if (rnd < probability) {
                groupId = group.getId();
                break;
            }
        }

        PersistentState state = this.idian != null
            && this.idian.getPersistentState() != PersistentState.NEW ? PersistentState.UPDATE_REQUIRED
            : PersistentState.NEW;

        this.idian = new Idian(getObjectId(), itemId, groupId, state);

        return this.idian;
    }

    /**
     * @param goodStone
     *            the goodStone to set
     */
    public void setGoodStone(GodStone goodStone) {
        this.godStone = goodStone;
    }

    /**
     * 
     */
    public void setIdian(Idian idian) {
        this.idian = idian;
    }

    /**
     * @return the echantLevel
     */
    public int getEnchantLevel() {
        return enchantLevel;
    }

    /**
     * @param enchantLevel
     *            the echantLevel to set
     */
    public void setEnchantLevel(int enchantLevel) {
        if (enchantLevel > 127)
            enchantLevel = 127;
        this.enchantLevel = enchantLevel;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    public int getTemperanceLevel() {
        return temperanceLevel;
    }

    public void setTemperanceLevel(int temperanceLevel) {
        if (temperanceLevel > 127)
            temperanceLevel = 127;
        this.temperanceLevel = temperanceLevel;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the persistentState
     */
    public PersistentState getPersistentState() {
        return persistentState;
    }

    /**
     * Possible changes: NEW -> UPDATED NEW -> UPDATE_REQURIED UPDATE_REQUIRED -> DELETED UPDATE_REQUIRED -> UPDATED
     * UPDATED -> DELETED UPDATED -> UPDATE_REQUIRED
     * 
     * @param persistentState
     *            the persistentState to set
     */
    public void setPersistentState(PersistentState persistentState) {
        if (temporary)
            return;

        switch (persistentState) {
            case DELETED:
                if (this.persistentState == PersistentState.NEW) {
                    this.persistentState = PersistentState.NOACTION;
                    ItemService.releaseItemId(this);
                }
                else
                    this.persistentState = PersistentState.DELETED;
                break;
            case UPDATE_REQUIRED:
                if (this.persistentState == PersistentState.NEW)
                    break;
            default:
                this.persistentState = persistentState;
        }

    }

    public void setItemLocation(int storageType) {
        this.itemLocation = storageType;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    public int getItemLocation() {
        return itemLocation;
    }

    public int getItemMask() {
        return itemTemplate.getMask();
    }

    public boolean isSoulBound() {
        return isSoulBound;
    }

    public void setSoulBound(boolean isSoulBound) {
        this.isSoulBound = isSoulBound;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    public EquipType getEquipmentType() {
        if (itemTemplate.isStigma())
            return EquipType.STIGMA;
        else
            return itemTemplate.getEquipmentType();
    }

    @Override
    public String toString() {
        return "Item [equipmentSlot=" + equipmentSlot + ", godStone=" + godStone + ", isEquipped="
            + isEquipped + ", itemColor=" + itemColor + ", itemCount=" + itemCount
            + ", itemLocation=" + itemLocation + ", itemTemplate=" + itemTemplate + ", manaStones="
            + manaStones + ", persistentState=" + persistentState + "]";
    }

    public int getItemId() {
        return itemTemplate.getTemplateId();
    }

    public int getNameID() {
        return itemTemplate.getNameId();
    }

    public boolean hasFusionedItem() {
        return (fusionedItemId != 0);
    }

    public int getFusionedItem() {
        return fusionedItemId;
    }

    public void setFusionedItem(int itemTemplateId) {
        fusionedItemId = itemTemplateId;
    }

    private static int basicSocket(ItemQuality rarity) {
        switch (rarity) {
            case COMMON:
                return 1;
            case RARE:
                return 2;
            case LEGEND:
                return 3;
            case UNIQUE:
                return 4;
            case EPIC:
            case MYTHIC:
                return 5;
            default:
                return 1;
        }
    }

    private static int extendedSocket(ItemType type) {
        switch (type) {
            case NORMAL:
                return 0;
            case ABYSS:
                return 2;
            case DRACONIC:
                return 0;
            case DEVANION:
                return 0;
            default:
                return 0;
        }
    }

    public int getSockets(boolean isFusionItem) {
        int numSockets;
        if (itemTemplate.isWeapon() || itemTemplate.isArmor()) {
            if (isFusionItem) {
                ItemTemplate fuseTemplate = DataManager.ITEM_DATA
                    .getItemTemplate(getFusionedItem());
                numSockets = fuseTemplate.getStoneSlots();
                // numSockets += fuseTemplate.getSpecialSlots();
                numSockets += hasOptionalFusionSocket() ? getOptionalFusionSocket() : 0;
            }
            else {
                numSockets = getItemTemplate().getStoneSlots();
                // numSockets += getItemTemplate().getSpecialSlots();
                numSockets += hasOptionalSocket() ? getOptionalSocket() : 0;
            }

            return Math.min(6, numSockets);

            /*
             * if (isFusionItem) { ItemTemplate fusedTemp = DataManager.ITEM_DATA.getItemTemplate(getFusionedItem());
             * numSockets = basicSocket(fusedTemp.getItemQuality()); numSockets +=
             * extendedSocket(fusedTemp.getItemType()); numSockets += hasOptionalFusionSocket() ?
             * getOptionalFusionSocket() : 0; } else { numSockets = basicSocket(getItemTemplate().getItemQuality());
             * numSockets += extendedSocket(getItemTemplate().getItemType()); numSockets += hasOptionalSocket() ?
             * getOptionalSocket() : 0; } if (numSockets < 6) return numSockets; return 6;
             */
        }

        return 0;
    }

    public boolean isStorable(int storageType) {
        switch (storageType) {
            case 0:
                return true;
            case 1:// regular warehouse
                if (this.getItemTemplate().isStorableinCharWarehouse())
                    return true;
                break;
            case 2:// account warehouse
                if (this.getItemTemplate().isStorableinAccWarehouse())
                    return true;
                break;
            case 3:// legion warehouse
                if (this.getItemTemplate().isStorableinLegionWarehouse())
                    return true;
                break;
        }

        return false;
    }

    /**
     * Tests whether the Item is a weapon and has been swapped into the non-active hand slot for a given Player.
     * 
     * @param player
     * @return Weapon Swapped Outcome.
     */
    public boolean isWeaponSwapped(final Player player) {
        // Does not add weapon stats if the weapon has been swapped.
        if (itemTemplate.isArmor())
            return false;
        if (itemTemplate.isWeapon()
            && (this == player.getEquipment().getMainHandWeapon() || this == player.getEquipment()
                .getOffHandWeapon())) {
            return false;
        }
        return true;
    }

    public int getMaxStoneSlots() {

        int slots = getItemTemplate().getStoneSlots();
        // slots += getItemTemplate().getSpecialSlots();
        slots += hasOptionalSocket() ? getOptionalSocket() : 0;

        return Math.min(6, slots);

        /*
         * switch (itemTemplate.getItemQuality()) { case COMMON: case JUNK: slots = 1; break; case RARE: slots = 2;
         * break; case LEGEND: slots = 3; break; case UNIQUE: slots = 4; break; case EPIC: case MYTHIC: slots = 5;
         * break; default: slots = 0; break; } if (itemTemplate.getItemType() == ItemType.DRACONIC) slots += 1; if
         * (itemTemplate.getItemType() == ItemType.ABYSS) slots += 2; slots += optionalSocket; if (slots > 6) slots = 6;
         * return slots;
         */
    }

    /**
     * @param return itemCreator
     */
    public String getItemCreator() {
        return itemCreator;
    }

    /**
     * @param itemCreator
     *            the itemCreator to set
     */
    public void setItemCreator(String itemCreator) {
        this.itemCreator = itemCreator;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @param expireTime
     *            the expireTime to set
     */
    public void setExpireTime(Timestamp expireTime) {
        this.expireTime = expireTime;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the expireTime
     */
    public Timestamp getExpireTime() {
        return expireTime;
    }

    /**
     * @param conditioning
     *            the conditioning to set
     */
    public void setConditioning(int conditioning) {
        this.conditioning = conditioning;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the conditioning
     */
    public int getConditioning() {
        return conditioning;
    }

    /**
     * @return the conditioningLevel
     */
    public int getConditioningLevel() {
        return (int) Math.ceil(conditioning / 500000d);
    }

    /**
     * @return the randomOption
     */
    public int getRandomOption() {
        return randomOption;
    }

    /**
     * @param randomOption
     *            the randomOption to set
     */
    public void setRandomOption(int randomOption) {
        this.randomOption = randomOption;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the randomFusionOption
     */
    public int getRandomFusionOption() {
        return randomFusionOption;
    }

    /**
     * @param randomFusionOption
     *            the randomFusionOption to set
     */
    public void setRandomFusionOption(int randomFusionOption) {
        this.randomFusionOption = randomFusionOption;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the bonusEnchant
     */
    public int getBonusEnchant() {
        return bonusEnchant;
    }

    /**
     * @param bonusEnchant
     *            the bonusEnchant to set
     */
    public void setBonusEnchant(int bonusEnchant) {
        this.bonusEnchant = bonusEnchant;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the glow
     */
    public boolean isGlow() {
        return glow;
    }

    /**
     * @param glow
     *            the glow to set
     */
    public void setGlow(boolean glow) {
        this.glow = glow;
        setGlowPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the glowPersistentState
     */
    public PersistentState getGlowPersistentState() {
        return glowPersistentState;
    }

    /**
     * @param glowPersistentState
     *            the glowPersistentState to set
     */
    public void setGlowPersistentState(PersistentState glowPersistentState) {
        this.glowPersistentState = glowPersistentState;
    }

    /**
     * @return the wrapped
     */
    public boolean isWrapped() {
        return wrapped;
    }

    /**
     * @param wrapped
     *            the wrapped to set
     */
    public void setWrapped(boolean wrapped) {
        this.wrapped = wrapped;
        setPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    public boolean isTradeable() {
        return (isWrapped() || itemTemplate.isTradeable()) && !isTemporary();
    }

    /**
     * @return the amplified
     */
    public boolean isAmplified() {
        if (GSConfig.SERVER_VERSION.startsWith("4.11"))
            return itemTemplate.canAmplify()
                && getEnchantLevel() >= itemTemplate.getMaxEnchant() + getBonusEnchant();

        return amplified;
    }

    /**
     * @param amplified
     *            the amplified to set
     */
    public void setAmplified(boolean amplified) {
        this.amplified = amplified;
        setAmplificationPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the amplificationSkillId
     */
    public int getAmplificationSkillId() {
        return amplificationSkillId;
    }

    /**
     * @param amplificationSkillId
     *            the amplificationSkillId to set
     */
    public void setAmplificationSkillId(int amplificationSkillId) {
        this.amplificationSkillId = amplificationSkillId;
        setAmplificationPersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the amplificationPersistentState
     */
    public PersistentState getAmplificationPersistentState() {
        return amplificationPersistentState;
    }

    /**
     * @param amplificationPersistentState
     *            the amplificationPersistentState to set
     */
    public void setAmplificationPersistentState(PersistentState amplificationPersistentState) {
        this.amplificationPersistentState = amplificationPersistentState;
    }

    /**
     * @return the godstoneGlow
     */
    public boolean isGodstoneGlow() {
        return godstoneGlow;
    }

    /**
     * @param godstoneGlow
     *            the godstoneGlow to set
     */
    public void setGodstoneGlow(boolean godstoneGlow) {
        this.godstoneGlow = godstoneGlow;
        setGodstonePersistentState(PersistentState.UPDATE_REQUIRED);
    }

    /**
     * @return the godstonePersistentState
     */
    public PersistentState getGodstonePersistentState() {
        return godstonePersistentState;
    }

    /**
     * @param godstonePersistentState
     *            the godstonePersistentState to set
     */
    public void setGodstonePersistentState(PersistentState godstonePersistentState) {
        this.godstonePersistentState = godstonePersistentState;
    }

    /**
     * @return the temporary
     */
    public boolean isTemporary() {
        return temporary;
    }

    /**
     * @param temporary
     *            the temporary to set
     */
    public void setTemporary(boolean temporary) {
        this.temporary = temporary;
    }
}
