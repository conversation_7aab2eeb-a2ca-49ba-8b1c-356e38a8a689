/*
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.ai.state.handler;

import gameserver.ai.AI;
import gameserver.ai.desires.impl.AggressionDesire;
import gameserver.ai.desires.impl.MoveToHomeDesire;
import gameserver.ai.desires.impl.WalkDesire;
import gameserver.ai.state.AIState;
import gameserver.model.EmotionType;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.services.OutpostService;
import gameserver.services.SiegeService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.world.Executor;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 */
public class ActiveAggroStateHandler extends StateHandler {
    private static final Logger log = Logger.getLogger(ActiveAggroStateHandler.class);

    private int ticks = 0;

    @Override
    public AIState getState() {
        return AIState.ACTIVE;
    }

    /**
     * State ACTIVE AI AggressiveMonsterAi AI GuardAi
     */
    @Override
    public void handleState(AIState state, final AI<?> ai) {
        final Npc owner = (Npc) ai.getOwner();

        if (!owner.isSpawned()) {
            ai.stop();
            return;
        }
        else if (owner.getActiveRegion() == null
            || (!owner.getActiveRegion().isMapRegionActive() && ticks++ < 10))
            return;

        ticks = 0;

        ai.clearDesires();

        owner.checkAggro();

        if (owner.hasWalkRoutes()) {
            ai.addDesire(new WalkDesire(owner, AIState.ACTIVE.getPriority()));
        }

        if (ai.desireQueueSize() == 0 && !owner.getMoveController().hasFollowLeader()
            && !owner.isNoHome()
            && (!owner.isAtSpawnLocation() || owner.getHeading() != owner.getSpawn().getHeading())) {
            ai.addDesire(new MoveToHomeDesire(owner, AIState.ACTIVE.getPriority()));
        }

        if (owner.getMoveController().hasFollowLeader())
            owner.getMoveController().schedule();

        if (owner.isInState(CreatureState.WEAPON_EQUIPPED) && !owner.isNoHome()
            && !owner.getMoveController().hasFollowLeader()) {
            if (owner.getTarget() == null
                || (owner.getTarget() instanceof Creature
                    && ((Creature) owner.getTarget()).getLifeStats() != null && ((Creature) owner
                        .getTarget()).getLifeStats().isAlreadyDead())) {
                owner.setTarget(null);
                owner.getAggroList().clearHate();
                owner.unsetState(CreatureState.WEAPON_EQUIPPED);
                owner.setState(CreatureState.NPC_IDLE);
                PacketSendUtility.broadcastPacket(owner, new SM_EMOTION(owner,
                    EmotionType.NEUTRALMODE));
            }
        }

        if (ai.desireQueueSize() == 0 && !owner.getLifeStats().isFullyRestoredHp()
            && !SiegeService.getInstance().isSiegeNpc(owner.getObjectId())
            && !OutpostService.getInstance().isGateSiegeNpc(owner)) {
            ai.setAiState(AIState.RESTING);
        }

        // else
        ai.schedule();
    }
}
