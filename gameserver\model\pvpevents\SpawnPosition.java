/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

/**
 * 
 * <AUTHOR>
 */
public class SpawnPosition {
    private int mapId = 0;
    private float x;
    private float y;
    private float z;
    private byte heading;
    private int npcId;

    public SpawnPosition(float x, float y, float z) {
        this(0, x, y, z, 0, 0);
    }

    public SpawnPosition(int mapId, float x, float y, float z) {
        this(mapId, x, y, z, 0, 0);
    }
    
    public SpawnPosition(int mapId, float x, float y, float z, int heading) {
        this(mapId, x, y, z, heading, 0);
    }
    
    public SpawnPosition(float x, float y, float z, int heading, int npcId) {
        this(0, x, y, z, heading, npcId);
    }
    
    public SpawnPosition(int mapId, float x, float y, float z, int heading, int npcId) {
        this.mapId = mapId;
        this.x = x;
        this.y = y;
        this.z = z;
        this.heading = (byte) heading;
        this.npcId = npcId;
    }

    /**
     * @return the x
     */
    public float getX() {
        return x;
    }

    /**
     * @param x
     *            the x to set
     */
    public void setX(float x) {
        this.x = x;
    }

    /**
     * @return the y
     */
    public float getY() {
        return y;
    }

    /**
     * @param y
     *            the y to set
     */
    public void setY(float y) {
        this.y = y;
    }

    /**
     * @return the z
     */
    public float getZ() {
        return z;
    }

    /**
     * @param z
     *            the z to set
     */
    public void setZ(float z) {
        this.z = z;
    }

    /**
     * @return the mapId
     */
    public int getMapId() {
        return mapId;
    }

    /**
     * @param mapId
     *            the mapId to set
     */
    public void setMapId(int mapId) {
        this.mapId = mapId;
    }

    /**
     * @return the heading
     */
    public byte getHeading() {
        return heading;
    }

    /**
     * @param heading the heading to set
     */
    public void setHeading(byte heading) {
        this.heading = heading;
    }

    /**
     * @return the npcId
     */
    public int getNpcId() {
        return npcId;
    }

    /**
     * @param npcId the npcId to set
     */
    public void setNpcId(int npcId) {
        this.npcId = npcId;
    }
}