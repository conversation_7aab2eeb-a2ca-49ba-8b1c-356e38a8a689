package gameserver.itemengine.actions;

import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.listeners.ItemEquipmentListener;
import gameserver.network.aion.serverpackets.SM_UPDATE_ITEM;
import gameserver.utils.PacketSendUtility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

import org.apache.log4j.Logger;

/**
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "IdentifyAction")
public class IdentifyAction extends AbstractItemAction {
    @XmlAttribute
    protected String target;

    @Override
    public boolean canAct(Player player, Item parentItem, Item targetItem) {
        return true;
    }

    @Override
    public void act(Player player, Item parentItem, Item targetItem) {
        Item item = player.getInventory().getItemByObjId(parentItem.getObjectId());

        if (target.equals("WEAPON") && !targetItem.getItemTemplate().isWeapon())
            return;
        else if (target.equals("ARMOR") && !targetItem.getItemTemplate().isArmor())
            return;
        else if (target.equals("EQUIPMENT") && !targetItem.getItemTemplate().isAccessory())
            return;

        if (item != null) {
            player.getInventory().decreaseItemCount(item, 1);

            if (targetItem.isEquipped())
                ItemEquipmentListener.removeRandomOptionEffect(player, targetItem);

            targetItem.tune();

            PacketSendUtility.sendPacket(player, new SM_UPDATE_ITEM(targetItem));

            if (targetItem.isEquipped())
                ItemEquipmentListener.addRandomOptionEffect(player, targetItem);
            
            PacketSendUtility.sendMessage(player, "The item has been retuned.");
        }
    }
}
