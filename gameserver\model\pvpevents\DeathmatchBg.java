/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.utils.ThreadPoolManager;

import java.util.ArrayList;
import java.util.List;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class DeathmatchBg extends Battleground {
    public DeathmatchBg() {
        super.name = "Deathmatch";
        super.description = "You are on your own and must kill the most enemies within the time limit to win.";
        super.minSize = 4;
        super.maxSize = 12;
        super.teamCount = 1;
        super.matchLength = 295;

        BattlegroundMap map1 = new BattlegroundMap(300050000);
        map1.addSpawn(new SpawnPosition(524.76f, 701.00f, 191.8985f));
        map1.addSpawn(new SpawnPosition(666.46f, 565.08f, 206.14534f));
        map1.addSpawn(new SpawnPosition(522.21f, 426.03f, 199.75935f));
        map1.addSpawn(new SpawnPosition(506.2f, 657.6f, 191.03f));
        map1.addSpawn(new SpawnPosition(546.0f, 671.9f, 191.03f));
        map1.addSpawn(new SpawnPosition(466.2f, 567.4f, 201.67f));
        map1.addSpawn(new SpawnPosition(503.07f, 469.94f, 198.89f));
        map1.addSpawn(new SpawnPosition(543.7f, 455.7f, 199.89f));
        map1.addSpawn(new SpawnPosition(542.3f, 546.62f, 198.88f));
        map1.addSpawn(new SpawnPosition(542.1f, 583.3f, 199.22f));
        map1.addSpawn(new SpawnPosition(638.3f, 543.3f, 204.05f));
        map1.addSpawn(new SpawnPosition(623.4f, 584.8f, 204.05f));
        map1.setKillZ(180f);

        BattlegroundMap map2 = new BattlegroundMap(210030000);
        map2.addSpawn(new SpawnPosition(2305.3f, 1259.7f, 176.3f));
        map2.addSpawn(new SpawnPosition(2250.7f, 1240.7f, 162.2f));
        map2.addSpawn(new SpawnPosition(2251.3f, 1202.0f, 158.2f));
        map2.addSpawn(new SpawnPosition(2176.0f, 1208.8f, 167.5f));
        map2.addSpawn(new SpawnPosition(2410.0f, 1218.6f, 182.3f));
        map2.addSpawn(new SpawnPosition(2309.3f, 1191.5f, 168.3f));
        map2.addSpawn(new SpawnPosition(2237.7f, 1321.4f, 170.6f));
        map2.addSpawn(new SpawnPosition(2204.6f, 1302.2f, 173.6f));
        map2.addSpawn(new SpawnPosition(2201.2f, 1263.2f, 163.5f));
        map2.addSpawn(new SpawnPosition(2330.7f, 1134.6f, 158.9f));
        map2.addSpawn(new SpawnPosition(2382.5f, 1120.8f, 166.3f));
        map2.addSpawn(new SpawnPosition(2396.1f, 1176.1f, 169.1f));
        map2.setKillZ(150f);

        BattlegroundMap map3 = new BattlegroundMap(300350000);
        map3.addSpawn(new SpawnPosition(1905.0f, 1257.6f, 288.6f));
        map3.addSpawn(new SpawnPosition(1905.9f, 1235.9f, 288.5f));
        map3.addSpawn(new SpawnPosition(1937.2f, 1221.3f, 269.8f));
        map3.addSpawn(new SpawnPosition(1929.1f, 1237.0f, 270.3f));
        map3.addSpawn(new SpawnPosition(1934.9f, 1263.8f, 272.7f));
        map3.addSpawn(new SpawnPosition(1877.8f, 1205.7f, 270.0f));
        map3.addSpawn(new SpawnPosition(1929.8f, 1157.0f, 281.0f));
        map3.addSpawn(new SpawnPosition(1960.0f, 1260.0f, 288.7f));
        map3.addSpawn(new SpawnPosition(1925.6f, 1224.0f, 269.8f));
        map3.addSpawn(new SpawnPosition(1940.5f, 1232.9f, 270.3f));
        map3.addSpawn(new SpawnPosition(1978.7f, 1282.9f, 286.3f));
        map3.addSpawn(new SpawnPosition(1989.6f, 1192.5f, 273.2f));
        map3.addStaticDoor(244);
        map3.addStaticDoor(245);
        map3.addStaticDoor(239);
        map3.addStaticDoor(236);
        map3.addStaticDoor(246);
        map3.addStaticDoor(188);
        map3.addStaticDoor(240);
        map3.addStaticDoor(243);
        map3.addStaticDoor(238);
        map3.addStaticDoor(237);
        map3.addStaticDoor(242);
        map3.addStaticDoor(241);
        map3.setKillZ(265f);

        BattlegroundMap map4 = new BattlegroundMap(300450000);
        map4.addSpawn(new SpawnPosition(499.0f, 393.0f, 205.75f));
        map4.addSpawn(new SpawnPosition(499.0f, 374.0f, 211.76f));
        map4.addSpawn(new SpawnPosition(480.0f, 368.0f, 217.44f));
        map4.addSpawn(new SpawnPosition(468.0f, 385.0f, 220.44f));
        map4.addSpawn(new SpawnPosition(482.0f, 391.0f, 196.44f));
        map4.addSpawn(new SpawnPosition(484.0f, 385.0f, 187.44f));
        map4.addSpawn(new SpawnPosition(503.0f, 376.0f, 197.44f));
        map4.addSpawn(new SpawnPosition(513.0f, 387.0f, 203.44f));
        map4.addSpawn(new SpawnPosition(507.0f, 407.0f, 210.44f));
        map4.addSpawn(new SpawnPosition(482.0f, 413.0f, 219.44f));
        map4.addSpawn(new SpawnPosition(468.0f, 398.0f, 225.44f));
        map4.addSpawn(new SpawnPosition(491.0f, 394.0f, 211.44f));
        map4.addStaticDoor(131);
        map4.addStaticDoor(128);
        map4.addStaticDoor(135);
        map4.addStaticDoor(137);
        map4.addStaticDoor(132);
        map4.addStaticDoor(136);
        map4.addStaticDoor(129);
        map4.addStaticDoor(130);
        map4.addStaticDoor(133);
        map4.addStaticDoor(127);
        map4.addStaticDoor(138);
        map4.addStaticDoor(134);
        map4.setKillZ(175f);

        BattlegroundMap map5 = new BattlegroundMap(300450000);
        map5.addSpawn(new SpawnPosition(494.0f, 1112.0f, 435.75f));
        map5.addSpawn(new SpawnPosition(507.0f, 1100.0f, 433.76f));
        map5.addSpawn(new SpawnPosition(531.0f, 1121.0f, 433.44f));
        map5.addSpawn(new SpawnPosition(507.0f, 1129.0f, 433.75f));
        map5.addSpawn(new SpawnPosition(472.0f, 1121.0f, 443.76f));
        map5.addSpawn(new SpawnPosition(466.0f, 1136.0f, 437.44f));
        map5.addSpawn(new SpawnPosition(507.0f, 1147.0f, 433.75f));
        map5.addSpawn(new SpawnPosition(535.0f, 1150.0f, 432.76f));
        map5.addSpawn(new SpawnPosition(494.0f, 1160.0f, 434.44f));
        map5.addSpawn(new SpawnPosition(521.0f, 1175.0f, 433.75f));
        map5.addSpawn(new SpawnPosition(488.0f, 1174.0f, 433.76f));
        map5.addSpawn(new SpawnPosition(471.0f, 1155.0f, 433.44f));
        map5.addStaticDoor(124);
        map5.addStaticDoor(118);
        map5.addStaticDoor(116);
        map5.addStaticDoor(126);
        map5.addStaticDoor(125);
        map5.addStaticDoor(110);
        map5.addStaticDoor(123);
        map5.addStaticDoor(115);
        map5.addStaticDoor(113);
        map5.addStaticDoor(109);
        map5.addStaticDoor(106);
        map5.addStaticDoor(107);
        map5.setKillZ(425f);

        BattlegroundMap map6 = new BattlegroundMap(300450000);
        map6.addSpawn(new SpawnPosition(1855.0f, 1132.0f, 218.75f));
        map6.addSpawn(new SpawnPosition(1830.0f, 1131.0f, 224.76f));
        map6.addSpawn(new SpawnPosition(1853.0f, 1144.0f, 232.44f));
        map6.addSpawn(new SpawnPosition(1853.0f, 1174.0f, 231.75f));
        map6.addSpawn(new SpawnPosition(1859.0f, 1198.0f, 230.76f));
        map6.addSpawn(new SpawnPosition(1832.0f, 1202.0f, 220.44f));
        map6.addSpawn(new SpawnPosition(1841.0f, 1165.0f, 218.75f));
        map6.addSpawn(new SpawnPosition(1819.0f, 1185.0f, 218.76f));
        map6.addSpawn(new SpawnPosition(1814.0f, 1178.0f, 218.44f));
        map6.addSpawn(new SpawnPosition(1812.0f, 1149.0f, 218.75f));
        map6.addSpawn(new SpawnPosition(1830.0f, 1149.0f, 218.76f));
        map6.addSpawn(new SpawnPosition(1849.0f, 1149.0f, 218.44f));
        map6.addStaticDoor(153);
        map6.addStaticDoor(162);
        map6.addStaticDoor(160);
        map6.addStaticDoor(163);
        map6.addStaticDoor(161);
        map6.addStaticDoor(158);
        map6.addStaticDoor(156);
        map6.addStaticDoor(154);
        map6.addStaticDoor(157);
        map6.addStaticDoor(165);
        map6.addStaticDoor(164);
        map6.addStaticDoor(152);
        map6.setKillZ(215f);

        BattlegroundMap map7 = new BattlegroundMap(301310000);
        map7.addSpawn(234f, 194f, 80f);
        map7.addSpawn(199f, 192f, 81f);
        map7.addSpawn(206f, 242f, 80f);
        map7.addSpawn(232f, 226f, 91f);
        map7.addSpawn(239f, 285f, 90f);
        map7.addSpawn(290f, 234f, 90f);
        map7.addSpawn(296f, 292f, 91f);
        map7.addSpawn(295f, 324f, 80f);
        map7.addSpawn(330f, 326f, 81f);
        map7.addSpawn(323f, 277f, 80f);
        map7.addSpawn(265f, 295f, 90f);
        map7.addSpawn(265f, 223f, 90f);
        map7.setKillZ(76f);

        super.maps.add(map1);
        // super.maps.add(map2);
        // super.maps.add(map3);
        super.maps.add(map4);
        super.maps.add(map5);
        super.maps.add(map6);
        super.maps.add(map7);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueSolo(players);

        if (super.getPlayers().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        openStaticDoors();

        List<SpawnPosition> spawns = new ArrayList<SpawnPosition>(getSpawnPositions());

        synchronized (super.getPlayers()) {
            for (Player pl : super.getPlayers()) {
                super.preparePlayer(pl, 25000);

                SpawnPosition pos = spawns.remove(Rnd.get(spawns.size()));
                if (pos != null)
                    performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                else
                    spawnPlayer(pl, false);
            }
        }

        super.specAnnounce("The match has begun!!!", 25000);

        startBattleground(25000, new Runnable() {
            @Override
            public void run() {
                if (getRemainingPlayers() <= 1)
                    endDeathmatch();
            }
        });

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endDeathmatch();
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public boolean createTournament(List<List<Player>> players) {
        if (!super.createPlayers(players))
            return false;

        this.matchLength = 405;
        startMatch();

        return true;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        if (player.getKillStreak() > 1) {
            super.announceAll(player.getName() + "'s killing streak has ended!");
        }

        player.setKillStreak(0); // Reset kill streak of killed player

        if (lastAttacker instanceof Player
            && ((Player) lastAttacker).getObjectId() != player.getObjectId()) {
            Player killer = (Player) lastAttacker;
            killer.setKillStreak(killer.getKillStreak() + 1);

            if (killer.getKillStreak() > 1) {
                super.announceAll(killer.getName() + " is on a killing streak of "
                    + killer.getKillStreak() + "!");
            }

            PvpService.getInstance().addMight(killer, killer.getKillStreak() + 1); // Reward 1 + <kills> Might

            killer.getLifeStats().increaseHp(TYPE.HP, 1000 + 200 * killer.getKillStreak());
            killer.getLifeStats().increaseMp(TYPE.MP, 1000 + 200 * killer.getKillStreak());
        }

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (player.getBattleground() != null
                    && player.getBattleground() instanceof DeathmatchBg) {
                    spawnPlayer(player, true);
                    spawnProtection(player);
                }
            }
        }, 6000);
    }

    public void onLeave(Player player, boolean isLogout, boolean isAfk) {
        super.onLeaveDefault(player, isLogout, isAfk);

        if (isStarted() && getRemainingPlayers() <= 1)
            endDeathmatch();
    }

    private void spawnPlayer(Player player, boolean isRespawn) {
        if (player.getLifeStats().isAlreadyDead())
            player.getReviveController().fullRevive();

        SpawnPosition spawnPos = getSpawnPositions().get(Rnd.get(getSpawnPositions().size()));
        if (spawnPos != null) {
            if (isRespawn)
                TeleportService.teleportTo(player, getMapId(), super.getInstanceId(),
                    spawnPos.getX(), spawnPos.getY(), spawnPos.getZ(), 0);
            else
                performTeleport(player, spawnPos.getX(), spawnPos.getY(), spawnPos.getZ());
        }
        else {
            log.error("spawnPos == null!");
        }
    }

    private void endDeathmatch() {
        super.onEndFirstDefault();

        Player winner = null;
        for (Player pl : super.getPlayers()) {
            if (winner == null || winner.getTotalKills() < pl.getTotalKills())
                winner = pl;
        }

        if (winner != null) {
            propagateWin(winner);

            logWinner(winner.getBgIndex() + 1);

            super.announceAll(winner.getName() + " has won the Deathmatch with "
                + winner.getTotalKills() + " kills in total!");

            for (Player pl : super.getPlayers()) {
                if (pl.getObjectId() == winner.getObjectId())
                    super.playerWinMatch(pl, super.K_VALUE * 2 / 3);
            }
        }

        for (Player pl : super.getPlayers()) {
            super.rewardPlayer(pl, 15, pl.getObjectId() == winner.getObjectId());

            super.scheduleAnnouncement(pl,
                "You have received some Might and Glory for your effort in this battleground!",
                3000);
        }

        super.propagateDone();

        super.onEndDefault();
    }
}