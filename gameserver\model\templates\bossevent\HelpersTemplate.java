package gameserver.model.templates.bossevent;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "helpergroup", propOrder = { "helper" })
public class HelpersTemplate {

    @XmlElement(name = "helper")
    protected List<HelperTemplate> helper;

    public List<HelperTemplate> getHelpers() {
        if (helper == null) {
            helper = new ArrayList<HelperTemplate>();
        }
        return this.helper;
    }

}
