/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.dataholders.DataManager;
import gameserver.model.drop.DropTemplate;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEffectType;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.RateModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.group.PlayerGroup;
import gameserver.model.items.PackageItem;
import gameserver.network.aion.serverpackets.SM_ITEM_PACKAGE;
import gameserver.network.aion.serverpackets.SM_ITEM_PACKAGE_CLOSE;
import gameserver.services.DropService;
import gameserver.services.ItemService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import javolution.util.FastTable;

/**
 * <AUTHOR>
 */
public class DefenseOfTheDrana extends Battleground {
    private boolean endCalled = false;

    private AtomicBoolean hardMode = new AtomicBoolean(false);

    private static final int INSTANCE_ID = 1003;

    private int waveNumber = 0;

    private SkillTemplate hardModeSkill = DataManager.SKILL_DATA.getSkillTemplate(19098);

    private SpawnPosition waveSpawn1 = new SpawnPosition(760.4f, 842.8f, 351.7f, 23, 0);
    private SpawnPosition waveSpawn2 = new SpawnPosition(889.4f, 927.5f, 348.9f, 58, 0);
    private SpawnPosition waveSpawn3 = new SpawnPosition(681.6f, 947.8f, 352.6f, 118, 0);

    private Collection<Npc> waveSpawns = new FastTable<Npc>().shared();
    private Collection<VisibleObject> hardModePuzzleSpawns = new FastTable<VisibleObject>().shared();

    private final static int INITIAL_DELAY = 20000;

    private final static int DEFENSE_RADIUS = 9;

    private final static int HEAT_ALERT = 10;
    private final static int HEAT_CRITICAL = 20;
    private final static int HEAT_LOSE = 30;

    private AtomicInteger npcsInDefenseArea = new AtomicInteger(0);
    private AtomicInteger heatMeter = new AtomicInteger(0);
    private AtomicInteger secondariesComplete = new AtomicInteger(0);

    public DefenseOfTheDrana() {
        super.name = "Defense of the Drana Facility";
        super.displayName = "Instance";
        super.minSize = 1;
        super.maxSize = 6;
        super.teamCount = 1;
        super.matchLength = INITIAL_DELAY / 1000 + 21 * 60;
        super.isAnonymous = false;
        super.afkKick = false;
        super.shouldDisband = false;
        super.isPvE = true;

        BattlegroundMap map2 = new BattlegroundMap(300250000);
        map2.addSpawn(new SpawnPosition(790, 941, 353));
        map2.addStaticDoor(70);
        map2.setKillZ(300f);

        super.maps.add(map2);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueGroup(players);

        if (super.getGroups().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        synchronized (super.getGroups()) {
            for (PlayerGroup group : super.getGroups()) {
                for (Player pl : group.getMembers()) {
                    super.preparePlayerInstance(pl, INITIAL_DELAY);

                    SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                    performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
                }
            }
        }

        createHardModePuzzle();

        startBattleground(INITIAL_DELAY, new Runnable() {
            @Override
            public void run() {
                if (getRemainingGroups(0) < 1)
                    endInstance(false);
            }
        });

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                destroyHardModePuzzle();
                createSecondaries();
            }
        }, INITIAL_DELAY + 2 * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                intro();

                setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                    private int extraCounter = 0;

                    @Override
                    public void run() {
                        extraCounter++;

                        // handleDefenseArea();

                        for (Npc npc : waveSpawns)
                            if (!npc.getLifeStats().isAlreadyDead())
                                npc.runTo(789.6f, 935.3f, 353.0f);

                        if ((extraCounter % 5) == 0) {
                            handleDefenseArea();
                            updateHeat();
                        }

                        if ((extraCounter % 60) == 0) {
                            extraCounter = 0;
                        }
                    }
                }, 1 * 1000, 1 * 1000));
            }
        }, INITIAL_DELAY);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endInstance(true);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    private void handleDefenseArea() {
        int npcs = 0;

        for (Npc npc : waveSpawns) {
            if (!npc.getLifeStats().isAlreadyDead()
                && MathUtil.isInRange(npc, 789.6f, 935.3f, DEFENSE_RADIUS)) {
                npcs++;
            }
        }

        npcsInDefenseArea.set(npcs);
    }

    private void updateHeat() {
        if (npcsInDefenseArea.get() == 0) {
            heatMeter.addAndGet(-5);

            if (heatMeter.get() < 0)
                heatMeter.set(0);
        }
        else {
            heatMeter.addAndGet(npcsInDefenseArea.get());
        }

        if (heatMeter.get() >= HEAT_LOSE) {
            super.announceAll("Drana Facility",
                "The Facility has been overrun! You have failed your mission.");
            endInstance(false);
        }
        else if (npcsInDefenseArea.get() > 0 && heatMeter.get() >= HEAT_CRITICAL) {
            super.announceAll("Drana Facility", "Clear the Facility immediately or it's all over!");
        }
        else if (npcsInDefenseArea.get() > 0 && heatMeter.get() >= HEAT_ALERT) {
            super.announceAll("Drana Facility", "The Facility is being overrun! Clear it quickly!");
        }
    }

    private void intro() {
        final Npc intro = spawnNpc(800966, 797.4f, 934.9f, 353f, 52, true);

        intro.shout("Welcome to the Drana Production Facility!", 500);
        intro.shout("Allow me to introduce you to this strange place.", 4500);

        intro.walkTo(802.5f, 873.2f, 348.9f, 1000);

        intro.shout("You must defend this platform before the doors to the laboratory.", 8500);
        intro.shout("You will be tested in every possible way - so hang in there!", 14000);
        intro.shout("Along the way, you should seek out in the vicinity", 19500);
        intro.shout("More challenges await those that seek the greatest of rewards!", 25000);
        intro.shout("Completing such a challenge will also bring back any dead players to life.",
            30500);
        intro.shout("Now... what do we have here?", 39000);
        intro.shout("It looks like a Jumping Puzzle!", 42000);

        intro.shout(
            "I bet, if you get to the top of this, you will have shown the Gods you fear nothing.",
            46000);
        intro.shout(
            "In return, I'm sure they will spice up your challenge for even greater rewards!",
            52000);
        intro.shout("Now I'll let you guys to it...", 58000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                intro.getController().onDelete();
                spawnWave();
            }
        }, 60000);
    }

    private void createHardModePuzzle() {
        hardModePuzzleSpawns.add(spawn(3110006, 798.902, 854.237, 350.345, 91, false));
        hardModePuzzleSpawns.add(spawn(3110006, 803.95, 851.201, 353.731, 1, false));
        hardModePuzzleSpawns.add(spawn(3110006, 808.494, 848.74, 355.534, 78, false));
        hardModePuzzleSpawns.add(spawn(3110006, 812.597, 845.958, 357.371, 84, false));
        hardModePuzzleSpawns.add(spawn(3110006, 818.068, 847.86, 358.371, 99, false));
        hardModePuzzleSpawns.add(spawn(3200005, 822.742, 849.954, 358.959, 7, false));

        Npc npc = spawnNpc(205506, 823.2f, 850.1f, 358.96f, 67, false, false);
        npc.setCustomTag("Hard Mode");
        hardModePuzzleSpawns.add(npc);
    }

    private void createSecondaries() {
        // Secondary 1 - Puzzle
        /*
         * spawn(3190027, 2992f, 1695.59f, 202.126f, 71, false); spawn(3190027, 2988.48f, 1693.14f, 203.955f, 38,
         * false); spawn(3190027, 2984.61f, 1691.17f, 205.87f, 39, false); spawn(3190027, 2980.76f, 1689.76f, 207.564f,
         * 41, false); spawn(3190027, 2977.7f, 1686.96f, 209.408f, 45, false); spawn(3190027, 2978.69f, 1682.92f,
         * 211.15f, 67, false); spawn(3141000, 2985.99f, 1681.51f, 213.646f, 3, false); spawn(3141000, 2992.73f,
         * 1684.91f, 215.048f, 75, false); spawn(3141000, 2991.68f, 1689.3f, 216.39f, 5, false); spawn(3141000,
         * 2989.66f, 1693.32f, 217.431f, 10, false); spawn(3141000, 2985.14f, 1695.59f, 218.423f, 89, false);
         * spawn(3200005, 2989.89f, 1703.45f, 217.924f, 59, false); spawn(3200005, 2993.97f, 1708.69f, 218.288f, 108,
         * false); spawn(3200005, 3001.16f, 1705.73f, 217.898f, 54, false); spawn(3200005, 3005.01f, 1702.03f, 219.255f,
         * 72, false); spawn(3200005, 3005.35f, 1697.49f, 220.786f, 51, false); spawn(3200005, 3006.26f, 1693.42f,
         * 221.66f, 68, false); spawn(3420010, 3006.63f, 1685.62f, 221.602f, 63, false); Npc secondary1 =
         * spawnNpc(205507, 3006.7f, 1684.9f, 222.44f, 32, false, false); secondary1.setCustomTag("Secondary 1");
         */

        // Secondary 2 - Hellpath Guardian Fireeye
        Npc secondary2 = spawnNpc(217469, 789.4f, 1113.7f, 363.2f, 89, false);
        secondary2.setCustomTag("Secondary 1");

        // Secondary 3 - Puzzle
        /*
         * spawn(3160008, 2868.56f, 2081.73f, 229.25f, 10, false); spawn(3160008, 2876.55f, 2085.18f, 229.739f, 114,
         * false); spawn(3160008, 2884.65f, 2079.47f, 230.109f, 100, false); spawn(3420008, 2867.88f, 2066.1f, 220.29f,
         * 17, false); spawn(3420008, 2871.32f, 2070.46f, 221.796f, 16, false); spawn(3420008, 2876.04f, 2065.94f,
         * 223.008f, 15, false); spawn(3420008, 2880.13f, 2070.68f, 224.148f, 16, false); spawn(3420008, 2876.44f,
         * 2075.71f, 225.311f, 16, false); spawn(3420008, 2872.66f, 2071.65f, 227.07f, 13, false); spawn(3420008,
         * 2867.74f, 2074.79f, 228.787f, 12, false); spawn(3420010, 2902.33f, 2089.29f, 233.906f, 107, false);
         * spawn(3420046, 2888.07f, 2073.35f, 231.421f, 37, false); spawn(3420046, 2891.36f, 2072.62f, 232.929f, 77,
         * false); spawn(3420046, 2895.01f, 2073.04f, 233.3f, 91, false); spawn(3420046, 2899.33f, 2075.3f, 231.975f,
         * 50, false); spawn(3420046, 2907.81f, 2077.69f, 231.899f, 65, false); spawn(3420046, 2907.98f, 2084.28f,
         * 233.171f, 88, false); Npc secondary3 = spawnNpc(205508, 2901.6f, 2089.9f, 234.75f, 107, false, false);
         * secondary3.setCustomTag("Secondary 3");
         */

        // Secondary 4 - Snowfur
        Npc secondary4 = spawnNpc(213706, 957.3f, 865.7f, 330.1f, 57, false);
        secondary4.setCustomTag("Secondary 2");

        // Secondary 5 - Spectral Elim Elder
        Npc secondary5 = spawnNpc(215387, 1001.6f, 975.7f, 324.0f, 89, false);
        secondary5.setCustomTag("Secondary 3");

        // Secondary 6 - Puzzle
        spawn(3420008, 914.539, 957.125, 346.542, 118, false);
        spawn(3420008, 920.942, 956.585, 346.964, 118, false);
        spawn(3420008, 928.608, 955.735, 347.128, 117, false);
        spawn(3420008, 930.763, 959.59, 347.708, 27, false);
        spawn(3420008, 931.345, 962.853, 348.811, 29, false);
        spawn(3190027, 932.193, 965.866, 349.795, 118, false);
        spawn(3190027, 934.167, 967.187, 351.441, 10, false);
        spawn(3190027, 936.994, 963.178, 352.52, 104, false);
        spawn(3141000, 941.819, 959.753, 352.097, 106, false);
        spawn(3141000, 943.734, 954.001, 353.203, 25, false);
        spawn(3141000, 942.185, 948.584, 353.941, 31, false);
        spawn(3420010, 937.887, 943.377, 353.417, 25, false);

        Npc secondary6 = spawnNpc(205509, 937.7f, 942.5f, 354.26f, 24, false, false);
        secondary6.setCustomTag("Secondary 4");
    }

    @Override
    public boolean onTalk(Npc npc, Player player) {
        if (player.getZ() - npc.getZ() < -1f)
            return true;
        
        switch (npc.getNpcId()) {
            case 205506: // Hard Mode Shugo
                npc.shout("Nyerk!");
                enableHardMode();
                return true;
            case 205507: // Secondary 1 Shugo
                npc.shout("Kekekekeke!");
                npc.getController().onDelete();
                completeSecondary();
                return true;
            case 205508: // Secondar 3 Shugo
                npc.shout("NYYYEERK!");
                npc.getController().onDelete();
                completeSecondary();
                return true;
            case 205509: // Secondary 6 Shugo
                npc.shout("KEKEKEKEKEKE!");
                npc.getController().onDelete();
                completeSecondary();
                return true;
        }

        return false;
    }

    private void completeSecondary() {
        super.announceAll("Drana Facility", "You have completed a secondary objective!");
        secondariesComplete.incrementAndGet();
        resurrectEveryone();

        int might = hardMode.get() ? 50 : 25;
        int dp = hardMode.get() ? 2000 : 500;

        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                PvpService.getInstance().addMight(pl, might);
                pl.getCommonData().addDp(dp);
            }
        }
    }

    @Override
    public void onKill(Npc npc, Creature lastAttacker) {
        if (!(lastAttacker.getMaster() instanceof Player))
            return;

        Player killer = (Player) lastAttacker.getMaster();

        switch (npc.getNpcId()) {
            case 215387: // Spectral Elim Elder
            case 213706: // Snowfur
            case 217469: // Hellpath Guardian Fireeye
                completeSecondary();
                break;
            case 213738: // Aarien the Seacaller
            case 217423: // Raksang Keymaster
            case 217425: // Illusionmaster Sharik
            case 212315: // Ulan
            case 217765: // Prison Strategist Humat
            case 216176: // Protector Dinata
                resurrectEveryone();

                int dropRate = hardMode.get() ? 20 : 15;

                List<DropTemplate> drops = new ArrayList<DropTemplate>();

                drops.add(new DropTemplate(160002419, 1, 4, dropRate));
                drops.add(new DropTemplate(160002491, 1, 4, dropRate));
                drops.add(new DropTemplate(160002418, 1, 4, dropRate));
                drops.add(new DropTemplate(160002417, 1, 4, dropRate));
                drops.add(new DropTemplate(160001432, 1, 4, dropRate));
                drops.add(new DropTemplate(160001431, 1, 4, dropRate));
                drops.add(new DropTemplate(160001430, 1, 4, dropRate));
                drops.add(new DropTemplate(160001429, 1, 4, dropRate));
                drops.add(new DropTemplate(160002420, 1, 4, dropRate));
                drops.add(new DropTemplate(160002421, 1, 4, dropRate));
                drops.add(new DropTemplate(160002422, 1, 4, dropRate));
                drops.add(new DropTemplate(160002423, 1, 4, dropRate));
                drops.add(new DropTemplate(160002416, 1, 4, dropRate));
                drops.add(new DropTemplate(160002426, 1, 4, dropRate));
                drops.add(new DropTemplate(160002427, 1, 4, dropRate));
                drops.add(new DropTemplate(160002348, 1, 4, dropRate));
                drops.add(new DropTemplate(164000053, 2, 6, dropRate));
                drops.add(new DropTemplate(164000061, 2, 6, dropRate));
                drops.add(new DropTemplate(164000049, 2, 6, dropRate));
                drops.add(new DropTemplate(164000057, 2, 6, dropRate));
                drops.add(new DropTemplate(164000124, 2, 6, dropRate));

                if (hardMode.get()) {
                    drops.add(new DropTemplate(160002506, 1, 2, dropRate));
                    drops.add(new DropTemplate(160002505, 1, 2, dropRate));
                    drops.add(new DropTemplate(160002504, 1, 2, dropRate));
                    drops.add(new DropTemplate(160002431, 1, 2, dropRate));
                    drops.add(new DropTemplate(160002432, 1, 2, dropRate));
                    drops.add(new DropTemplate(160002433, 1, 2, dropRate));
                    drops.add(new DropTemplate(160002436, 1, 2, dropRate));
                    drops.add(new DropTemplate(160001338, 1, 2, dropRate));
                    drops.add(new DropTemplate(160001341, 1, 2, dropRate));
                    drops.add(new DropTemplate(160002337, 1, 2, dropRate));
                }

                registerDrops(npc, drops.toArray(new DropTemplate[drops.size()]));

                super.announceAll("Drana Facility", "You have slain a powerful creature!");
                break;
            default:
                // super.message(killer, "You have slain a creature!");
                break;
        }

        int might = hardMode.get() ? 3 : 1;

        for (Player pl : killer.getPlayerGroup().getMembers())
            PvpService.getInstance().addMight(pl, might);

        if (waveSpawns.contains(npc))
            waveSpawns.remove(npc);

        if (!DropService.getInstance().hasDrops(npc)) {
            npc.getController().onDelete();
        }
    }

    private void registerDrops(Npc npc, DropTemplate... drops) {
        DropService.getInstance().registerDrop(npc, Arrays.asList(drops), true);
    }

    private void spawnWave() {
        if (super.isDone() || endCalled)
            return;

        waveNumber++;

        switch (waveNumber) {
            case 1:
                waveSpawns.add(spawnNpc(230508, waveSpawn1, true));

                waveSpawns.add(spawnNpc(211185, waveSpawn1, true));
                waveSpawns.add(spawnNpc(211185, waveSpawn2, true));
                waveSpawns.add(spawnNpc(211185, waveSpawn3, true));

                waveSpawns.add(spawnNpc(213837, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn3, true));

                waveSpawns.add(spawnNpc(213837, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn3, true));
                break;
            case 2:
                waveSpawns.add(spawnNpc(216176, waveSpawn1, true)); // Protector Dinata

                waveSpawns.add(spawnNpc(254542, waveSpawn1, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn2, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn3, true));

                waveSpawns.add(spawnNpc(284663, waveSpawn1, true));
                waveSpawns.add(spawnNpc(284663, waveSpawn2, true));
                waveSpawns.add(spawnNpc(284663, waveSpawn3, true));
                break;
            case 3:
                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));

                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));

                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));
                break;
            case 4:
                waveSpawns.add(spawnNpc(217765, waveSpawn1, true)); // Prison Strategist Humat

                waveSpawns.add(spawnNpc(231060, waveSpawn1, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn2, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn3, true));

                waveSpawns.add(spawnNpc(231060, waveSpawn1, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn2, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn3, true));
                break;
            case 5:
                waveSpawns.add(spawnNpc(230508, waveSpawn1, true));

                waveSpawns.add(spawnNpc(211185, waveSpawn1, true));
                waveSpawns.add(spawnNpc(211185, waveSpawn2, true));
                waveSpawns.add(spawnNpc(211185, waveSpawn3, true));

                waveSpawns.add(spawnNpc(213837, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn3, true));

                waveSpawns.add(spawnNpc(213837, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn3, true));
                break;
            case 6:
                waveSpawns.add(spawnNpc(216946, waveSpawn1, true));
                waveSpawns.add(spawnNpc(216946, waveSpawn2, true));
                waveSpawns.add(spawnNpc(216946, waveSpawn3, true));

                waveSpawns.add(spawnNpc(281896, waveSpawn1, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn2, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn3, true));

                waveSpawns.add(spawnNpc(281896, waveSpawn1, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn2, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn3, true));

                waveSpawns.add(spawnNpc(281896, waveSpawn1, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn2, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn3, true));
                break;
            case 7:
                waveSpawns.add(spawnNpc(212315, waveSpawn1, true)); // Ulan

                waveSpawns.add(spawnNpc(213121, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213121, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213121, waveSpawn3, true));

                waveSpawns.add(spawnNpc(213121, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213121, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213121, waveSpawn3, true));
                break;
            case 8:
                waveSpawns.add(spawnNpc(230508, waveSpawn1, true));

                waveSpawns.add(spawnNpc(211185, waveSpawn1, true));
                waveSpawns.add(spawnNpc(211185, waveSpawn2, true));
                waveSpawns.add(spawnNpc(211185, waveSpawn3, true));

                waveSpawns.add(spawnNpc(213837, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn3, true));

                waveSpawns.add(spawnNpc(213837, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn3, true));
                break;
            case 9:
                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));

                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));

                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));
                break;
            case 10:
                waveSpawns.add(spawnNpc(217425, waveSpawn1, true)); // Illusionmaster Sharik

                waveSpawns.add(spawnNpc(254542, waveSpawn1, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn2, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn3, true));

                waveSpawns.add(spawnNpc(284663, waveSpawn1, true));
                waveSpawns.add(spawnNpc(284663, waveSpawn2, true));
                waveSpawns.add(spawnNpc(284663, waveSpawn3, true));
                break;
            case 11:
                waveSpawns.add(spawnNpc(216946, waveSpawn1, true));
                waveSpawns.add(spawnNpc(216946, waveSpawn2, true));
                waveSpawns.add(spawnNpc(216946, waveSpawn3, true));

                waveSpawns.add(spawnNpc(281896, waveSpawn1, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn2, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn3, true));

                waveSpawns.add(spawnNpc(281896, waveSpawn1, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn2, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn3, true));

                waveSpawns.add(spawnNpc(281896, waveSpawn1, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn2, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn3, true));
                break;
            case 12:
                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));

                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));

                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));
                break;
            case 13:
                waveSpawns.add(spawnNpc(217423, waveSpawn1, true)); // Raksang Keymaster

                waveSpawns.add(spawnNpc(231060, waveSpawn1, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn2, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn3, true));

                waveSpawns.add(spawnNpc(231060, waveSpawn1, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn2, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn3, true));
                break;
            case 14:
                waveSpawns.add(spawnNpc(284663, waveSpawn1, true));
                waveSpawns.add(spawnNpc(284663, waveSpawn2, true));
                waveSpawns.add(spawnNpc(284663, waveSpawn3, true));

                waveSpawns.add(spawnNpc(230508, waveSpawn1, true));
                waveSpawns.add(spawnNpc(230508, waveSpawn2, true));
                waveSpawns.add(spawnNpc(230508, waveSpawn3, true));

                waveSpawns.add(spawnNpc(254542, waveSpawn1, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn2, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn3, true));
                break;
            case 15:
                waveSpawns.add(spawnNpc(216946, waveSpawn1, true));
                waveSpawns.add(spawnNpc(216946, waveSpawn2, true));
                waveSpawns.add(spawnNpc(216946, waveSpawn3, true));

                waveSpawns.add(spawnNpc(281896, waveSpawn1, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn2, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn3, true));

                waveSpawns.add(spawnNpc(281896, waveSpawn1, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn2, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn3, true));

                waveSpawns.add(spawnNpc(281896, waveSpawn1, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn2, true));
                waveSpawns.add(spawnNpc(281896, waveSpawn3, true));
                break;
            case 16:
                waveSpawns.add(spawnNpc(213738, waveSpawn1, true)); // Aarien the Seacaller

                waveSpawns.add(spawnNpc(231060, waveSpawn1, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn2, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn3, true));

                waveSpawns.add(spawnNpc(231060, waveSpawn1, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn2, true));
                waveSpawns.add(spawnNpc(218639, waveSpawn3, true));
                break;
            case 17:
                waveSpawns.add(spawnNpc(230508, waveSpawn1, true));

                waveSpawns.add(spawnNpc(211185, waveSpawn1, true));
                waveSpawns.add(spawnNpc(211185, waveSpawn2, true));
                waveSpawns.add(spawnNpc(211185, waveSpawn3, true));

                waveSpawns.add(spawnNpc(213837, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn3, true));

                waveSpawns.add(spawnNpc(213837, waveSpawn1, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn2, true));
                waveSpawns.add(spawnNpc(213837, waveSpawn3, true));
                break;
            case 18:
                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));

                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));

                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));

                waveSpawns.add(spawnNpc(214863, waveSpawn1, true));
                waveSpawns.add(spawnNpc(214862, waveSpawn2, true));
                waveSpawns.add(spawnNpc(214861, waveSpawn3, true));
                break;
            case 19:
                waveSpawns.add(spawnNpc(254542, waveSpawn1, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn2, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn3, true));

                waveSpawns.add(spawnNpc(284663, waveSpawn1, true));
                waveSpawns.add(spawnNpc(284663, waveSpawn2, true));
                waveSpawns.add(spawnNpc(284663, waveSpawn3, true));

                waveSpawns.add(spawnNpc(254542, waveSpawn1, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn2, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn3, true));

                waveSpawns.add(spawnNpc(254542, waveSpawn1, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn2, true));
                waveSpawns.add(spawnNpc(254542, waveSpawn3, true));
                break;
            case 20:
                waveSpawns.add(spawnNpc(216176, waveSpawn1, true)); // Protector Dinata
                waveSpawns.add(spawnNpc(213738, waveSpawn2, true)); // Aarien the Seacaller
                waveSpawns.add(spawnNpc(217423, waveSpawn3, true)); // Raksang Keymaster
                waveSpawns.add(spawnNpc(217425, waveSpawn1, true)); // Illusionmaster Sharik
                waveSpawns.add(spawnNpc(212315, waveSpawn2, true)); // Ulan
                waveSpawns.add(spawnNpc(217765, waveSpawn3, true)); // Prison Strategist Humat

                waveNumber = 19;
                return;
        }

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                spawnWave();
            }
        }, 60 * 1000);
    }

    private void enableHardMode() {
        if (!hardMode.compareAndSet(false, true))
            return;

        super.special = true;

        destroyHardModePuzzle();

        super.announceAll("Drana Facility", "Hard Mode has been triggered! Buckle up!");

        getInstance().doOnAllNpcs(new Executor<Npc>() {
            @Override
            public boolean run(Npc npc) {
                hardMode(npc);
                return true;
            }
        });
    }

    private void destroyHardModePuzzle() {
        for (VisibleObject vo : hardModePuzzleSpawns) {
            if (vo instanceof Npc)
                ((Npc) vo).getController().onDelete();
            else
                vo.getController().delete();
        }
    }

    private void hardMode(Npc npc) {
        if (hardMode.get()) {
            TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

            mods.add(RateModifier.newInstance(StatEnum.MAXHP, 30, true));
            mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 75, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 750, true));
            mods.add(AddModifier.newInstance(StatEnum.MAIN_HAND_ACCURACY, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.MAGICAL_ACCURACY, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.MAGICAL_RESIST, 500, true));
            mods.add(AddModifier.newInstance(StatEnum.ABNORMAL_RESISTANCE_ALL, 300, true));
            mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL_RESIST, 1000, true));

            switch (npc.getNpcId()) {
                case 215387: // Spectral Elim Elder
                    mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 50, true));
                    mods.add(RateModifier.newInstance(StatEnum.MAXHP, 50, true));
                    mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 250, true));
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 1000, true));
                    break;
                case 213706: // Snowfur
                    mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 50, true));
                    mods.add(RateModifier.newInstance(StatEnum.MAXHP, 50, true));
                    mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 250, true));
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 1000, true));
                    break;
                case 217469: // Hellpath Guardian Fireeye
                    mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 50, true));
                    mods.add(RateModifier.newInstance(StatEnum.MAXHP, 50, true));
                    mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 250, true));
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 1000, true));
                    break;
            }

            npc.getGameStats().endEffect(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT));
            npc.getGameStats().addModifiers(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT), mods);

            Effect effect = new Effect(npc, npc, hardModeSkill, 1, 60 * 60 * 1000);
            npc.getEffectController().addEffect(effect);
            effect.addAllEffectToSucess();
            effect.startEffect(true);
        }
        else {
            TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

            mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 15, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 200, true));
            mods.add(AddModifier.newInstance(StatEnum.ABNORMAL_RESISTANCE_ALL, 100, true));
            mods.add(RateModifier.newInstance(StatEnum.MAXHP, -20, true));

            switch (npc.getNpcId()) {
                case 215387: // Spectral Elim Elder
                    mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 40, true));
                    mods.add(RateModifier.newInstance(StatEnum.MAXHP, 25, true));
                    mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 600, true));
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 1000, true));
                    break;
                case 213706: // Snowfur
                    mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 40, true));
                    mods.add(RateModifier.newInstance(StatEnum.MAXHP, 25, true));
                    mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 400, true));
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 1000, true));
                    break;
                case 217469: // Hellpath Guardian Fireeye
                    mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 40, true));
                    mods.add(RateModifier.newInstance(StatEnum.MAXHP, 25, true));
                    mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 400, true));
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 1000, true));
                    break;
            }

            npc.getGameStats().endEffect(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT));
            npc.getGameStats().addModifiers(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT), mods);
        }
    }

    private Npc spawnNpc(int npcId, SpawnPosition pos, boolean noHome) {
        return spawnNpc(npcId, pos.getX(), pos.getY(), pos.getZ(), pos.getHeading(), true, noHome);
    }

    private Npc spawnNpc(int npcId, float x, float y, float z, int heading, boolean noHome) {
        return spawnNpc(npcId, x, y, z, heading, true, noHome);
    }

    private Npc spawnNpc(int npcId, float x, float y, float z, int heading, boolean geo,
        boolean noHome) {
        Npc npc = (Npc) spawn(npcId, x, y, z, heading, geo);
        npc.setNoHome(noHome);

        hardMode(npc);

        return npc;
    }

    private void resurrectEveryone() {
        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                if (!pl.getLifeStats().isAlreadyDead())
                    continue;

                SpawnPosition pos = getSpawnPositions().get(group.getBgIndex());
                TeleportService.teleportTo(pl, super.getMapId(), super.getInstanceId(), pos.getX(),
                    pos.getY(), pos.getZ(), 0);

                super.announce(pl, "Drana Facility", "You have resurrected!");
            }
        }
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        super
            .announce(player, "Drana Facility",
                "You have died! Your allies must complete a secondary objective or slay a boss to resurrect you");
    }

    public void onLeave(final Player player, boolean isLogout, boolean isAfk) {
        if (!player.isSpectating())
            log.info("[DEBUG] Drana Facility: " + player.getName() + " disconnected from instance "
                + super.getInstanceId() + " with " + super.getSecondsLeft() + " seconds left.");

        super.onLeaveDefault(player, true, isAfk);

        if (isStarted() && getRemainingGroups(0) < 1)
            endInstance(false);
    }

    public void onReconnect(Player player) {
        log.info("[DEBUG] Drana Facility: " + player.getName() + " reconnected to instance "
            + super.getInstanceId() + " with " + super.getSecondsLeft() + " seconds left.");
    }

    private void endInstance(boolean timer) {
        if (endCalled)
            return;

        endCalled = true;

        super.onEndFirstDefault();

        super.deleteNpcs();

        if (!timer) {
            super.onEndDefault();
            return;
        }

        String names = "";
        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                if (!names.isEmpty())
                    names += ", ";

                names += pl.getName();
            }
        }

        log.info("[DEBUG] Drana Facility: " + names + " finished instance " + super.getInstanceId()
            + ". Secondaries: " + secondariesComplete.get() + ", Hard Mode: "
            + (hardMode.get() ? "Yes" : "No"));

        super.announceAll("Drana Facility", "You have completed your mission! The Temple is safe!");

        super.announceAll("Drana Facility", "Secondaries: " + secondariesComplete.get()
            + ", Hard Mode: " + (hardMode.get() ? "Yes" : "No"), 5000);

        for (PlayerGroup group : super.getGroups()) {
            for (Player pl : group.getMembers()) {
                if (pl.getLifeStats().isAlreadyDead())
                    pl.getReviveController().fullRevive();

                super.createTimer(pl, 30);
            }
        }

        for (Player pl : super.getSpectators())
            super.createTimer(pl, 30);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                for (PlayerGroup group : getGroups()) {
                    for (Player pl : group.getMembers()) {
                        List<PackageItem> options = new ArrayList<PackageItem>();

                        switch (secondariesComplete.get()) {
                            case 4:
                                options.add(new PackageItem(166030005, hardMode.get() ? 25 : 7));
                                break;
                            case 3:
                                options.add(new PackageItem(166030005, hardMode.get() ? 15 : 5));
                                break;
                            case 2:
                                options.add(new PackageItem(166030005, hardMode.get() ? 10 : 3));
                            case 1:
                                break;
                        }

                        if (options.isEmpty()) {
                            scheduleAnnouncement(pl, "Drana Facility",
                                "You didn't complete enough secondaries to get a bonus reward.",
                                5000);
                            continue;
                        }

                        pl.setCurrentPackage(options);
                        PacketSendUtility.sendPacket(pl, new SM_ITEM_PACKAGE(-1, options));
                    }
                }
            }
        }, 5 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                for (PlayerGroup group : getGroups()) {
                    for (Player pl : group.getMembers()) {
                        if (pl.getCurrentPackage() != null && !pl.getCurrentPackage().isEmpty()) {
                            PackageItem option = pl.getCurrentPackage().get(0);

                            pl.setCurrentPackage(null);

                            PacketSendUtility.sendPacket(pl, new SM_ITEM_PACKAGE_CLOSE(-1));

                            ItemService.addItem(pl, option.getItemId(), option.getQuantity());
                        }
                    }
                }

                onEndDefault();
            }
        }, 30 * 1000);
    }
}