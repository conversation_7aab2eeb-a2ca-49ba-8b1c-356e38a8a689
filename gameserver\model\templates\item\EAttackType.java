package gameserver.model.templates.item;

import gameserver.model.SkillElement;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlType(name = "attack_type")
@XmlEnum
public enum EAttackType {
    PHYSICAL(false),
    MAGICAL_FIRE(true),
    MAGICAL_WATER(true),
    MAGICAL_WIND(true),
    MAGICAL_EARTH(true);

    private boolean magic;

    private EAttackType(boolean magic) {
        this.magic = magic;
    }

    /**
     * @return Returns the magic.
     */
    public boolean isMagical() {
        return magic;
    }

    public SkillElement getElement() {
        switch (this) {
            case MAGICAL_WATER:
                return SkillElement.WATER;
            case MAGICAL_FIRE:
                return SkillElement.FIRE;
            case MAGICAL_WIND:
                return SkillElement.WIND;
            case MAGICAL_EARTH:
                return SkillElement.EARTH;
        }
        return SkillElement.NONE;
    }
}