/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.alliance;

/**
 * <AUTHOR>
 */
public enum PlayerAllianceEvent {
    LEAVE(0),
    LEAVE_TIMEOUT(0),
    BANNED(0),

    MOVEMENT(1),
    MENTOR(1),

    DISCONNECTED(3),

    MEMBER_GROUP_CHANGE(5),
    ENTER(5),

    // Similar to 0, 1, 3 -- only the initial information block.
    UNK(9),

    RECONNECT(13),
    UPDATE(13),

    // Extra? Unused?
    APPOINT_VICE_CAPTAIN(13),
    DEMOTE_VICE_CAPTAIN(13),
    APPOINT_CAPTAIN(13),
    SYSTEM_CAPTAIN(13);

    private int id;

    private PlayerAllianceEvent(int id) {
        this.id = id;
    }

    public int getId() {
        return this.id;
    }
}
