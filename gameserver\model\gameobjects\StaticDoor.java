/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects;

import gameserver.controllers.StaticObjectController;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.EmotionType;
import gameserver.model.templates.StaticDoorTemplate;
import gameserver.model.templates.StaticDoorTemplate.DoorState;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.utils.PacketSendUtility;

/**
 * <AUTHOR>
 * 
 */
public class StaticDoor extends StaticObject {
    private int state = 0;

    /**
     * @param objectId
     * @param controller
     * @param spawnTemplate
     * @param objectTemplate
     */
    public StaticDoor(int objectId, StaticObjectController controller, SpawnTemplate spawnTemplate,
        StaticDoorTemplate objectTemplate) {
        super(objectId, controller, spawnTemplate, objectTemplate);

        this.state = objectTemplate.getState();
        
        //if (DoorState.isSet(DoorState.CLOSEABLE, this.state))
        //    this.state = DoorState.setState(DoorState.OPEN, this.state);
    }

    public boolean isOpen() {
        return DoorState.isSet(DoorState.OPEN, this.state);
    }

    public int getState() {
        return state;
    }

    public void setOpen(boolean open) {
        if (open) {
            state = DoorState.unsetState(DoorState.CLICKABLE, state);
            state = DoorState.setState(DoorState.OPEN, state);

            PacketSendUtility.broadcastPacket(this, new SM_EMOTION(getDoorId(),
                EmotionType.OPEN_DOOR, state));
        }
        else {
            if (DoorState.isSet(DoorState.CLICKABLE, getObjectTemplate().getState()))
                state = DoorState.setState(DoorState.CLICKABLE, state);

            state = DoorState.unsetState(DoorState.OPEN, state);

            PacketSendUtility.broadcastPacket(this, new SM_EMOTION(getDoorId(),
                EmotionType.CLOSE_DOOR, state));
        }
        
        GeoEngine2.getInstance().setDoorOpen(getWorldId(), getInstanceId(), getDoorId(), open);
    }
    
    public int getDoorId() {
        return getObjectTemplate().getId();
    }

    @Override
    public StaticDoorTemplate getObjectTemplate() {
        return (StaticDoorTemplate) super.getObjectTemplate();
    }
}
