/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.slipperyslope;

import gameserver.ai.state.AIState;
import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.utils.MathUtil;
import gameserver.world.World;

/**
 * <AUTHOR>
 */
public class GradarimController extends BossController {
    private final BossSkill DAMAGE = new BossSkill(18724, 1);

    private int shoutTicker = 0;

    public GradarimController() {
        super(215796, true);
    }

    @Override
    protected void think() {
        Npc owner = getOwner();
        SpawnTemplate spawn = owner.getSpawn();

        if (MathUtil.getDistance(owner, spawn.getX(), spawn.getY(), spawn.getZ()) > 45) {
            owner.getAi().clearDesires();
            owner.getAggroList().clearHate();

            owner.getMoveController().stop();
            World.getInstance().updatePosition(owner, spawn.getX(), spawn.getY(), spawn.getZ(),
                spawn.getHeading(), true);
            owner.getController().stopMoving();

            owner.setInCombat(false);
            owner.getLifeStats().increaseHp(TYPE.NATURAL_HP, owner.getLifeStats().getMaxHp() / 6);

            owner.getAi().setAiState(AIState.ACTIVE);

            return;
        }
        
        int hp = owner.getLifeStats().getHpPercentage();

        switch (shoutTicker) {
            case 0:
                if (hp <= 99) {
                    shoutTicker++;
                    owner.shout("Taste my BREATH!");
                }
                break;
            case 1:
                if (hp <= 50) {
                    shoutTicker++;
                    owner.shout("You are no match to me.");
                }
                break;
        }

        Player priority = getPriorityTarget();
        if (priority == null || !MathUtil.isIn3dRange(owner, priority, 20))
            return;

        getOwner().getAggroList().addHate(priority, 10000);

        if (DAMAGE.timeSinceUse() > 30)
            queueSkill(DAMAGE, owner);
    }
}