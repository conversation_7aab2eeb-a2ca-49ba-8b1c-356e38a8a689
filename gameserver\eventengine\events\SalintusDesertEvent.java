/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 *
 */
public class SalintusDesertEvent extends MobEvent {
    public SalintusDesertEvent() {
        super.mapId = 220020000;
        super.center = new SpawnPosition(1034, 1393, 242);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("An event at the Big Arc at Salintus Desert in Morheim will commence in 2 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at the Big Arc in Salintus Desert starts in 1 minute", 1 * 60 * 1000);
        announceAll("The event at the Big Arc in Salintus Desert starts in 30 seconds", 30 * 1000 + 1
            * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 2 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters at the Big Arc in Salintus Desert in the next 2 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 2 * 60));
        }

        super.scheduleEnd(2 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(217937, 953, 1427, 251);
        spawnMob(217937, 966, 1416, 253);
        spawnMob(217937, 955, 1401, 252);
        spawnMob(217937, 968, 1381, 249);
        spawnMob(217937, 970, 1432, 252);
        spawnMob(217937, 969, 1451, 248);
        spawnMob(217937, 998, 1442, 248);
        spawnMob(217937, 1011, 1451, 245);
        spawnMob(217937, 1034, 1424, 245);
        spawnMob(217937, 1039, 1418, 244);
        spawnMob(217937, 1027, 1395, 245);
        spawnMob(217937, 1020, 1377, 243);
        spawnMob(217937, 1015, 1373, 243);
        spawnMob(217937, 1039, 1364, 245);
        spawnMob(217937, 1038, 1371, 244);
        spawnMob(217937, 1045, 1389, 244);
        spawnMob(217937, 1057, 1403, 245);
        spawnMob(217937, 1063, 1402, 245);
        spawnMob(217937, 1104, 1393, 247);
        spawnMob(217937, 1117, 1390, 248);
        spawnMob(217937, 1113, 1367, 249);
        spawnMob(217937, 1119, 1351, 251);
        spawnMob(217937, 1103, 1344, 251);
        spawnMob(217937, 1098, 1322, 251);
        spawnMob(217937, 1071, 1316, 256);
        spawnMob(217937, 1055, 1326, 256);
        spawnMob(217937, 1050, 1312, 258);
        spawnMob(217937, 1036, 1317, 255);
    }
}
