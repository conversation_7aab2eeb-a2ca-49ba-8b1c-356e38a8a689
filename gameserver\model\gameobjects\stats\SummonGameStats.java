/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.stats;

import gameserver.model.EmotionType;
import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.id.FusionStatEffectId;
import gameserver.model.gameobjects.stats.id.ItemStatEffectId;
import gameserver.model.gameobjects.stats.id.StatEffectId;
import gameserver.model.gameobjects.stats.id.StoneStatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.items.ItemSlot;
import gameserver.model.templates.item.EAttackType;
import gameserver.model.templates.stats.SummonStatsTemplate;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_SUMMON_UPDATE;
import gameserver.utils.PacketSendUtility;

import java.util.Arrays;
import java.util.Map.Entry;
import java.util.TreeSet;

/**
 * <AUTHOR>
 */
public class SummonGameStats extends CreatureGameStats<Summon> {

    /**
     * @param owner
     * @param statsTemplate
     */
    public SummonGameStats(Summon owner, SummonStatsTemplate statsTemplate) {
        super(owner);
        initStat(StatEnum.MAXHP, statsTemplate.getMaxHp());
        initStat(StatEnum.MAXMP, statsTemplate.getMaxMp());
        initStat(StatEnum.MAIN_MIN_DAMAGES, statsTemplate.getMainHandAttack());
        initStat(StatEnum.MAIN_MAX_DAMAGES, statsTemplate.getMainHandAttack());
        initStat(StatEnum.MAIN_HAND_POWER, statsTemplate.getMainHandAttack());
        initStat(StatEnum.PHYSICAL_DEFENSE, statsTemplate.getPdefense());
        initStat(StatEnum.MAIN_HAND_ACCURACY, statsTemplate.getMainHandAccuracy());
        initStat(StatEnum.MAIN_HAND_CRITICAL, statsTemplate.getMainHandCritRate());
        initStat(StatEnum.MAGICAL_CRITICAL, statsTemplate.getMcrit());
        initStat(StatEnum.BOOST_MAGICAL_SKILL, statsTemplate.getMboost());
        initStat(StatEnum.MAGICAL_ACCURACY, statsTemplate.getMagicAccuracy());
        initStat(StatEnum.PARRY, statsTemplate.getParry());
        initStat(StatEnum.EVASION, statsTemplate.getEvasion());
        initStat(StatEnum.MAGICAL_RESIST, statsTemplate.getMresist());
        initStat(StatEnum.MAIN_HAND_ATTACK_SPEED, 2000);
        initStat(StatEnum.SPEED, Math.round(statsTemplate.getRunSpeed() * 1000));
        initStat(StatEnum.FLY_SPEED, Math.round(statsTemplate.getRunSpeed() * 1000));
        initStat(StatEnum.REGEN_HP, owner.getLevel() + 3);
        initStat(StatEnum.KNOWLEDGE, 100);

        if (statsTemplate.isMagicalDamage()) {
            initStat(StatEnum.MAIN_HAND_MAGICAL_ATTACK, statsTemplate.getMainHandAttack());
            owner.setAttackType(EAttackType.MAGICAL_WATER);
        }
    }

    @Override
    public void recomputeStats() {
        if (owner.getMaster() != null) {
            TreeSet<StatModifier> mods = new TreeSet<StatModifier>();
            
            StatEnum[] stats = { StatEnum.MAXHP, StatEnum.BOOST_MAGICAL_SKILL,
                StatEnum.MAGICAL_ACCURACY, StatEnum.MAGICAL_CRITICAL, StatEnum.PHYSICAL_DEFENSE,
                StatEnum.PARRY, StatEnum.EVASION, StatEnum.BOOST_MAGICAL_SKILL_RESIST,
                StatEnum.MAGICAL_RESIST };

            long[] slots = { ItemSlot.EARRINGS_LEFT.getSlotIdMask(),
                ItemSlot.EARRINGS_RIGHT.getSlotIdMask(), ItemSlot.RING_LEFT.getSlotIdMask(),
                ItemSlot.RING_RIGHT.getSlotIdMask(), ItemSlot.NECKLACE.getSlotIdMask(),
                ItemSlot.HELMET.getSlotIdMask(), ItemSlot.WAIST.getSlotIdMask() };

            Arrays.sort(stats);
            Arrays.sort(slots);

            int[] finalStats = new int[stats.length];
            Arrays.fill(finalStats, 0);

            for (Entry<StatEffectId, TreeSet<StatModifier>> entry : owner.getMaster()
                .getGameStats().getStatsModifiers().entrySet()) {
                if (entry.getKey() instanceof StoneStatEffectId
                    || entry.getKey() instanceof FusionStatEffectId) {
                    for (StatModifier mod : entry.getValue()) {
                        int index = Arrays.binarySearch(stats, mod.getStat());
                        if (index >= 0) {
                            if (mod instanceof AddModifier) {
                                finalStats[index] += ((AddModifier) mod).getValue();
                            }
                        }
                    }
                }
                else if (entry.getKey() instanceof ItemStatEffectId) {
                    ItemStatEffectId stat = (ItemStatEffectId) entry.getKey();

                    if (Arrays.binarySearch(slots, stat.getSlot()) >= 0) {
                        for (StatModifier mod : entry.getValue()) {
                            if (!mod.isBonus())
                                continue;

                            int index = Arrays.binarySearch(stats, mod.getStat());
                            if (index >= 0) {
                                if (mod instanceof AddModifier) {
                                    finalStats[index] += ((AddModifier) mod).getValue();
                                }
                            }
                        }
                    }
                }
            }

            for (int i = 0; i < stats.length; i++) {
                mods.add(AddModifier.newInstance(stats[i], finalStats[i], true));
            }

            super.endEffect(StatEffectId.getInstance(owner.getObjectId(),
                StatEffectType.SUMMON_EFFECT), false);
            super.addModifiers(
                StatEffectId.getInstance(owner.getObjectId(), StatEffectType.SUMMON_EFFECT), mods, false);
        }

        super.recomputeStats();

        updateVisualStats();
    }

    @Override
    public void updateStats() {
        Player master = owner.getMaster();
        if (master != null)
            PacketSendUtility.sendPacket(master, new SM_SUMMON_UPDATE(owner));
    }
}
