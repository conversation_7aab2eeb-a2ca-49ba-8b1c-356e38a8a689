/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.PlayerClass;
import gameserver.model.TaskId;
import gameserver.model.alliance.PlayerAllianceMember;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.ItemService;
import gameserver.services.RespawnService;
import gameserver.utils.MathUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Future;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class AluruRvrDropController extends BossController {

    public AluruRvrDropController() {
        super(Arrays.asList(855921, 855920, 855912, 855913, 855909, 855908, 855898, 856013, 219491,
            219490, 256670, 256671), false);
    }

    @Override
    public void onCreation() {
        Npc owner = getOwner();

        switch (owner.getNpcId()) {
            case 212928:
                owner.getSpawn().getSpawnGroup().setInterval(5 * 60);
                break;
            default:
                owner.getSpawn().getSpawnGroup().setInterval(10 * 60);
                break;
        }
    }

    @Override
    public void scheduleRespawn() {
        int instanceId = getOwner().getInstanceId();

        if (!getOwner().getSpawn().isNoRespawn(instanceId)) {
            Future<?> respawnTask = RespawnService.scheduleRespawnTask(getOwner());
            addTask(TaskId.RESPAWN, respawnTask);
        }
    }

    @Override
    public void doReward() {
        Player killer = getOwner().getAggroList().getMostPlayerDamage();

        if (killer == null)
            return;

        List<Player> players;

        switch (getOwner().getNpcId()) {
            case 855920:
            case 855913:
            case 855908:
            case 856013:
                players = new ArrayList<Player>();

                if (killer.isInAlliance()) {
                    for (PlayerAllianceMember pla : killer.getPlayerAlliance().getMembers()) {
                        Player pl = pla.getPlayer();

                        if (pl != null && MathUtil.isIn3dRange(killer, pl, 80)) {
                            players.add(pl);
                        }
                    }
                }
                else if (killer.isInGroup()) {
                    for (Player pl : killer.getPlayerGroup().getMembers()) {
                        if (MathUtil.isIn3dRange(killer, pl, 80)) {
                            players.add(pl);
                        }
                    }
                }
                else {
                    players.add(killer);
                }

                // Reward 2 random players in the ally/group
                for (int i = 0; i < 2; i++) {
                    if (players.isEmpty())
                        break;

                    Player winner = players.remove(Rnd.get(players.size()));

                    int itemId = 0;

                    switch (PlayerClass.getStartingClassFor(winner.getPlayerClass())) {
                        case WARRIOR:
                        case SCOUT:
                        case ENGINEER:
                            itemId = 164000167;
                            break;
                        case PRIEST:
                        case MAGE:
                        case ARTIST:
                            itemId = 164000178;
                            break;
                    }

                    if (itemId == 0)
                        break;

                    rewardTemporaryItem(winner, itemId, 1);
                }
                break;
            case 855921:
            case 855912:
            case 855909:
            case 855898:
                players = new ArrayList<Player>();

                if (killer.isInAlliance()) {
                    for (PlayerAllianceMember pla : killer.getPlayerAlliance().getMembers()) {
                        Player pl = pla.getPlayer();

                        if (pl != null && MathUtil.isIn3dRange(killer, pl, 80)) {
                            players.add(pl);
                        }
                    }
                }
                else if (killer.isInGroup()) {
                    for (Player pl : killer.getPlayerGroup().getMembers()) {
                        if (MathUtil.isIn3dRange(killer, pl, 80)) {
                            players.add(pl);
                        }
                    }
                }
                else {
                    players.add(killer);
                }

                // Reward 2 random players in the ally/group
                for (int i = 0; i < 2; i++) {
                    if (players.isEmpty())
                        break;

                    Player winner = players.remove(Rnd.get(players.size()));

                    int itemId = 0;

                    if (i == 0)
                        itemId = 164000183;
                    else
                        itemId = 164000174;

                    if (itemId == 0)
                        break;

                    rewardTemporaryItem(winner, itemId, 1);
                }
                break;
            case 256670:
                rewardTemporaryItem(killer, 164000166, 1);
                break;
            case 256671:
                rewardTemporaryItem(killer, 160010238, 1);
                break;
        }
    }
    
    private void rewardTemporaryItem(Player pl, int itemId, long amount) {
        Item item = ItemService.newItem(itemId, amount);

        item.setTemporary(true);

        pl.getInventory().putToBag(item, true);
        ItemService.updateItem(pl, item, true);
    }

    @Override
    protected void think() {
        //
    }
}