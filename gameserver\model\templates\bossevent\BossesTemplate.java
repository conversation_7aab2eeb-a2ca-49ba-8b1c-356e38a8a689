package gameserver.model.templates.bossevent;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "bossgroup", propOrder = { "boss" })
public class BossesTemplate {
    @XmlElement(name = "boss")
    protected List<BossTemplate> boss;

    public List<BossTemplate> getBosses() {
        if (boss == null) {
            boss = new ArrayList<BossTemplate>();
        }
        return this.boss;
    }

}
