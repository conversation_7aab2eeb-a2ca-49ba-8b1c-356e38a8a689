/*
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.ai.state.handler;

import gameserver.ai.AI;
import gameserver.ai.npcai.HomingAi;
import gameserver.ai.npcai.ServantAi;
import gameserver.ai.state.AIState;
import gameserver.model.gameobjects.Npc;
import gameserver.services.OutpostService;
import gameserver.services.SiegeService;

/**
 * <AUTHOR>
 */
public class ThinkingStateHandler extends StateHandler {
    @Override
    public AIState getState() {
        return AIState.THINKING;
    }

    /**
     * State THINKING AI MonsterAi AI AggressiveAi
     */
    @Override
    public void handleState(AIState state, AI<?> ai) {
        ai.clearDesires();

        Npc owner = (Npc) ai.getOwner();

        if (owner.isAggressive() && !(ai instanceof HomingAi || ai instanceof ServantAi))
            owner.checkAggro();

        if (ai.desireQueueSize() > 0)
            return;

        if (owner.getAggroList().getMostHated() != null) {
            ai.setAiState(AIState.ATTACKING);
            return;
        }

        if (!owner.getMoveController().hasFollowLeader() && !owner.isNoHome()
            && !owner.isAtSpawnLocation()) {
            ai.setAiState(AIState.MOVINGTOHOME);
            return;
        }

        if (!owner.getLifeStats().isFullyRestoredHp()
            && !SiegeService.getInstance().isSiegeNpc(owner.getObjectId())
            && !OutpostService.getInstance().isGateSiegeNpc(owner)) {
            ai.setAiState(AIState.RESTING);
            return;
        }

        if (owner.getMoveController().hasFollowLeader())
            owner.getMoveController().schedule();

        ai.setAiState(AIState.ACTIVE);
    }
}
