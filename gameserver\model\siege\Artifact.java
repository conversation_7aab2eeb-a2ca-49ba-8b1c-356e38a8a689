/*
 * This file is part of aion-unique <aion-unique.org>.
 *
 *  aion-unique is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  aion-unique is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with aion-unique.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.siege;

import gameserver.controllers.ArtifactController;
import gameserver.model.gameobjects.Npc;
import gameserver.model.templates.VisibleObjectTemplate;
import gameserver.model.templates.siege.ArtifactTemplate;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.services.SiegeService;
import gameserver.utils.ThreadPoolManager;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class Artifact extends Npc {
    private int spawnStaticId;
    private int artifactId;
    private ArtifactProtector protector;

    private ArrayList<Integer> relatedSpawnedObjectsIds;
    private ArtifactTemplate template;
    private Future<?> balaurRetake = null;
    private SiegeRace race;

    public Artifact(int objId, ArtifactController controller, SpawnTemplate spawn,
        VisibleObjectTemplate objectTemplate, int artifactId, int staticId, SiegeRace race) {
        super(objId, controller, spawn, objectTemplate);
        this.artifactId = artifactId;
        this.spawnStaticId = staticId;
        this.relatedSpawnedObjectsIds = new ArrayList<Integer>();
        this.race = race;

        if (race != SiegeRace.BALAUR)
            scheduleBalaurRetake();
    }

    public void setProtector(ArtifactProtector protector) {
        this.protector = protector;
        protector.setArtifact(this);
    }

    public int getLocationId() {
        return artifactId;
    }

    public int getStaticId() {
        return spawnStaticId;
    }

    public ArtifactProtector getProtector() {
        return protector;
    }

    public void registerRelatedSpawn(int objectId) {
        if (!relatedSpawnedObjectsIds.contains(objectId))
            relatedSpawnedObjectsIds.add(objectId);
    }

    public List<Integer> getRelatedSpawnIds() {
        return relatedSpawnedObjectsIds;
    }

    /**
     * @return the template
     */
    public ArtifactTemplate getTemplate() {
        return template;
    }

    /**
     * @param template
     *            the template to set
     */
    public void setTemplate(ArtifactTemplate template) {
        this.template = template;
    }

    public ArtifactController getController() {
        return (ArtifactController) super.getController();
    }

    public void scheduleBalaurRetake() {
        cancelBalaurRetake();

        final Artifact _artifact = this;

        balaurRetake = ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                SiegeService.getInstance().onArtifactCaptured(_artifact);
            }
        }, Rnd.get(60, 120) * 60 * 1000);
    }

    public void cancelBalaurRetake() {
        if (balaurRetake != null)
            balaurRetake.cancel(false);

        balaurRetake = null;
    }
}
