/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.configs.main;

import com.aionemu.commons.configuration.Property;

/**
 * <AUTHOR> Global Default Pricing
 */
public class PricesConfig {

    // Controls the "Prices:" value in influence tab.
    @Property(key = "gameserver.prices.default.prices", defaultValue = "100")
    public static int DEFAULT_PRICES;

    // Hidden modifier for all prices.
    @Property(key = "gameserver.prices.default.modifier", defaultValue = "100")
    public static int DEFAULT_MODIFIER;

    // Taxes: value = 100 + tax %
    @Property(key = "gameserver.prices.default.taxes", defaultValue = "100")
    public static int DEFAULT_TAXES;

    @Property(key = "gameserver.prices.vendor.buymod", defaultValue = "100")
    public static int VENDOR_BUY_MODIFIER;

    @Property(key = "gameserver.prices.vendor.sellmod", defaultValue = "20")
    public static int VENDOR_SELL_MODIFIER;

}
