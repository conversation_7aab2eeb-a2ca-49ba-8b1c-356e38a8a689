/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 */
package gameserver.eventengine.events;

import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.network.aion.serverpackets.SM_QUEST_ACCEPTED;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.Collection;

/**
 * <AUTHOR>
 * 
 */
public class EyeEvent extends MobEvent {
    public EyeEvent() {
        super.mapId = 600040000;
        super.center = new SpawnPosition(756, 766, 1218);
        super.apBasePlayer = 1000;
        super.apPoolPerPlayer = 1000;
        super.mightBasePlayer = 10;
        super.mightPoolPerPlayer = 10;
    }

    @Override
    public void execute() {
        announceAll("The Commander's Room in Tiamaranta's Eye will be assaulted in 3 minutes!");
        announceAll("The goal will be to kill as many players and monsters as possible", 10 * 1000);
        announceAll("The event at the Commander's Room in Tiamaranta's Eye starts in 2 minutes",
            1 * 60 * 1000);
        announceAll("The event at the Commander's Room in Tiamaranta's Eye starts in 1 minute",
            2 * 60 * 1000);
        announceAll("The event at the Commander's Room in Tiamaranta's Eye starts in 30 seconds",
            30 * 1000 + 2 * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 3 * 60 * 1000);
    }

    private void startEvent() {
        Collection<Player> players = getAvailablePlayers();

        announce(players,
            "Kill as many players and monsters in the Commander's Room in the next 3 minutes!");

        for (Player pl : players) {
            pl.setEvent(this);
            super.playerPoints.put(pl.getObjectId(), 0);
            PacketSendUtility.sendPacket(pl, new SM_QUEST_ACCEPTED(0, 3 * 60));
        }

        super.scheduleEnd(3 * 60);

        spawnEventMobs();
    }

    protected void spawnEventMobs() {
        spawnMob(218645, 781, 766, 1218);
        spawnMob(218645, 775, 779, 1218);    
        spawnMob(218645, 758, 791, 1218);    
        spawnMob(218645, 743, 784, 1218);    
        spawnMob(218645, 732, 767, 1218);    
        spawnMob(218645, 739, 752, 1218);   
        spawnMob(218645, 754, 743, 1218);    
        spawnMob(218645, 771, 749, 1218);    
        spawnMob(217937, 644, 812, 1224);
        spawnMob(217937, 665, 849, 1224);   
        spawnMob(217937, 698, 874, 1224);
        spawnMob(217937, 740, 887, 1224);
        spawnMob(217937, 772, 888, 1224);
        spawnMob(217937, 813, 875, 1224);
        spawnMob(217937, 846, 851, 1224);
        spawnMob(217937, 869, 813, 1224);
        spawnMob(217937, 864, 710, 1224);
        spawnMob(217937, 837, 676, 1224);
        spawnMob(217937, 803, 653, 1224);
        spawnMob(217937, 772, 646, 1224);
        spawnMob(217937, 741, 645, 1224);
        spawnMob(217937, 700, 657, 1224);
        spawnMob(217937, 666, 683, 1224);
        spawnMob(217937, 645, 719, 1224);
    }
}
