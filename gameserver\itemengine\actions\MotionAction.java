package gameserver.itemengine.actions;

import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Motion;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_DELETE_ITEM;
import gameserver.network.aion.serverpackets.SM_MOTION;
import gameserver.utils.PacketSendUtility;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MotionAction")
public class MotionAction extends AbstractItemAction {
    @XmlAttribute
    protected String motionName;
    @XmlAttribute
    protected int expire;

    public int getMotionId() {
        Motion motion = Motion.getMotionByName(motionName);
        return motion != null ? motion.getMotionId() : 0;
    }

    @Override
    public boolean canAct(Player player, Item parentItem, Item targetItem) {
        return player.getMotionList().canAddMotion(getMotionId());
    }

    @Override
    public void act(Player player, Item parentItem, Item targetItem) {
        Item item = player.getInventory().getItemByObjId(parentItem.getObjectId());

        if (item != null) {
            player.getInventory().removeFromBag(item, true);
        }

        Motion motion = Motion.getMotionByName(motionName);
        player.getMotionList().addMotion(motion.getMotionId(), System.currentTimeMillis(),
            (expire * 60L));
        player.getMotions().put(motion.getType(), motion);

        PacketSendUtility.sendPacket(player, new SM_MOTION(motion, (expire * 60L)));
        PacketSendUtility.sendPacket(player, new SM_DELETE_ITEM(parentItem.getObjectId()));
        PacketSendUtility.broadcastPacket(player, new SM_MOTION(player));
    }
}
