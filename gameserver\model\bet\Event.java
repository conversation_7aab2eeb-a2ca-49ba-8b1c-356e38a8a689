/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.bet;

/**
 * <AUTHOR>
 * 
 */
public class Event {
    private String name;
    private String groupname = "GLOBAL";
    private int value = -1;

    public Event(String name) {
        this.name = name;
    }

    public Event(String name, String groupname, int value) {
        this.name = name;
        this.groupname = groupname;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public String getGroupname() {
        return groupname;
    }

    public int getValue() {
        return value;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    public void setValue(int value) {
        this.value = value;
    }
}
