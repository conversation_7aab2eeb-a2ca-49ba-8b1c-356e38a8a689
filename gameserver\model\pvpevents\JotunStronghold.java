/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.pvpevents;

import gameserver.dao.InstanceScoreDAO;
import gameserver.dataholders.DataManager;
import gameserver.model.Gender;
import gameserver.model.drop.DropTemplate;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEffectType;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.RateModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.templates.item.EAttackType;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.DropService;
import gameserver.services.GloryService;
import gameserver.services.ItemService;
import gameserver.services.PvpService;
import gameserver.services.TeleportService;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.Executor;
import gameserver.world.World;
import gameserver.world.WorldPosition;

import java.util.Arrays;
import java.util.List;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 */
public class JotunStronghold extends Battleground {
    private boolean endCalled = false;

    private AtomicBoolean hardMode = new AtomicBoolean(false);

    private static final int INSTANCE_ID = 1004;

    private static final int INITIAL_DELAY = 15000;

    private AtomicInteger objectivesCompleted = new AtomicInteger(0);
    private AtomicBoolean completed = new AtomicBoolean(false);
    private AtomicBoolean completedPuzzle = new AtomicBoolean(false);
    private AtomicBoolean spawnedDebil = new AtomicBoolean(false);
    private AtomicInteger score = new AtomicInteger(0);

    private WorldPosition savedPosition = null;

    private int finishSeconds = 0;

    private AtomicBoolean door37 = new AtomicBoolean(false);

    public JotunStronghold() {
        super.name = "Jotun Stronghold";
        super.displayName = "Instance";
        super.minSize = 1;
        super.maxSize = 1;
        super.teamCount = 1;
        super.matchLength = INITIAL_DELAY / 1000 + 25 * 60;
        super.isAnonymous = false;
        super.afkKick = false;
        super.shouldDisband = false;
        super.isPvE = true;

        BattlegroundMap map1 = new BattlegroundMap(300510000);
        map1.addSpawn(new SpawnPosition(1582, 1069, 493));
        map1.addStaticDoor(37);
        map1.addStaticDoor(610);
        map1.addStaticDoor(706);
        map1.addStaticDoor(711);
        map1.setKillZ(350f);

        super.maps.add(map1);
    }

    public void createMatch(List<Player> players) {
        super.handleQueueSolo(players);

        if (super.getPlayers().isEmpty())
            return;

        startMatch();
    }

    public void startMatch() {
        super.createInstance();

        super.openStaticDoors();

        synchronized (super.getPlayers()) {
            for (Player pl : super.getPlayers()) {
                super.preparePlayerInstance(pl, INITIAL_DELAY);

                SpawnPosition pos = getSpawnPositions().get(pl.getBgIndex());
                performTeleport(pl, pos.getX(), pos.getY(), pos.getZ());
            }
        }

        createSpawns();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                intro();

                /*
                 * setExtraTask(ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
                 * @Override public void run() { if (door37.get()) { if (getExtraTask() != null)
                 * getExtraTask().cancel(false); return; } if (!hasPlayers()) return; Player pl = getPlayers().get(0);
                 * if (MathUtil.isInRange(pl, 740, 1069, 50) && door37.compareAndSet(false, true)) {
                 * getInstance().openDoor(37, 0); if (getExtraTask() != null) getExtraTask().cancel(false); } } }, 1 *
                 * 1000, 1 * 1000));
                 */
            }
        }, INITIAL_DELAY);

        super.setExpireTask(ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endInstance(false);
            }
        }, getMatchLength() * 1000));

        super.startBackgroundTask();
    }

    @Override
    public void onStatRecomputation(Player player) {
        super.onStatRecomputation(player);

        if (player.isSpectating())
            return;

        switch (player.getPlayerClass()) {
            case CLERIC:
            case BARD:
                player.getGameStats().setStat(StatEnum.BOOST_HEAL,
                    player.getGameStats().getStatBonus(StatEnum.BOOST_HEAL) - 150, true);
                break;
            case RANGER:
                player.getGameStats().setStat(StatEnum.MAIN_HAND_POWER,
                    player.getGameStats().getStatBonus(StatEnum.MAIN_HAND_POWER) + 300, true);
                break;
        }
    }

    private void intro() {
        final Npc intro = spawnNpc(800966, 1578f, 1073.2f, 492.1f, 101, true);

        intro.shout("Welcome to the Jotun Stronghold!", 500);
        intro.shout("It's just you and me here.", 4500);

        intro.walkTo(1550f, 1072f, 492.16028f, 2000);

        intro.shout("You must vanquish the giants that roam these halls.", 8500);
        intro
            .shout("But try to explore a bit - I've heard of powerful magic in this place!", 14000);
        intro
            .shout(
                "If you find this magic, perhaps it will make you strong enough to take on the giants.",
                19500);
        intro.shout("Of course, the bold and brave will be rewarded plentiful.", 25000);

        String gender = "lad";
        for (Player pl : super.getPlayers())
            if (pl.getCommonData().getGender() == Gender.FEMALE)
                gender = "lass";

        intro.shout("This is it, courageous " + gender + "!", 30500);
        intro.shout("I bid you the best of luck.", 38000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                intro.getController().onDelete();
            }
        }, 42000);
    }

    private void outro() {
        addTimeScore();

        for (Player pl : super.getPlayers())
            GloryService.getInstance().rewardSlippery(pl, score.get() / 40);

        final Npc outro = spawnNpc(800966, 1087f, 1063f, 786.3f, 50, true);

        spawnNpc(205510, 1090.7f, 1069.8f, 818.75836f, 59, false, true);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                getMap().setKillZ(780f);

                for (Player pl : getPlayers()) {
                    switch (objectivesCompleted.get()) {
                        case 4:
                            rewardItem(pl, 166020000, hardMode.get() ? 20 : 7);
                            break;
                        case 3:
                            rewardItem(pl, 166020000, hardMode.get() ? 10 : 4);
                            break;
                        case 2:
                            rewardItem(pl, 166020000, hardMode.get() ? 5 : 3);
                            break;
                        case 1:
                            break;
                    }

                    if (objectivesCompleted.get() < 2) {
                        scheduleAnnouncement(pl, "Jotun Stronghold",
                            "You didn't complete enough objective to get a bonus reward.", 5000);
                        continue;
                    }
                }
            }
        }, 6000);

        announceAll("Jotun Stronghold", "You completed " + objectivesCompleted.get()
            + " out of 4 objectives with the score " + score.get() + "!", 3000);

        outro.shout("You made it my dear!", 4000);
        outro.shout("This is truly a magnificent place.", 8000);
        outro.shout("Do you see those flying objects above us?", 12000);
        outro.shout("I think it's a challenge for the extra bold.", 16000);

        outro.walkTo(1102.8f, 1056.7f, 790.55963f, 17000);

        outro.shout("Follow this staircase and go behind the tree.", 21000);
        outro.shout("May the Gods be with you.", 28000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                getMap().setKillZ(788f);

                outro.getController().onDelete();
            }
        }, 32000);
    }

    private void createSpawns() {
        // Spawn Room
        spawnNpc(281844, 1216.6f, 1069.2f, 491.4f, 119, true); // Gravekeeper Basaim

        spawnNpc(215838, 1496f, 1059f, 492f, 7, true);
        spawnNpc(215838, 1449f, 1077f, 492f, 115, true);
        spawnNpc(215838, 1381f, 1063f, 492f, 0, true);
        spawnNpc(215838, 1329f, 1077f, 492f, 86, true);

        spawnNpc(282637, 1283f, 1057f, 492f, 7, true);
        spawnNpc(282637, 1276f, 1081f, 492f, 107, true);

        spawnNpc(700568, 1248.6f, 1088.3f, 492f, 88, true); // Jotun Relics

        spawnNpc(701682, 1248.7f, 1048.1f, 492f, 29, true); // Frozen Novun

        // Center
        spawnNpc(282637, 1144f, 1110f, 498f, 58, true);
        spawnNpc(282637, 1111f, 1104f, 498f, 75, true);
        spawnNpc(282637, 1072f, 1093f, 497f, 51, true);
        spawnNpc(282637, 1049f, 1123f, 498f, 40, true);
        spawnNpc(282637, 1033f, 1096f, 498f, 114, true);
        spawnNpc(282637, 1061f, 1054f, 497f, 86, true);
        spawnNpc(282637, 1036f, 1022f, 498f, 97, true);
        spawnNpc(282637, 1085f, 1035f, 497f, 6, true);
        spawnNpc(282637, 1128f, 1020f, 498f, 20, true);
        spawnNpc(282637, 1105f, 1064f, 498f, 55, true);
        spawnNpc(282637, 1085f, 962f, 496f, 21, true);
        spawnNpc(282637, 1097f, 939f, 496f, 80, true);
        spawnNpc(282637, 1078f, 915f, 496f, 104, true);
        spawnNpc(282637, 1072f, 841f, 496f, 112, true);
        spawnNpc(282637, 1107f, 816f, 496f, 66, true);
        spawnNpc(282637, 1076f, 790f, 496f, 14, true);

        // Military Room
        spawnNpc(215794, 981.5f, 1319.2f, 497f, 119, true); // Anvilface

        spawnNpc(215838, 1090f, 1277f, 496f, 68, true);
        spawnNpc(215838, 1080f, 1303f, 496f, 117, true);
        spawnNpc(215838, 1032f, 1322f, 496f, 104, true);

        spawnNpc(282637, 1075f, 1226f, 496f, 108, true);
        spawnNpc(282637, 1022f, 1349f, 497f, 89, true);
        spawnNpc(282637, 1022f, 1290f, 497f, 30, true);

        spawnNpc(700568, 998.6f, 1288.5f, 497f, 17, true); // Jotun Relics

        spawnNpc(701682, 999.2f, 1349.9f, 497f, 102, true); // Frozen Novun

        // Gauntlet Room
        spawnNpc(215796, 677.7f, 1068.0f, 498f, 0, true); // Gradarim the Collector

        spawnNpc(215838, 755f, 1099f, 502f, 89, true);
        spawnNpc(215838, 757f, 1038f, 502f, 26, true);
        spawnNpc(215838, 795f, 1061f, 501f, 9, true);
        spawnNpc(215838, 816f, 1102f, 502f, 105, true);
        spawnNpc(215838, 826f, 1035f, 502f, 15, true);
        spawnNpc(215838, 849f, 1069f, 501f, 114, true);
        spawnNpc(215838, 869f, 1103f, 502f, 96, true);
        spawnNpc(215838, 878f, 1032f, 502f, 44, true);

        spawnNpc(282637, 922f, 1073f, 496f, 1, true);
        spawnNpc(282637, 937f, 1056f, 496f, 13, true);

        spawnNpc(700568, 680.1f, 1103.3f, 498f, 90, true); // Jotun Relics

        spawnNpc(701682, 678.8f, 1035f, 498f, 28, true); // Frozen Novun

        // Command Room
        spawnNpc(281419, 1030.4f, 297.7f, 409.1f, 29, true); // Debilkarim the Maker (small)

        spawnNpc(215838, 1043f, 451f, 443f, 37, true);
        spawnNpc(215838, 1020f, 449f, 443f, 15, true);

        spawnNpc(700568, 1053.5f, 321.2f, 410f, 75, true); // Jotun Relics
        spawnNpc(701682, 1006.1f, 321f, 410f, 107, true); // Frozen Novun

        // Jumping Puzzle
        spawn(3420046, 1120.29, 1067.81, 790.919, 119, false);
        spawn(3420046, 1121.59, 1067.78, 792.336, 119, false);
        spawn(3420046, 1122.59, 1067.43, 793.957, 119, false);
        spawn(3420046, 1124.8, 1066.6, 795.322, 113, false);
        spawn(3420046, 1125.42, 1064.91, 796.591, 84, false);
        spawn(3420046, 1125, 1063.34, 797.939, 84, false);
        spawn(3420046, 1123.61, 1061.9, 799.738, 105, false);
        spawn(3420046, 1121.25, 1061.92, 800.854, 24, false);
        spawn(3420046, 1119.97, 1063.48, 802.451, 12, false);
        spawn(3420046, 1120.86, 1066.23, 803.724, 52, false);
        spawn(3420008, 1120.94, 1073.55, 803.841, 31, false);
        spawn(3420008, 1119.42, 1078.26, 805.135, 41, false);
        spawn(3420008, 1115.68, 1082.99, 806.145, 45, false);
        spawn(3420008, 1109.9, 1086.66, 807.331, 51, false);
        spawn(3420008, 1102, 1089.78, 807.92, 51, false);
        spawn(3420008, 1096.67, 1091.1, 809.509, 85, false);
        spawn(3420008, 1089.08, 1092.87, 809.075, 55, false);
        spawn(3420008, 1082.06, 1094.34, 810.105, 87, false);
        spawn(3141000, 1078.95, 1092.02, 811.521, 86, false);
        spawn(3141000, 1076.01, 1087.15, 811.969, 80, false);
        spawn(3141000, 1073.65, 1082.76, 812.256, 81, false);
        spawn(3141000, 1073.99, 1079.18, 813.391, 89, false);
        spawn(3141000, 1075.03, 1076, 814.651, 119, false);
        spawn(3141000, 1076.64, 1072.8, 815.467, 97, false);
        spawn(3141000, 1078.54, 1069.61, 816.985, 16, false);
        spawn(3141000, 1082.59, 1069.61, 817.315, 1, false);
        spawn(3141000, 1087.18, 1069.83, 817.413, 119, false);
        spawn(3420010, 1090.23, 1069.78, 817.922, 119, false);
    }

    @Override
    public boolean onTalk(Npc npc, Player player) {
        switch (npc.getNpcId()) {
            case 205506: // Military Teleport
                npc.shout("Nyerk!");
                TeleportService.teleportTo(player, getMapId(), getInstanceId(), 1086f, 1172f, 496f,
                    (byte) 30, TeleportService.TELEPORT_BEAM_DELAY);
                return true;
            case 205507: // Gauntlet Teleport
                npc.shout("Kekekekeke!");
                TeleportService.teleportTo(player, getMapId(), getInstanceId(), 977f, 1068f, 497f,
                    (byte) 59, TeleportService.TELEPORT_BEAM_DELAY);

                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        openStaticDoors();
                    }
                }, 5000);

                return true;
            case 205508: // Throne Teleport
                npc.shout("NYYYEERK!");
                TeleportService.teleportTo(player, getMapId(), getInstanceId(), 1030f, 490f, 443f,
                    (byte) 90, TeleportService.TELEPORT_BEAM_DELAY);

                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        openStaticDoors();
                    }
                }, 5000);

                return true;
            case 205509: // Final Teleport
                if (!completed.compareAndSet(false, true)) // Only-once
                    return true;

                npc.shout("KEKEKEKEKEKE!");
                TeleportService.teleportTo(player, getMapId(), getInstanceId(), 1073f, 1069f, 786f,
                    (byte) 119, TeleportService.TELEPORT_BEAM_DELAY);
                outro();
                return true;
            case 205510: // Jumping Puzzle
                if (!completedPuzzle.compareAndSet(false, true)) // Only-once
                    return true;
                else if (!completed.get()) // Can't do this without having finished instance
                    return true;

                npc.shout("KEKENYYYYYEEERK!");
                npc.getController().onDelete();
                completePuzzle();
                return true;
            case 700568: // Jotun Relics
                SkillTemplate buffRelics = DataManager.SKILL_DATA.getSkillTemplate(19786);

                if (buffRelics != null) {
                    Effect effect = new Effect(player, player, buffRelics, 1, 140 * 1000);
                    player.getEffectController().addEffect(effect);
                    effect.addAllEffectToSucess();
                    effect.startEffect(true);

                    player.getEffectController().updatePlayerEffectIcons();
                }

                player.getLifeStats().increaseHp(TYPE.HP, 7500);
                player.getLifeStats().increaseMp(TYPE.MP, 7500);

                npc.shout("You have discovered some powerful magic!");
                npc.getController().onDelete();

                scorePoints(-50);

                return true;
            case 701682: // Frozen Novun
                SkillTemplate buffFrozen = DataManager.SKILL_DATA.getSkillTemplate(20490);

                if (buffFrozen != null) {
                    Effect effect = new Effect(player, player, buffFrozen, 1, 140 * 1000);
                    player.getEffectController().addEffect(effect);
                    effect.addAllEffectToSucess();
                    effect.startEffect(true);

                    player.getEffectController().updatePlayerEffectIcons();
                }

                player.getLifeStats().increaseHp(TYPE.HP, 7500);
                player.getLifeStats().increaseMp(TYPE.MP, 7500);

                npc.shout("You have discovered some powerful magic!");
                npc.getController().onDelete();

                scorePoints(-50);

                return true;
        }

        return false;
    }

    private void completeObjective(boolean end) {
        if (end) {
            super.announceAll("Jotun Stronghold", "You have completed the final objective!");
            scorePoints(300);
        }
        else {
            super.announceAll("Jotun Stronghold", "You have completed an objective!");
            scorePoints(250);
        }

        objectivesCompleted.incrementAndGet();

        int might = hardMode.get() ? 50 : 20;
        int dp = hardMode.get() ? 2000 : 1000;

        for (Player pl : super.getPlayers()) {
            PvpService.getInstance().addMight(pl, might);
            pl.getCommonData().addDp(dp);
        }
    }

    private void completePuzzle() {
        super.announceAll("Jotun Stronghold",
            "You have completed the Jumping Puzzle bonus objective!");
        super
            .announceAll("Jotun Stronghold",
                "You have received three [item:166020000] - that deserves a pat on the shoulder.",
                5000);

        int might = hardMode.get() ? 100 : 50;

        for (Player pl : super.getPlayers()) {
            PvpService.getInstance().addMight(pl, might);
            rewardItem(pl, 166020000, 3);
        }

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                endInstance(false);
            }
        }, 6000);
    }

    private void rewardItem(Player player, int itemId, long amount) {
        ItemService.addItem(player, itemId, amount);
        PacketSendUtility.sendMessage(player, "You have received " + amount + " x [item:" + itemId
            + "].");
    }

    @Override
    public void onKill(Npc npc, Creature lastAttacker) {
        if (lastAttacker == null && super.getPlayers().size() > 0) {
            lastAttacker = super.getPlayers().get(0);
        }

        if (!(lastAttacker.getMaster() instanceof Player))
            return;

        Player killer = (Player) lastAttacker.getMaster();

        switch (npc.getNpcId()) {
            case 281844: // Gravekeeper Basaim
                spawnNpc(205506, 1181.8f, 1069.2f, 492f, 119, false).setCustomTag(
                    "Military Teleport");
                completeObjective(false);
                break;
            case 215794: // Anvilface
                spawnNpc(205507, 960.8f, 1319.5f, 497f, 119, false).setCustomTag(
                    "Gauntlet Teleport");
                completeObjective(false);
                break;
            case 215796: // Gradarim the Collector
                spawnNpc(205508, 651.5f, 1067.9f, 498f, 0, false).setCustomTag("Command Teleport");
                completeObjective(false);
                break;
            case 281419: // Debilkarim the Maker (small)
                if (!spawnedDebil.compareAndSet(false, true)) // Only-once
                    break;

                spawnNpc(283134, npc.getX(), npc.getY(), npc.getZ(), npc.getHeading(), true)
                    .delete(5000); // Dust storm at small

                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        spawnNpc(215795, 1030.1f, 271.9f, 409.1f, 29, true).shout("Now I'm mad!",
                            1000); // Debilkarim the Maker
                    }
                }, 3000);

                spawnNpc(283137, 1017f, 299f, 410f, 27, true).delete(5000); // Debris
                spawnNpc(283137, 1045f, 296f, 410f, 33, true).delete(5000); // Debris
                spawnNpc(283081, 1030f, 298f, 410f, 31, true).delete(5000); // Earthquake
                break;
            case 215795: // Debilkarim the Maker
                npc.shout("AARGGH!!!");

                spawnNpc(205509, 1030.1f, 264.5f, 410f, 30, false).setCustomTag("Final Teleport");
                spawnNpc(205509, 1029.5f, 384.4f, 400f, 89, false).setCustomTag("Final Teleport");
                completeObjective(true);
                break;
            case 282469: // Protection of Aion
                break;
            default:
                scorePoints(25);

                int might = hardMode.get() ? 10 : 5;

                PvpService.getInstance().addMight(killer, might);
                break;
        }

        killer.getLifeStats().increaseHp(TYPE.HP, 2000);
        killer.getLifeStats().increaseMp(TYPE.MP, 2000);

        if (!DropService.getInstance().hasDrops(npc)) {
            npc.getController().onDelete();
        }
    }

    private void registerDrops(Npc npc, DropTemplate... drops) {
        DropService.getInstance().registerDrop(npc, Arrays.asList(drops), true);
    }

    private void alterStats(Npc npc) {
        if (hardMode.get()) {
            TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

            mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 40, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 800, true));
            mods.add(AddModifier.newInstance(StatEnum.MAGICAL_RESIST, 700, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL_RESIST, 800, true));
            mods.add(AddModifier.newInstance(StatEnum.EVASION, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 2500, true));

            mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_CRITICAL_RESIST, 400, true));
            mods.add(AddModifier.newInstance(StatEnum.MAGICAL_CRITICAL_RESIST, 200, true));

            mods.add(AddModifier.newInstance(StatEnum.ABNORMAL_RESISTANCE_ALL, -2000, true));
            mods.add(AddModifier.newInstance(StatEnum.ROOT_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SNARE_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SILENCE_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SLEEP_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.OPENAREIAL_RESISTANCE, 1000, true));

            switch (npc.getNpcId()) {
                case 281844: // Gravekeeper Basaim
                    npc.setAttackType(EAttackType.MAGICAL_FIRE);
                    mods.add(AddModifier.newInstance(StatEnum.KNOWLEDGE, 250, true));
                    break;
                case 215794: // Anvilface
                    break;
                case 215796: // Gradarim the Collector
                    break;
                case 281419: // Debilkarim the Maker (small)
                    break;
                case 215795: // Debilkarim the Maker
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_CRITICAL_RESIST, 500, true));
                    mods.add(AddModifier.newInstance(StatEnum.MAGICAL_CRITICAL_RESIST, 500, true));
                    break;
                case 217040: // Jotun Artificer
                    mods.add(AddModifier.newInstance(StatEnum.SLEEP_RESISTANCE, -1000, true));
                    mods.add(AddModifier.newInstance(StatEnum.ROOT_RESISTANCE, -1000, true));
                    break;
                case 282469: // Protection of Aion
                    npc.setAttackType(EAttackType.MAGICAL_WIND);
                    mods.add(AddModifier.newInstance(StatEnum.KNOWLEDGE, 50, true));
                    break;
            }

            npc.getGameStats().endEffect(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT));
            npc.getGameStats().addModifiers(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT), mods);
        }
        else {
            TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

            mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 15, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 400, true));
            mods.add(AddModifier.newInstance(StatEnum.MAGICAL_RESIST, 250, true));
            mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL_RESIST, 300, true));
            mods.add(AddModifier.newInstance(StatEnum.EVASION, 600, true));

            mods.add(AddModifier.newInstance(StatEnum.ABNORMAL_RESISTANCE_ALL, -2000, true));
            mods.add(AddModifier.newInstance(StatEnum.ROOT_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SILENCE_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.SLEEP_RESISTANCE, 1000, true));
            mods.add(AddModifier.newInstance(StatEnum.OPENAREIAL_RESISTANCE, 1000, true));

            switch (npc.getNpcId()) {
                case 281844: // Gravekeeper Basaim
                    npc.setAttackType(EAttackType.MAGICAL_FIRE);
                    mods.add(AddModifier.newInstance(StatEnum.KNOWLEDGE, 175, true));
                    mods.add(AddModifier.newInstance(StatEnum.SNARE_RESISTANCE, 1000, true));
                    break;
                case 215794: // Anvilface
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, 2500, true));
                    mods.add(AddModifier.newInstance(StatEnum.MAGICAL_RESIST, 500, true));
                    mods.add(AddModifier.newInstance(StatEnum.FIRE_RESISTANCE, -200, true));
                    mods.add(AddModifier.newInstance(StatEnum.EARTH_RESISTANCE, -200, true));
                    mods.add(AddModifier.newInstance(StatEnum.WIND_RESISTANCE, -200, true));
                    mods.add(AddModifier.newInstance(StatEnum.WATER_RESISTANCE, -200, true));
                    mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, -15, true));
                    break;
                case 215796: // Gradarim the Collector
                    mods.add(AddModifier.newInstance(StatEnum.EVASION, 600, true));
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, -1000, true));
                    break;
                case 281419: // Debilkarim the Maker (small)
                    break;
                case 215795: // Debilkarim the Maker
                    mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_CRITICAL_RESIST, 500, true));
                    mods.add(AddModifier.newInstance(StatEnum.MAGICAL_CRITICAL_RESIST, 500, true));
                    break;
                case 217040: // Jotun Artificer
                    mods.add(AddModifier.newInstance(StatEnum.SLEEP_RESISTANCE, -1000, true));
                    mods.add(AddModifier.newInstance(StatEnum.ROOT_RESISTANCE, -1000, true));
                    break;
                case 282469: // Protection of Aion
                    npc.setAttackType(EAttackType.MAGICAL_WIND);
                    mods.add(AddModifier.newInstance(StatEnum.KNOWLEDGE, 50, true));
                    break;
            }

            npc.getGameStats().endEffect(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT));
            npc.getGameStats().addModifiers(
                StatEffectId.getInstance(npc.getObjectId(), StatEffectType.SUMMON_EFFECT), mods);
        }
    }

    private void scorePoints(int points) {
        scorePoints(points, false);
    }

    private void scorePoints(final int points, final boolean time) {
        final int currentScore = score.addAndGet(points);

        getInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                if (time) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_GET_SCORE("Time Bonus", points));
                }
                else {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_GET_SCORE_FOR_ENEMY(points));
                }

                if (currentScore - points < 2500 && currentScore >= 2500) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(2500));
                }
                else if (currentScore - points < 2250 && currentScore >= 2250) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(2250));
                }
                else if (currentScore - points < 2000 && currentScore >= 2000) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(2000));
                }
                else if (currentScore - points < 1750 && currentScore >= 1750) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(1750));
                }
                else if (currentScore - points < 1500 && currentScore >= 1500) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(1500));
                }
                else if (currentScore - points < 1250 && currentScore >= 1250) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(1250));
                }
                else if (currentScore - points < 1000 && currentScore >= 1000) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(1000));
                }
                else if (currentScore - points < 750 && currentScore >= 750) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(750));
                }
                else if (currentScore - points < 500 && currentScore >= 500) {
                    PacketSendUtility.sendPacket(pl,
                        SM_SYSTEM_MESSAGE.STR_MSG_INSTANCE_SCORE_ALARM(500));
                }

                return true;
            }
        });
    }

    private void addTimeScore() {
        int seconds = getSecondsLeft();

        finishSeconds = seconds;

        int points = (int) Math.max(Math.log(seconds / 60d) * 200, 0);

        scorePoints(points, true);
    }

    private Npc spawnNpc(int npcId, SpawnPosition pos) {
        return spawnNpc(npcId, pos.getX(), pos.getY(), pos.getZ(), pos.getHeading(), true, true);
    }

    private Npc spawnNpc(int npcId, SpawnPosition pos, boolean noHome) {
        return spawnNpc(npcId, pos.getX(), pos.getY(), pos.getZ(), pos.getHeading(), true, noHome);
    }

    private Npc spawnNpc(int npcId, float x, float y, float z, int heading, boolean noHome) {
        return spawnNpc(npcId, x, y, z, heading, true, noHome);
    }

    private Npc spawnNpc(int npcId, float x, float y, float z, int heading, boolean geo,
        boolean noHome) {
        Npc npc = (Npc) spawn(npcId, x, y, z, heading, geo);
        npc.setNoHome(noHome);

        alterStats(npc);

        return npc;
    }

    public void onDie(final Player player, Creature lastAttacker) {
        super.onDieDefault(player, lastAttacker);

        if (completed.get()) {
            super.announceAll("Jotun Stronghold", "Better luck on the Puzzle next time!");
        }
        else {
            super.announceAll("Jotun Stronghold", "You have failed your mission!");
        }

        endInstance(false);
    }

    public void onLeave(final Player player, boolean isLogout, boolean isAfk) {
        if (!player.isSpectating()) {
            if (completed.get()) {
                endInstance(false);
            }
            else {
                log.info("[DEBUG] Jotun Stronghold: " + player.getName()
                    + " disconnected from instance " + super.getInstanceId() + " with "
                    + super.getSecondsLeft() + " seconds left.");

                savedPosition = player.getPosition().clone();
            }
        }

        super.onLeaveDefault(player, true, isAfk);
    }

    public void onReconnect(Player player) {
        log.info("[DEBUG] Jotun Stronghold: " + player.getName() + " reconnected to instance "
            + super.getInstanceId() + " with " + super.getSecondsLeft() + " seconds left.");

        if (savedPosition != null)
            World.getInstance().setPosition(player, getMapId(), getInstanceId(),
                savedPosition.getX(), savedPosition.getY(), savedPosition.getZ(),
                savedPosition.getHeading());
    }

    public void recordScore() {
        if (!completed.get())
            return;

        for (Player pl : super.getPlayers()) {
            log.info("[DEBUG] Jotun Stronghold: " + pl.getName() + " finished instance "
                + super.getInstanceId() + ". Objectives: " + objectivesCompleted.get()
                + ", Score: " + score.get() + ", Puzzle: " + (completedPuzzle.get() ? "Yes" : "No"));

            DAOManager.getDAO(InstanceScoreDAO.class).insertScore(pl, INSTANCE_ID, score.get(),
                objectivesCompleted.get(), finishSeconds, completedPuzzle.get() ? 1 : 0);
        }
    }

    private void endInstance(boolean timer) {
        if (endCalled)
            return;

        endCalled = true;

        super.onEndFirstDefault();

        super.deleteNpcs();

        recordScore();

        if (!timer) {
            super.onEndDefault();
            return;
        }

        if (completed.get()) {
            super.announceAll("Jotun Stronghold", "Time's up! That's all for now.");
        }
        else {
            super.announceAll("Jotun Stronghold", "The time has run out! Better luck next time.");
        }

        onEndDefault();
    }
}