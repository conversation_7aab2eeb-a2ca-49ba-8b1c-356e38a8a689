/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects;

import gameserver.controllers.NpcPlayerController;
import gameserver.model.BoundRadius;
import gameserver.model.Gender;
import gameserver.model.PlayerClass;
import gameserver.model.Race;
import gameserver.model.gameobjects.player.AbyssRank;
import gameserver.model.gameobjects.player.PlayerAppearance;
import gameserver.model.gameobjects.stats.NpcPlayerGameStats;
import gameserver.model.items.NpcEquippedGear;
import gameserver.model.templates.spawn.SpawnTemplate;

public class NpcPlayer extends Monster {
    private Race race;
    private Gender gender;
    private PlayerAppearance playerAppearance;
    private PlayerClass playerClass;
    private int robot;
    private String name;
    private boolean mentor;
    private NpcEquippedGear equipment;
    private AbyssRank abyssRank;
    private int bonusIcon;
    private int aggroRange;
    private byte level;

    /**
     * @param objId
     * @param controller
     * @param spawn
     * @param objectTemplate
     */
    public NpcPlayer(int objId, NpcPlayerController controller, SpawnTemplate spawn) {
        super(objId, controller, spawn, null);

        super.setGameStats(new NpcPlayerGameStats(this));
        
        this.race = Race.ELYOS;
        this.gender = Gender.MALE;
        this.playerAppearance = new PlayerAppearance();
        this.playerAppearance.setHeight(1.0f);
        this.playerClass = PlayerClass.WARRIOR;
        this.name = "NoName";
        this.abyssRank = new AbyssRank(0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0,
            System.currentTimeMillis());
        this.aggroRange = 10;
        this.level = 65;
    }

    @Override
    public NpcPlayerController getController() {
        return (NpcPlayerController) super.getController();
    }
    
    @Override
    public int getNpcId() {
        return 1000001;
    }

    /**
     * @return the gender
     */
    public Gender getGender() {
        return gender;
    }

    /**
     * @param gender
     *            the gender to set
     */
    public void setGender(Gender gender) {
        this.gender = gender;
    }

    /**
     * @return the playerAppearance
     */
    public PlayerAppearance getPlayerAppearance() {
        return playerAppearance;
    }

    /**
     * @param playerAppearance
     *            the playerAppearance to set
     */
    public void setPlayerAppearance(PlayerAppearance playerAppearance) {
        this.playerAppearance = playerAppearance;
    }

    /**
     * @return the race
     */
    public Race getRace() {
        return race;
    }

    /**
     * @param race
     *            the race to set
     */
    public void setRace(Race race) {
        this.race = race;
    }

    /**
     * @return the templateId
     */
    public int getTemplateId() {
        return 100000 + race.getRaceId() * 2 + gender.getGenderId();
    }

    /**
     * @return the robot
     */
    public int getRobot() {
        return robot;
    }

    /**
     * @param robot
     *            the robot to set
     */
    public void setRobot(int robot) {
        this.robot = robot;
    }

    /**
     * @return the playerClass
     */
    public PlayerClass getPlayerClass() {
        return playerClass;
    }

    /**
     * @param playerClass
     *            the playerClass to set
     */
    public void setPlayerClass(PlayerClass playerClass) {
        this.playerClass = playerClass;
    }

    /**
     * @return the name
     */
    @Override
    public String getName() {
        return name;
    }

    /**
     * @param name
     *            the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return the mentor
     */
    public boolean isMentor() {
        return mentor;
    }

    /**
     * @param mentor
     *            the mentor to set
     */
    public void setMentor(boolean mentor) {
        this.mentor = mentor;
    }

    /**
     * @return the equipment
     */
    public NpcEquippedGear getEquipment() {
        return equipment;
    }

    /**
     * @param equipment
     *            the equipment to set
     */
    public void setEquipment(NpcEquippedGear equipment) {
        this.equipment = equipment;
    }

    /**
     * @return the abyssRank
     */
    public AbyssRank getAbyssRank() {
        return abyssRank;
    }

    /**
     * @param abyssRank
     *            the abyssRank to set
     */
    public void setAbyssRank(AbyssRank abyssRank) {
        this.abyssRank = abyssRank;
    }

    /**
     * @return the bonusIcon
     */
    public int getBonusIcon() {
        return bonusIcon;
    }

    /**
     * @param bonusIcon
     *            the bonusIcon to set
     */
    public void setBonusIcon(int bonusIcon) {
        this.bonusIcon = bonusIcon;
    }
    
    @Override
    public boolean isAggressiveTo(Creature creature) {
        return true;
    }
    
    @Override
    public boolean isAggressive() {
        return true;
    }
    
    @Override
    public boolean isHostile() {
        return true;
    }
    
    @Override
    public boolean isGuard() {
        return false;
    }

    /**
     * @return the aggroRange
     */
    @Override
    public int getAggroRange() {
        return aggroRange;
    }

    /**
     * @param aggroRange the aggroRange to set
     */
    public void setAggroRange(int aggroRange) {
        this.aggroRange = aggroRange;
    }

    /**
     * @return the level
     */
    public byte getLevel() {
        return level;
    }

    /**
     * @param level the level to set
     */
    public void setLevel(byte level) {
        this.level = level;
    }
}
