/*
 * This file is part of aion-nice <aion-nice.com>.
 *
 * aion-nice team is private software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * aion-nice team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with aion-nice team.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.configs.main;

import com.aionemu.commons.configuration.Property;

/**
 * <AUTHOR>
 */
public class DredgionConfig {

    @Property(key = "gameserver.dredgion.timer", defaultValue = "60")
    public static long DREDGION_TIMER;

    @Property(key = "gameserver.dredgion.player", defaultValue = "12")
    public static int DREDGION_PLAYER;

    @Property(key = "gameserver.dredgion.grouplimit", defaultValue = "6")
    public static int DREDGION_GROUPLIMIT;

    @Property(key = "gameserver.dredgion.levellimit", defaultValue = "50")
    public static int DREDGION_LEVELLIMIT;

    @Property(key = "gameserver.dredgion.winreward", defaultValue = "3000")
    public static int DREDGION_WINREWARD;

    @Property(key = "gameserver.dredgion.lostreward", defaultValue = "1500")
    public static int DREDGION_LOSTREWARD;

}
