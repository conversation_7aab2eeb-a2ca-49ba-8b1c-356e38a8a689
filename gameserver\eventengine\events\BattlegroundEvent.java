/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.eventengine.Event;
import gameserver.services.LadderService;
import gameserver.utils.ThreadPoolManager;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 
 */
public class BattlegroundEvent extends Event {
    private List<Integer> battlegrounds = new ArrayList<Integer>();

    @Override
    public void execute() {
        LadderService.getInstance().createNormalBgs(this);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                if (!isFinished())
                    onEnd();
            }
        }, 15 * 60 * 1000);
    }

    public int getBgCount() {
        return battlegrounds.size();
    }

    public void onCreate(Integer bgId) {
        if (!battlegrounds.contains(bgId))
            battlegrounds.add(bgId);
    }

    public void onEnd(Integer bgId) {
        battlegrounds.remove(bgId);
        if (battlegrounds.isEmpty())
            this.onEnd();
    }

    public void onEnd() {
        battlegrounds.clear();
        super.finish();
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#onReset()
     */
    @Override
    protected void onReset() {
        battlegrounds.clear();
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#cancel(boolean)
     */
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return false;
    }
}
