/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.eventengine.events;

import gameserver.eventengine.Event;
import gameserver.utils.ThreadPoolManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class ArtifactEvent extends Event {

    private Integer[] GelkmarosArtifacts = { 3012, 3013, 3022, 3033 };
    private Integer[] InggisonArtifacts = { 2012, 2013, 2022, 2023 };

    private List<Integer> artifacts;
    private String map;

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#cancel(boolean)
     */
    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return false;
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#execute()
     */
    @Override
    protected void execute() {
        super.announceAll("A Capture Points event is starting in 5 minutes!", 0);

        switch (Rnd.get(1, 2)) {
            case 1:
                artifacts = new ArrayList<Integer>(Arrays.asList(InggisonArtifacts));
                map = "Inggison";
                break;
            case 2:
                artifacts = new ArrayList<Integer>(Arrays.asList(GelkmarosArtifacts));
                map = "Gelkmaros";
                break;
        }

        super.announceAll("The event will take place in " + map + " at the Artifacts", 5 * 1000);

        super.announceAll("The event in " + map + " starts in 3 minutes!", 2 * 60 * 1000);
        super.announceAll("The event in " + map + " starts in 1 minute!", 4 * 60 * 1000);
        super.announceAll("The event in " + map + " starts in 30 seconds!", 30 + 4 * 60 * 1000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                startEvent();
            }
        }, 5 * 60 * 1000);
    }

    /*
     * (non-Javadoc)
     * @see gameserver.eventengine.Event#onReset()
     */
    @Override
    protected void onReset() {
        artifacts.clear();
    }

    private void startEvent() {

    }

}
