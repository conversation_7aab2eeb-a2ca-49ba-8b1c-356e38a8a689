/*
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.ai.events.handler;

import gameserver.ai.AI;
import gameserver.ai.events.Event;
import gameserver.ai.npcai.HomingAi;
import gameserver.ai.npcai.ServantAi;
import gameserver.ai.npcai.SkillAreaNpcAi;
import gameserver.ai.npcai.TrapAi;
import gameserver.ai.state.AIState;
import gameserver.model.gameobjects.Kisk;
import gameserver.model.siege.FortressGate;

/**
 * <AUTHOR>
 */
public class AttackedEventHandler implements EventHandler {
    @Override
    public Event getEvent() {
        return Event.ATTACKED;
    }

    @Override
    public void handleEvent(Event event, AI<?> ai) {
        if (ai.getOwner().isDummy() || ai.getOwner() instanceof FortressGate
            || ai instanceof ServantAi || ai instanceof SkillAreaNpcAi || ai instanceof TrapAi
            || ai instanceof HomingAi || ai.getOwner() instanceof Kisk)
            return;

        ai.setAiState(AIState.ATTACKING);
        if (!ai.isScheduled())
            ai.analyzeState();
    }
}
