/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.group;

import gameserver.configs.main.GroupConfig;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_GROUP_INFO;
import gameserver.network.aion.serverpackets.SM_GROUP_MEMBER_INFO;
import gameserver.network.aion.serverpackets.SM_LEAVE_GROUP_MEMBER;
import gameserver.network.aion.serverpackets.SM_SHOW_BRAND;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.LadderService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> Lyahim, Simple
 * <AUTHOR>
 */
public class PlayerGroup extends AionObject {
    private LootGroupRules lootGroupRules = new LootGroupRules();

    private Player groupLeader;

    private Map<Integer, Player> groupMembers = new ConcurrentHashMap<Integer, Player>();

    private int RoundRobinNr = 0;

    private int instancePoints = 0;
    private long instanceStartTime = 0;

    private int killCount = 0;
    private int bgIndex = -1;
    
    private int averageRating = 1000;

    private Map<Integer, Integer> memberBrands = new ConcurrentHashMap<Integer, Integer>();

    private List<Player> observers = new ArrayList<Player>();

    /**
     * Instantiates new player group with unique groupId
     * 
     * @param groupId
     */
    public PlayerGroup(int groupId, Player groupleader) {
        super(groupId);
        this.groupMembers.put(groupleader.getObjectId(), groupleader);
        this.setGroupLeader(groupleader);
        groupleader.setPlayerGroup(this);
        PacketSendUtility.sendPacket(groupLeader, new SM_GROUP_INFO(this));
        updateGroupUIToEvent(groupLeader, GroupEvent.CREATE);
    }

    /**
     * @return the groupId
     */
    public int getGroupId() {
        return this.getObjectId();
    }

    /**
     * @return the groupLeader
     */
    public Player getGroupLeader() {
        return groupLeader;
    }

    /**
     * Used to set group leader
     * 
     * @param groupLeader
     *            the groupLeader to set
     */
    public void setGroupLeader(Player groupLeader) {
        this.groupLeader = groupLeader;
    }

    /**
     * Adds player to group
     * 
     * @param newComer
     */
    public void addPlayerToGroup(Player newComer) {
        groupMembers.put(newComer.getObjectId(), newComer);
        newComer.setPlayerGroup(this);
        PacketSendUtility.sendPacket(newComer, new SM_GROUP_INFO(this));
        updateGroupUIToEvent(newComer, GroupEvent.ENTER);
    }

    /**
     * This method will return a round robin group member.
     * 
     * @param npc
     *            The killed Npc
     * @return memberObjId or 0 if the selected player isn't in range.
     */
    public int getRoundRobinMember(Npc npc) {
        if (size() == 0)
            return 0;

        RoundRobinNr = ++RoundRobinNr % size();
        int i = 0;
        for (Player player : getMembers()) {
            if (i == RoundRobinNr) {
                if (MathUtil.isIn3dRange(player, npc, GroupConfig.GROUP_MAX_DISTANCE)) { // the player is in range of
                    // the killed NPC.
                    return player.getObjectId();
                }
                else {
                    return 0;
                }
            }
            i++;
        }
        return 0;
    }

    /**
     * Removes player from group
     * 
     * @param player
     */
    public void removePlayerFromGroup(Player player) {
        this.groupMembers.remove(player.getObjectId());
        player.setPlayerGroup(null);
        updateGroupUIToEvent(player, GroupEvent.LEAVE);

        PacketSendUtility.sendPacket(player, new SM_LEAVE_GROUP_MEMBER());
    }

    public void disband() {
        this.groupMembers.clear();

        for (Player player : observers)
            PacketSendUtility.sendPacket(player, new SM_LEAVE_GROUP_MEMBER());

        this.observers.clear();
        // World.getInstance().removeObject(this);
    }

    public void onGroupMemberLogIn(Player player) {
        groupMembers.remove(player.getObjectId());
        groupMembers.put(player.getObjectId(), player);
    }

    /**
     * Checks whether group is full
     * 
     * @return true or false
     */
    public boolean isFull() {
        return groupMembers.size() == 6;
    }

    public Collection<Player> getMembers() {
        return groupMembers.values();
    }

    public Collection<Integer> getMemberObjIds() {
        return groupMembers.keySet();
    }

    /**
     * @return count of group members
     */
    public int size() {
        return groupMembers.size();
    }

    /**
     * @return the lootGroupRules
     */
    public LootGroupRules getLootGroupRules() {
        return lootGroupRules;
    }

    public void setLootGroupRules(LootGroupRules lgr) {
        this.lootGroupRules = lgr;
        for (Player member : groupMembers.values())
            PacketSendUtility.sendPacket(member, new SM_GROUP_INFO(this));
    }

    /**
     * Update the Client user interface with the newer data
     */
    // TODO: Move to GroupService
    public void updateGroupUIToEvent(Player subjective, GroupEvent groupEvent) {
        switch (groupEvent) {
            case CREATE: {
                PacketSendUtility.sendPacket(subjective, new SM_GROUP_MEMBER_INFO(this, subjective,
                    groupEvent));
            }
                break;
            case CHANGELEADER: {
                for (Player member : this.getMembers()) {
                    PacketSendUtility.sendPacket(member, new SM_GROUP_INFO(this));
                    if (subjective.equals(member))
                        PacketSendUtility.sendPacket(member,
                            SM_SYSTEM_MESSAGE.CHANGE_GROUP_LEADER());
                    PacketSendUtility.sendPacket(member, new SM_GROUP_MEMBER_INFO(this, subjective,
                        groupEvent));
                }

                for (Player player : this.getObservers()) {
                    PacketSendUtility.sendPacket(player, new SM_GROUP_INFO(this));
                    PacketSendUtility.sendPacket(player, new SM_GROUP_MEMBER_INFO(this, subjective,
                        groupEvent));
                }
            }
                break;
            case LEAVE: {
                boolean changeleader = false;
                if (subjective == this.getGroupLeader() && this.getMembers().size() > 0) {
                    this.setGroupLeader(this.getMembers().iterator().next());
                    changeleader = true;
                }

                for (Player member : this.getMembers()) {
                    if (changeleader) {
                        PacketSendUtility.sendPacket(member, new SM_GROUP_INFO(this));
                        PacketSendUtility.sendPacket(member,
                            SM_SYSTEM_MESSAGE.CHANGE_GROUP_LEADER());
                    }

                    PacketSendUtility.sendPacket(member, new SM_GROUP_MEMBER_INFO(this, subjective,
                        groupEvent));

                    if (this.size() > 1)
                        PacketSendUtility.sendPacket(member,
                            SM_SYSTEM_MESSAGE.MEMBER_LEFT_GROUP(subjective.getName()));
                }

                for (Player player : this.getObservers()) {
                    if (changeleader) {
                        PacketSendUtility.sendPacket(player, new SM_GROUP_INFO(this));
                        PacketSendUtility.sendPacket(player,
                            SM_SYSTEM_MESSAGE.CHANGE_GROUP_LEADER());
                    }

                    PacketSendUtility.sendPacket(player, new SM_GROUP_MEMBER_INFO(this, subjective,
                        groupEvent));

                    if (this.size() > 1)
                        PacketSendUtility.sendPacket(player,
                            SM_SYSTEM_MESSAGE.MEMBER_LEFT_GROUP(subjective.getName()));
                }

                eventToSubjective(subjective, GroupEvent.LEAVE);
            }
                break;
            case ENTER: {
                eventToSubjective(subjective, GroupEvent.ENTER);
                for (Player member : this.getMembers()) {
                    PacketSendUtility.sendPacket(member, new SM_GROUP_MEMBER_INFO(this, subjective,
                        groupEvent));

                    if (getMemberBrands().get(member.getObjectId()) != null)
                        PacketSendUtility.sendPacket(subjective, new SM_SHOW_BRAND(
                            getMemberBrands().get(member.getObjectId()), member.getObjectId()));
                }

                for (Player player : this.getObservers()) {
                    PacketSendUtility.sendPacket(player, new SM_GROUP_MEMBER_INFO(this, subjective,
                        groupEvent));
                }
            }
                break;
            default: {
                for (Player member : this.getMembers()) {
                    PacketSendUtility.sendPacket(member, new SM_GROUP_MEMBER_INFO(this, subjective,
                        groupEvent));
                }
                
                for (Player player : this.getObservers()) {
                    PacketSendUtility.sendPacket(player, new SM_GROUP_MEMBER_INFO(this, subjective,
                        groupEvent));
                }
            }
                break;
        }
    }

    public void eventToSubjective(Player subjective, GroupEvent groupEvent) {
        for (Player member : getMembers()) {
            if (!subjective.equals(member))
                PacketSendUtility.sendPacket(subjective, new SM_GROUP_MEMBER_INFO(this, member,
                    groupEvent));
        }
        if (groupEvent == GroupEvent.LEAVE)
            PacketSendUtility.sendPacket(subjective, SM_SYSTEM_MESSAGE.YOU_LEFT_GROUP());
    }

    public void setGroupInstancePoints(int points) {
        instancePoints = points;
    }

    public int getGroupInstancePoints() {
        return instancePoints;
    }

    public void setInstanceStartTimeNow() {
        instanceStartTime = System.currentTimeMillis();
    }

    public long getInstanceStartTime() {
        return instanceStartTime;
    }

    @Override
    public String getName() {
        return "Player Group";
    }

    /**
     * @param killCount
     *            the killCount to set
     */
    public void setKillCount(int killCount) {
        this.killCount = killCount;
    }

    /**
     * @return the killCount
     */
    public int getKillCount() {
        return killCount;
    }

    /**
     * @param bgIndex
     *            the bgIndex to set
     */
    public void setBgIndex(int bgIndex) {
        this.bgIndex = bgIndex;
    }

    /**
     * @return the bgIndex
     */
    public int getBgIndex() {
        return bgIndex;
    }

    /**
     * @return the memberBrands
     */
    public Map<Integer, Integer> getMemberBrands() {
        return memberBrands;
    }

    public void addObserver(Player player) {
        observers.add(player);

        PacketSendUtility.sendPacket(player, new SM_GROUP_INFO(this));
        
        eventToSubjective(player, GroupEvent.ENTER);
    }

    public void removeObserver(Player player) {
        observers.remove(player);
        
        PacketSendUtility.sendPacket(player, new SM_LEAVE_GROUP_MEMBER());
    }

    public List<Player> getObservers() {
        return observers;
    }
    
    public int computeAverageRating() {
        int rating = 0;
        
        for (Player pl : groupMembers.values())
            rating += LadderService.getInstance().getCachedRating(pl);
        
        rating = rating / Math.max(size(), 1);
        
        this.averageRating = rating;
        
        return averageRating;
    }
    
    public int getAverageRating() {
        return averageRating;
    }
}
