/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.slipperyslope;

import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.utils.ThreadPoolManager;

/**
 * <AUTHOR>
 */
public class AetherBombController extends BossController {
    private final BossSkill EXPLODE = new BossSkill(20617, 1);

    public AetherBombController() {
        super(700641, true);
    }

    @Override
    protected void think() {
        final Npc owner = getOwner();
        
        if (owner.isCasting())
            return;
        
        queueSkill(EXPLODE, owner);
        
        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                Npc add = spawnAdd(282979, null, owner.getX(), owner.getY(), owner.getZ(), true);
                add.setAi(null);
            }
        }, 500);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                for (VisibleObject add : adds)
                    add.getController().delete();
                
                owner.getController().onDelete();
            }
        }, EXPLODE.getSkillTemplate().getDuration() + 100);
    }
}