/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.network.aion.clientpackets;

import gameserver.controllers.MoveController;
import gameserver.controllers.attack.AttackStatus;
import gameserver.controllers.movement.MovementType;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.state.CreatureState;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.network.aion.AionClientPacket;
import gameserver.network.aion.serverpackets.SM_MOVE;
import gameserver.services.MountService;
import gameserver.skillengine.effect.EffectId;
import gameserver.skillengine.model.Skill;
import gameserver.taskmanager.tasks.GroupUpdater;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.World;

import org.apache.log4j.Logger;

/**
 * Packet about player movement.
 * 
 * <AUTHOR>
 */
public class CM_MOVE extends AionClientPacket {
    /**
     * logger for this class
     */
    private static final Logger log = Logger.getLogger(CM_MOVE.class);

    private MovementType type;

    private byte heading;

    private byte movementType;

    private float x, y, z, x2, y2, z2;

    private byte glideFlag, glideFlag2;

    private boolean mountSprinting = false;
    private boolean geyser = false;

    /**
     * Constructs new instance of <tt>CM_MOVE </tt> packet
     * 
     * @param opcode
     */
    public CM_MOVE(int opcode) {
        super(opcode);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected void readImpl() {
        Player player = getConnection().getActivePlayer();

        if (!player.isSpawned())
            return;

        x = readF();
        y = readF();
        z = readF();

        heading = (byte) readC();
        movementType = (byte) readC();

        // Remove Mount sprinting 0x01 bit
        if ((movementType & 0x01) >= 0x01) {
            mountSprinting = true;
            movementType = (byte) (movementType ^ 0x01);
        }

        // Remove Geyser 0x02 bit
        if ((movementType & 0x02) >= 0x02) {
            geyser = true;
            movementType = (byte) (movementType ^ 0x02);
        }

        type = MovementType.getMovementTypeById(movementType);

        switch (type) {
            case MOVEMENT_START_MOUSE:
            case MOVEMENT_START_KEYBOARD:
                x2 = readF();
                y2 = readF();
                z2 = readF();
                break;
            case MOVEMENT_GLIDE_DOWN:
            case MOVEMENT_GLIDE_START_MOUSE:
                x2 = readF();
                y2 = readF();
                z2 = readF();
                // no break
            case MOVEMENT_GLIDE_UP:
            case VALIDATE_GLIDE_MOUSE:
                glideFlag = (byte) readC();
                if (getRemainingBytes() > 0)
                    glideFlag2 = (byte) readC();
                break;
            default:
                break;
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    protected void runImpl() {
        Player player = getConnection().getActivePlayer();
        World world = World.getInstance();
        // packet was not read correctly
        if (type == null || player == null || player.getLifeStats().isAlreadyDead())
            return;

        player.getMoveController().setMountSprinting(mountSprinting);
        player.getMoveController().setInGeyser(geyser);

        World.getInstance().updateHeading(player, heading);

        /*
         * if (player.getSkillAllowMove() > System.currentTimeMillis()) { PacketSendUtility.sendMessage(player,
         * "MOVEMENT DURING ANIMATION! YIKES! " + type.name()); }
         */

        if (player.isMovementBlocked())
            return;

        // if (!player.canPerformMove()
        // && player.getEffectController().getAbnormals() != EffectId.ROOT.getEffectId())
        // return;

        // if (!player.canPerformMove() && (movementType & 0x08) == 0
        // && type != MovementType.MOVEMENT_STOP)
        // return;

        if (!player.canPerformMove()
            && type != MovementType.MOVEMENT_STOP
            && !(player.getEffectController().isAbnormalSet(EffectId.ROOT)
                || player.getEffectController().isAbnormalSet(EffectId.PARALYZE)
                || player.getEffectController().isAbnormalSet(EffectId.SLEEP)
                || player.getEffectController().isAbnormalSet(EffectId.SPIN) || player
                .getEffectController().isAbnormalSet(EffectId.STUN)))
            return;

        if (player.getMoveController().isBeingForcedMove()) {
            player.getMoveController().correctPosition();
            return;
        }

        /*
         * PacketSendUtility.sendMessage(player, String.format(
         * "Move: (%.02f, %.02f, %.02f) -> (%.02f, %.02f, %.02f) %s", player.getMoveController() .getOriginX(),
         * player.getMoveController().getOriginY(), player.getMoveController() .getOriginZ(), x, y, z, type));
         */
        /*
         * if (GeoEngine2.getInstance().getLowestZ(player.getWorldId(), x, y) > 2F + z || (player.getBattleground() !=
         * null && glideFlag2 != 0)) { player.getMoveController().stop(); World.getInstance().updatePosition(player,
         * player.getMoveController().getOriginX(), player.getMoveController().getOriginY(),
         * player.getMoveController().getOriginZ() + 2F, heading); PacketSendUtility.broadcastPacketAndReceive(player,
         * new SM_MOVE(player, player.getX(), player.getY(), player.getZ(), heading, MovementType.MOVEMENT_STOP));
         * return; }
         */

        if (player.getBattleground() != null && !player.isSpectating() && glideFlag2 != 0) {
            player.getMoveController().stop();
            World.getInstance().updatePosition(player, player.getMoveController().getOriginX(),
                player.getMoveController().getOriginY(), player.getMoveController().getOriginZ(),
                heading);
            PacketSendUtility.broadcastPacketAndReceive(player, new SM_MOVE(player, player.getX(),
                player.getY(), player.getZ(), heading, MovementType.MOVEMENT_STOP));
            return;
        }

        switch (type) {
            case MOVEMENT_GLIDE_DOWN:
            case MOVEMENT_GLIDE_UP:
            case MOVEMENT_GLIDE_START_MOUSE:
            case VALIDATE_GLIDE_MOUSE:
                player.getFlyController().switchToGliding();
                break;
            default:
                player.getFlyController().onStopGliding();
                break;
        }

        if (player.isInState(CreatureState.GLIDING)
            && (player.getEffectController().isAbnormalSet(EffectId.SLEEP)
                || player.getEffectController().isAbnormalSet(EffectId.ROOT)
                || player.getEffectController().isAbnormalSet(EffectId.STUN)
                || player.getEffectController().isAbnormalSet(EffectId.SPIN) || player
                .getEffectController().isAbnormalSet(EffectId.PARALYZE))) {
            player.getMoveController().stop();
            player.getFlyController().onStopGliding();
            World.getInstance().updatePosition(player, player.getMoveController().getOriginX(),
                player.getMoveController().getOriginY(), player.getMoveController().getOriginZ(),
                heading);
            PacketSendUtility.broadcastPacketAndReceive(player, new SM_MOVE(player, player.getX(),
                player.getY(), player.getZ(), heading, MovementType.MOVEMENT_STOP));
            return;
        }

        if (Float.isNaN(player.getSpeedOverride())
            && /*
                * type != MovementType.VALIDATE_JUMP && type != MovementType.VALIDATE_JUMP_WHILE_MOVING &&
                */!player.getMoveController().validateNewPosition(type, x, y, z, x2, y2, z2,
                heading, glideFlag, glideFlag2)) {
            /*
             * if (player.isInState(CreatureState.GLIDING)) { PacketSendUtility.sendPacket(player, new SM_MOVE(player,
             * player.getMoveController() .getOriginX(), player.getMoveController().getOriginY(), player
             * .getMoveController().getOriginZ(), 0, 0, 0, heading, (byte) 0, (byte) 0,
             * MovementType.MOVEMENT_GLIDE_START_MOUSE)); } else { PacketSendUtility.sendPacket(player, new
             * SM_MOVE(player, player.getMoveController() .getOriginX(), player.getMoveController().getOriginY(), player
             * .getMoveController().getOriginZ(), heading, MovementType.MOVEMENT_STOP)); }
             */
            player.getMoveController().correctPosition();

            /*
             * PacketSendUtility.sendMessage(player,
             * String.format("[CORRECT] %.01f, %.01f, %.01f, (%.01f, %.01f, %.01f) - %02X", x, y, z, x2, y2, z2,
             * movementType));
             */
            return;
        }

        if (Float.isNaN(player.getSpeedOverride())
            && !GeoEngine2.getInstance().canSee(player.getWorldId(),
                player.getMoveController().getOriginX(), player.getMoveController().getOriginY(),
                player.getMoveController().getOriginZ(), x, y, z)) {
            // if (player.getAccessLevel() > 0)
            // PacketSendUtility.sendMessage(player, String.format(
            // "[DBG] %.02f, %.02f, %.02f (%.02f, %.02f, %.02f)", x, y, z, player
            // .getMoveController().getOriginX(), player.getMoveController().getOriginY(),
            // player.getMoveController().getOriginZ()));
            player.getMoveController().correctPosition();
            return;
        }

        player.getController().cancelCurrentAction();

        switch (type) {
            case MOVEMENT_START_MOUSE:
            case MOVEMENT_START_KEYBOARD:
                if (!player.canPerformMove())
                    break;

                // if (player.getFlyState() == 0 && z2 > 0) {
                // x2 *= 0.8f;
                // y2 *= 0.8f;
                // z2 *= 0.6f;
                // }

                player.getMoveController().setNewDirection(x2, y2, z2);
            case MOVEMENT_MOVIN_ELEVATOR:
            case MOVEMENT_ON_ELEVATOR:
            case MOVEMENT_STAYIN_ELEVATOR:
                world.updatePosition(player, x, y, z, heading);
                player.getController().onMove();
                player.getMoveController().stopFalling();

                PacketSendUtility.broadcastPacket(player, new SM_MOVE(player, x, y, z, x2, y2, z2,
                    heading, type), false);
                break;
            case MOVEMENT_GLIDE_START_MOUSE:
                player.getMoveController().setNewDirection(x2, y2, z2);
                // no break
            case MOVEMENT_GLIDE_DOWN:
                world.updatePosition(player, x, y, z, heading);
                player.getController().onMove();
                PacketSendUtility.broadcastPacket(player, new SM_MOVE(player, x, y, z, x2, y2, z2,
                    heading, glideFlag, glideFlag2, type), false);
                break;
            case MOVEMENT_GLIDE_UP:
                world.updatePosition(player, x, y, z, heading);
                player.getController().onMove();
                PacketSendUtility.broadcastPacket(player, new SM_MOVE(player, x, y, z, heading,
                    glideFlag, glideFlag2, type), false);
                break;
            case VALIDATE_GLIDE_MOUSE:
                world.updatePosition(player, x, y, z, heading);
                player.getController().onMove();

                /**
                 * Broadcast a fake packet to trick the client
                 */
                // TODO: glideSpeed?
                float glideSpeed = player.getGameStats().getCurrentStat(StatEnum.SPEED);
                double angle = Math.toRadians(heading * 3);
                x2 = (float) (glideSpeed * Math.cos(angle));
                y2 = (float) (glideSpeed * Math.sin(angle));

                PacketSendUtility.broadcastPacket(player, new SM_MOVE(player, x, y, z, x2, y2, z2,
                    heading, glideFlag, glideFlag2, MovementType.MOVEMENT_GLIDE_DOWN), false);
                break;
            case VALIDATE_JUMP_WHILE_MOVING:
            case VALIDATE_JUMP:
                world.updatePosition(player, x, y, z, heading);
                break;
            case VALIDATE_MOUSE:
            case VALIDATE_KEYBOARD:
                player.getController().onMove();
                world.updatePosition(player, x, y, z, heading);

                if (!player.canPerformMove())
                    break;

                MoveController mc = player.getMoveController();
                PacketSendUtility.broadcastPacket(player,
                    new SM_MOVE(player, x, y, z, mc.getTargetX(), mc.getTargetY(), mc.getTargetZ(),
                        heading,
                        (type == MovementType.VALIDATE_MOUSE) ? MovementType.MOVEMENT_START_MOUSE
                            : MovementType.MOVEMENT_START_KEYBOARD), false);
                break;
            case MOVEMENT_STOP:
                /*
                 * if (player.getMoveController().getMoveType() == MovementType.MOVEMENT_START_KEYBOARD ||
                 * player.getMoveController().getMoveType() == MovementType.MOVEMENT_START_MOUSE) {
                 * PacketSendUtility.broadcastPacket(player, new SM_MOVE(player, x, y, z, 0, 0, 0, heading,
                 * MovementType.MOVEMENT_START_KEYBOARD), false); } else {
                 */
                PacketSendUtility.broadcastPacket(player, new SM_MOVE(player, x, y, z, heading,
                    type), false);
                // }

                world.updatePosition(player, x, y, z, heading);
                player.getController().onStopMove();
                break;
            case UNKNOWN:
                StringBuilder sb = new StringBuilder();
                sb.append("Unknown movement type: ").append(movementType);
                sb.append("Coordinates: X=").append(x);
                sb.append(" Y=").append(y);
                sb.append(" Z=").append(z);
                sb.append(" player=").append(player.getName());
                log.warn(sb.toString());
                break;
            default:
                break;
        }

        player.getMoveController().logLastMove(type, x, y, z);
        player.getMoveController().updateMoveDirection(type, x2, y2, heading);

        /*
         * if (type == MovementType.VALIDATE_KEYBOARD || type == MovementType.VALIDATE_JUMP || type ==
         * MovementType.VALIDATE_JUMP_WHILE_MOVING)
         * player.getMoveController().setMoveType(MovementType.MOVEMENT_START_KEYBOARD); else if (type ==
         * MovementType.VALIDATE_MOUSE) player.getMoveController().setMoveType(MovementType.MOVEMENT_START_MOUSE); else
         * player.getMoveController().setMoveType(type);
         */

        switch (type) {
            case MOVEMENT_START_KEYBOARD:
            case MOVEMENT_START_MOUSE:
                player.getMoveController().startMovement(type, x, y, z, x2, y2, z2, heading);
                break;
            case MOVEMENT_GLIDE_UP:
            case VALIDATE_GLIDE_MOUSE:
                player.getMoveController().stop();
                player.getMoveController().setPosition(x, y, z);
                break;
            case MOVEMENT_GLIDE_DOWN:
            case MOVEMENT_GLIDE_START_MOUSE:
            case MOVEMENT_STOP:
                player.getMoveController().stop();
            default:
                player.getMoveController().setPosition(x, y, z, x2, y2, z2);
        }

        player.setLastAction();

        if (player.isInGroup() || player.isInAlliance()) {
            GroupUpdater.getInstance().startTask(player);
        }

        if (player.getMoveController().getOriginZ() < -200) {
            player.getController().onStopMove();
            player.getFlyController().onStopGliding();
            player.getController().onAttack(player, player.getLifeStats().getMaxHp() + 1,
                AttackStatus.NORMALHIT, true);
            return;
        }

        // Fall Damage
        if ((type.getMovementTypeId() & 0x08) == 0x08)
            player.getMoveController().updateFalling(z);
        else
            player.getMoveController().stopFalling(z);

        if (type != MovementType.MOVEMENT_STOP && player.isProtectionActive()) {
            player.getController().stopProtectionActiveTask();
        }
    }
}
