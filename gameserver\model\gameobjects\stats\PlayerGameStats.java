/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.model.gameobjects.stats;

import gameserver.configs.main.CustomConfig;
import gameserver.dataholders.PlayerStatsData;
import gameserver.model.EmotionType;
import gameserver.model.gameobjects.Item;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.modifiers.CheckWeapon;
import gameserver.model.gameobjects.stats.modifiers.SimpleModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.templates.stats.PlayerStatsTemplate;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.network.aion.serverpackets.SM_STATS_INFO;
import gameserver.taskmanager.tasks.StatUpdater;
import gameserver.utils.PacketSendUtility;

/**
 * <AUTHOR>
 */
public class PlayerGameStats extends CreatureGameStats<Player> {
    private int currentRunSpeed = 0;
    private int currentFlySpeed = 0;
    private int currentAttackSpeed = 0;
    private int currentBaseAttackSpeed = 0;

    /**
     * @param playerStatsData
     * @param owner
     */
    public PlayerGameStats(PlayerStatsData playerStatsData, final Player owner) {
        super(owner);
        PlayerStatsTemplate pst = playerStatsData.getTemplate(owner.getPlayerClass(),
            owner.getLevel());
        initStats(pst, owner);
    }

    @Override
    public void recomputeStats() {
        if (owner == null || owner.getEquipment() == null)// || !owner.isOnline())
            return;

        super.recomputeStats();

        addExtraAttackRange();
        applyLimits();
        applyCustomNerfs();
        applyOldSchoolChanges();

        if (owner.getBattleground() != null)
            owner.getBattleground().onStatRecomputation(owner);

        if (!Float.isNaN(owner.getSpeedOverride()) && owner.getSpeedOverride() > 0f) {
            this.setStat(StatEnum.SPEED, (int) (owner.getSpeedOverride() * 1000), false);
            this.setStat(StatEnum.SPEED, 0, true);

            this.setStat(StatEnum.FLY_SPEED, (int) (owner.getSpeedOverride() * 1000), false);
            this.setStat(StatEnum.FLY_SPEED, 0, true);
        }

        if (owner.isSpawned())
            owner.getLifeStats().updateCurrentStats();

        int newRunSpeed = getCurrentStat(StatEnum.SPEED);
        int newFlySpeed = getCurrentStat(StatEnum.FLY_SPEED);
        int newAttackSpeed = getCurrentStat(StatEnum.ATTACK_SPEED);
        int newBaseAttackSpeed = getBaseStat(StatEnum.ATTACK_SPEED);

        if (newRunSpeed != currentRunSpeed || currentFlySpeed != newFlySpeed
            || newAttackSpeed != currentAttackSpeed || newBaseAttackSpeed != currentBaseAttackSpeed) {
            // updateVisualSpeed();
            updateSpeed();
        }

        // updateVisualStatOnly();
        updateStats();

        this.currentRunSpeed = newRunSpeed;
        this.currentFlySpeed = newFlySpeed;
        this.currentAttackSpeed = newAttackSpeed;
        this.currentBaseAttackSpeed = newBaseAttackSpeed;

        if (owner.getSummon() != null) {
            StatUpdater.getInstance().startTask(owner.getSummon());
            // owner.getSummon().getGameStats().recomputeStats();
        }
    }

    protected void applyOldSchoolChanges() {
        if (!CustomConfig.OLD_SCHOOL)
            return;

        // set Magic Suppression to 0
        // this.setStat(StatEnum.BOOST_MAGICAL_SKILL_RESIST,
        // -this.getBaseStat(StatEnum.BOOST_MAGICAL_SKILL_RESIST), true);

        this.setStat(StatEnum.BOOST_MAGICAL_SKILL_RESIST, 0, false);
        this.setStat(StatEnum.BOOST_MAGICAL_SKILL_RESIST, 0, true);

        // set Magic Defense to 0
        // this.setStat(StatEnum.MAGICAL_DEFENSE, -this.getBaseStat(StatEnum.MAGICAL_DEFENSE), true);

        this.setStat(StatEnum.MAGICAL_DEFENSE, 0, false);
        this.setStat(StatEnum.MAGICAL_DEFENSE, 0, true);
    }

    protected void applyLimits() {
        int MIN_ATTACK_SPEED = CheckWeapon.getInstance().getValue(owner);
        int MIN_SPEED = 600;
        int MAX_SPEED = 12000;
        int MIN_FLY_SPEED = 600;
        int MAX_FLY_SPEED = 16000;

        int bonus = 0;

        if (getCurrentStat(StatEnum.ATTACK_SPEED) < MIN_ATTACK_SPEED) {
            bonus = this.getBaseStat(StatEnum.ATTACK_SPEED) - MIN_ATTACK_SPEED;
            this.setStat(StatEnum.ATTACK_SPEED, -bonus, true);
            bonus = 0;
        }

        // speed limit
        if (getCurrentStat(StatEnum.SPEED) < MIN_SPEED) {
            bonus = this.getBaseStat(StatEnum.SPEED) - MIN_SPEED;
            this.setStat(StatEnum.SPEED, -bonus, true);
            bonus = 0;
        }
        else if (getCurrentStat(StatEnum.SPEED) > MAX_SPEED) {
            bonus = MAX_SPEED - this.getBaseStat(StatEnum.SPEED);
            this.setStat(StatEnum.SPEED, bonus, true);
            bonus = 0;
        }

        // fly speed limit
        if (getCurrentStat(StatEnum.FLY_SPEED) < MIN_FLY_SPEED) {
            bonus = this.getBaseStat(StatEnum.FLY_SPEED) - MIN_FLY_SPEED;
            this.setStat(StatEnum.FLY_SPEED, -bonus, true);
            bonus = 0;
        }
        else if (getCurrentStat(StatEnum.FLY_SPEED) > MAX_FLY_SPEED) {
            bonus = MAX_FLY_SPEED - this.getBaseStat(StatEnum.FLY_SPEED);
            this.setStat(StatEnum.FLY_SPEED, bonus, true);
            bonus = 0;
        }
    }

    protected void applyCustomNerfs() {
        switch (owner.getPlayerClass()) {
            case CLERIC:
            case CHANTER:
                // case BARD:
                // If no Heal Boost stat present, don't apply any nerfs
                if (!super.stats.containsKey(StatEnum.BOOST_HEAL))
                    return;

                break;
            default:
                return;
        }

        // Don't apply to PvE instances
        if (owner.getBattleground() != null && owner.getBattleground().isPvE())
            return;

        final int BLOCK_THRESHOLD = 3400;
        final float BLOCK_TRADEOFF = 0.6f;

        final int MR_THRESHOLD = 2400;
        final float MR_TRADEOFF = 0.8f;

        int statDiff;

        // BLOCK TRADEOFF
        statDiff = BLOCK_THRESHOLD - getCurrentStat(StatEnum.BLOCK);

        if (statDiff < 0)
            super.stats.get(StatEnum.BOOST_HEAL).increase((int) (statDiff * BLOCK_TRADEOFF), true);

        // MR TRADEOFF
        statDiff = MR_THRESHOLD - getCurrentStat(StatEnum.MAGICAL_RESIST);

        if (statDiff < 0)
            super.stats.get(StatEnum.BOOST_HEAL).increase((int) (statDiff * MR_TRADEOFF), true);
    }

    protected void addExtraAttackRange() {
        if (owner == null || owner.getEquipment() == null)
            return;

        Item mainHand = owner.getEquipment().getMainHandWeapon();
        Item offHand = owner.getEquipment().getOffHandWeapon();
        if (mainHand == null || offHand == null
            || offHand.getItemTemplate().getWeaponType() == null)
            return; // only get bonus from dual wielding

        int mainHandRange = 0;
        int offHandRange = 0;

        try {
            for (StatModifier sm : mainHand.getItemTemplate().getModifiers()) {
                if (sm instanceof SimpleModifier && sm.getStat() == StatEnum.ATTACK_RANGE) {
                    mainHandRange = ((SimpleModifier) sm).getValue();
                    break;
                }
            }

            for (StatModifier sm : offHand.getItemTemplate().getModifiers()) {
                if (sm instanceof SimpleModifier && sm.getStat() == StatEnum.ATTACK_RANGE) {
                    offHandRange = ((SimpleModifier) sm).getValue();
                    break;
                }
            }
        }
        catch (Exception e) {
        }

        setStat(StatEnum.ATTACK_RANGE, Math.min(mainHandRange, offHandRange));
    }

    /**
     * @param pst
     * @param level
     */
    private void initStats(PlayerStatsTemplate pst, Player player) {
        this.initStats(player, pst.getMaxHp(), pst.getMaxMp(), pst.getPower(), pst.getHealth(),
            pst.getAgility(), pst.getAccuracy(), pst.getKnowledge(), pst.getWill(),
            pst.getMainHandAttack(), pst.getMainHandCritRate(),
            Math.round(pst.getAttackSpeed() * 1000), 1500, Math.round(pst.getRunSpeed() * 1000),
            Math.round(pst.getFlySpeed() * 1000), pst.getBoostHeal());
        setAttackCounter(1);
        initStat(StatEnum.PARRY, pst.getParry());
        initStat(StatEnum.BLOCK, pst.getBlock());
        initStat(StatEnum.EVASION, pst.getEvasion());
        initStat(StatEnum.MAGICAL_ACCURACY, pst.getMagicAccuracy());
        initStat(StatEnum.MAIN_HAND_ACCURACY, pst.getMainHandAccuracy());
        initStat(StatEnum.FLY_TIME, CustomConfig.BASE_FLYTIME);
        initStat(StatEnum.REGEN_HP, player.getLevel() + 3);
        initStat(StatEnum.REGEN_MP, player.getLevel() + 8);
        initStat(StatEnum.MAXDP, 4000);
        initStat(StatEnum.BOOST_HUNTING_XP_RATE, 100);
        initStat(StatEnum.BOOST_GROUP_HUNTING_XP_RATE, 100);
        initStat(StatEnum.BOOST_QUEST_XP_RATE, 100);
        initStat(StatEnum.BOOST_CRAFTING_XP_RATE, 100);
        initStat(StatEnum.BOOST_GATHERING_XP_RATE, 100);
        initStat(StatEnum.CONCENTRATION, 50);
    }

    /**
     * @param maxHp
     * @param maxMp
     * @param power
     * @param health
     * @param agility
     * @param accuracy
     * @param knowledge
     * @param will
     * @param mainHandAttack
     * @param mainHandCritRate
     * @param attackSpeed
     * @param attackRange
     * @param runSpeed
     * @param flySpeed
     */
    protected void initStats(Player player, int maxHp, int maxMp, int power, int health,
        int agility, int accuracy, int knowledge, int will, int mainHandAttack,
        int mainHandCritRate, int attackSpeed, int attackRange, int runSpeed, int flySpeed,
        int boostHeal) {
        stats.clear();
        initStat(StatEnum.MAXHP, maxHp);
        initStat(StatEnum.MAXMP, maxMp);
        initStat(StatEnum.POWER, power);
        initStat(StatEnum.ACCURACY, accuracy);
        initStat(StatEnum.HEALTH, health);
        initStat(StatEnum.AGILITY, agility);
        initStat(StatEnum.KNOWLEDGE, knowledge);
        initStat(StatEnum.WILL, will);
        initStat(StatEnum.MAIN_HAND_POWER, Math.round(18 * (power * 0.01f)));
        initStat(StatEnum.MAIN_HAND_CRITICAL, mainHandCritRate);
        initStat(StatEnum.OFF_HAND_POWER, 0);
        initStat(StatEnum.OFF_HAND_CRITICAL, 0);
        initStat(StatEnum.ATTACK_SPEED, attackSpeed);
        initStat(StatEnum.MAIN_HAND_ATTACK_SPEED, attackSpeed);
        initStat(StatEnum.OFF_HAND_ATTACK_SPEED, 0);
        initStat(StatEnum.ATTACK_RANGE, attackRange);
        initStat(StatEnum.PHYSICAL_DEFENSE, 0);
        initStat(StatEnum.PARRY, Math.round(agility * 3.1f - 248.5f + 12.4f * owner.getLevel()));
        initStat(StatEnum.EVASION, Math.round(agility * 3.1f - 248.5f + 12.4f * owner.getLevel()));
        initStat(StatEnum.BLOCK, Math.round(agility * 3.1f - 248.5f + 12.4f * owner.getLevel()));
        initStat(StatEnum.DAMAGE_REDUCE, 0);
        initStat(StatEnum.MAIN_HAND_ACCURACY,
            Math.round((accuracy * 2 - 10) + 8 * owner.getLevel()));
        initStat(StatEnum.OFF_HAND_ACCURACY, Math.round((accuracy * 2 - 10) + 8 * owner.getLevel()));
        initStat(StatEnum.MAGICAL_RESIST, 0);
        initStat(StatEnum.WIND_RESISTANCE, 0);
        initStat(StatEnum.FIRE_RESISTANCE, 0);
        initStat(StatEnum.WATER_RESISTANCE, 0);
        initStat(StatEnum.EARTH_RESISTANCE, 0);
        initStat(StatEnum.MAGICAL_ACCURACY, Math.round(14.26f * owner.getLevel()));
        initStat(StatEnum.BOOST_MAGICAL_SKILL, 0);
        initStat(StatEnum.SPEED, runSpeed);
        initStat(StatEnum.FLY_SPEED, flySpeed);
        initStat(StatEnum.PVP_ATTACK_RATIO, 0);
        initStat(StatEnum.PVP_DEFEND_RATIO, 0);
        initStat(StatEnum.PVP_ATTACK_RATIO_MAGICAL, 0);
        initStat(StatEnum.PVP_ATTACK_RATIO_PHYSICAL, 0);
        initStat(StatEnum.PVP_DEFEND_RATIO_MAGICAL, 0);
        initStat(StatEnum.PVP_DEFEND_RATIO_PHYSICAL, 0);
        initStat(StatEnum.BOOST_CASTING_TIME, 100);
        initStat(StatEnum.BOOST_HATE, 1000);
        initStat(StatEnum.BOOST_HEAL, boostHeal);
        initStat(StatEnum.MAGICAL_CRITICAL, 50);

        initStat(StatEnum.PHYSICAL_CRITICAL_RESIST, Math.max(owner.getLevel() - 50, 0) * 6);

        switch (player.getPlayerClass()) {
            case CLERIC:
            case SORCERER:
            case SPIRIT_MASTER:
            case BARD:
                initStat(StatEnum.MAGICAL_CRITICAL_RESIST, 50);
                break;
            default:
                initStat(StatEnum.MAGICAL_CRITICAL_RESIST, 0);
                break;
        }
    }

    /**
     * @param playerStatsData
     * @param level
     */
    public void doLevelUpgrade() {
        initStats(owner.getPlayerStatsTemplate(), owner);
        StatUpdater.getInstance().startTask(owner);// recomputeStats();
    }

    @Override
    public void updateStats() {
        PacketSendUtility.sendPacket(owner, new SM_STATS_INFO(owner));
    }
}
